#!/usr/bin/env python3
"""
测试auth_service的vip_level修复
"""
import requests
import json


def test_crazygames_login():
    """测试CrazyGames登录是否修复"""
    print("测试CrazyGames登录修复...")
    
    # 模拟CrazyGames登录请求
    url = "http://localhost:8000/api/v1/auth/crazygames-login"
    
    # 测试数据（模拟CrazyGames的请求）
    test_data = {
        "token": "test_token_123",
        "user_data": {
            "userId": "test_user_123",
            "username": "TestUser",
            "profilePictureUrl": "https://example.com/avatar.jpg"
        }
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=5)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ CrazyGames登录成功！vip_level错误已修复")
            result = response.json()
            print(f"返回数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        elif response.status_code == 400:
            error_data = response.json()
            if "vip_level" in str(error_data):
                print("✗ vip_level错误仍然存在")
                print(f"错误信息: {error_data}")
            else:
                print("✓ vip_level错误已修复，但可能有其他验证问题")
                print(f"错误信息: {error_data}")
        else:
            print(f"服务器返回状态码: {response.status_code}")
            try:
                print(f"错误信息: {response.json()}")
            except:
                print(f"响应内容: {response.text}")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务")
        print("请确保后端服务正在运行: cd backend && python -m uvicorn app.main:app --reload")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")


def test_health_check():
    """测试健康检查"""
    print("\n测试健康检查...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✓ 后端服务运行正常")
            return True
        else:
            print(f"✗ 健康检查失败，状态码: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 后端服务未启动")
        return False
    except Exception as e:
        print(f"❌ 健康检查错误: {e}")
        return False


def main():
    """主函数"""
    print("《千亿像素城市寻宝》auth_service修复测试")
    print("=" * 50)
    
    # 先检查服务是否运行
    if not test_health_check():
        print("\n请先启动后端服务:")
        print("cd backend && python -m uvicorn app.main:app --reload")
        return
    
    # 测试CrazyGames登录
    test_crazygames_login()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("如果看到 '✓ CrazyGames登录成功' 或 '✓ vip_level错误已修复'，说明问题已解决")


if __name__ == "__main__":
    main()