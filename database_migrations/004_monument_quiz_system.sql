-- ====================================
-- 古迹问答系统表
-- ====================================

-- 1. 古迹配置表
CREATE TABLE monuments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    monument_name VARCHAR(100) NOT NULL COMMENT '古迹名称',
    monument_name_en VARCHAR(100) COMMENT '古迹英文名称',
    city_id VARCHAR(50) NOT NULL COMMENT '所属城市',
    location_description TEXT COMMENT '位置描述',
    historical_info TEXT COMMENT '历史信息',
    polluted_image_url VARCHAR(255) NOT NULL COMMENT '被污染时的图片',
    clean_image_url VARCHAR(255) NOT NULL COMMENT '清洁后的图片',
    success_exp INT DEFAULT 50 COMMENT '答题成功奖励经验',
    failure_exp INT DEFAULT -10 COMMENT '答题失败扣除经验',
    boss_damage INT DEFAULT 20 COMMENT '成功保护对BOSS造成的伤害',
    difficulty_level ENUM('easy', 'medium', 'hard') DEFAULT 'medium' COMMENT '难度等级',
    unlock_guardian_level INT DEFAULT 1 COMMENT '解锁需要的守望者等级',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_city_difficulty (city_id, difficulty_level),
    INDEX idx_active_level (is_active, unlock_guardian_level)
) COMMENT='古迹配置表';

-- 2. 古迹问题题库表
CREATE TABLE monument_questions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    monument_id INT NOT NULL,
    question_text TEXT NOT NULL COMMENT '问题内容',
    question_text_en TEXT COMMENT '问题英文内容',
    question_type ENUM('single_choice', 'multiple_choice', 'true_false') DEFAULT 'single_choice' COMMENT '题型',
    difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'medium' COMMENT '难度',
    explanation TEXT COMMENT '答案解释',
    explanation_en TEXT COMMENT '答案解释英文版',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (monument_id) REFERENCES monuments(id) ON DELETE CASCADE,
    INDEX idx_monument_difficulty (monument_id, difficulty),
    INDEX idx_active (is_active)
) COMMENT='古迹问题题库表';

-- 3. 问题选项表
CREATE TABLE question_options (
    id INT PRIMARY KEY AUTO_INCREMENT,
    question_id INT NOT NULL,
    option_text TEXT NOT NULL COMMENT '选项内容',
    option_text_en TEXT COMMENT '选项英文内容',
    is_correct BOOLEAN DEFAULT FALSE COMMENT '是否为正确答案',
    option_order INT DEFAULT 1 COMMENT '选项顺序',
    FOREIGN KEY (question_id) REFERENCES monument_questions(id) ON DELETE CASCADE,
    INDEX idx_question_order (question_id, option_order)
) COMMENT='问题选项表';

-- 4. 用户古迹挑战记录表
CREATE TABLE user_monument_challenges (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    monument_id INT NOT NULL,
    challenge_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    questions_total INT NOT NULL COMMENT '总题数',
    questions_correct INT NOT NULL COMMENT '答对题数',
    is_success BOOLEAN NOT NULL COMMENT '是否挑战成功',
    exp_gained INT NOT NULL COMMENT '获得的经验值(可为负)',
    boss_damage_dealt INT DEFAULT 0 COMMENT '对BOSS造成的伤害',
    time_spent_seconds INT COMMENT '答题耗时(秒)',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (monument_id) REFERENCES monuments(id) ON DELETE CASCADE,
    INDEX idx_user_time (user_id, challenge_time DESC),
    INDEX idx_monument_success (monument_id, is_success),
    INDEX idx_success_time (is_success, challenge_time DESC)
) COMMENT='用户古迹挑战记录表';

-- 5. 古迹保护状态表
CREATE TABLE monument_protection_status (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    monument_id INT NOT NULL,
    city_id VARCHAR(50) NOT NULL,
    is_protected BOOLEAN DEFAULT FALSE COMMENT '是否已保护',
    first_success_at TIMESTAMP NULL COMMENT '首次保护成功时间',
    last_challenge_at TIMESTAMP NULL COMMENT '最近一次挑战时间',
    success_count INT DEFAULT 0 COMMENT '成功保护次数',
    total_attempts INT DEFAULT 0 COMMENT '总挑战次数',
    best_score INT DEFAULT 0 COMMENT '最佳得分',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (monument_id) REFERENCES monuments(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_monument (user_id, monument_id),
    INDEX idx_city_protected (city_id, is_protected),
    INDEX idx_user_success_count (user_id, success_count DESC)
) COMMENT='古迹保护状态表';

-- 插入古迹配置数据
INSERT INTO monuments (monument_name, monument_name_en, city_id, location_description, historical_info, polluted_image_url, clean_image_url, success_exp, failure_exp, boss_damage) VALUES
-- 北京古迹
('故宫太和殿', 'Hall of Supreme Harmony', 'beijing', '紫禁城中轴线上最重要的建筑', '明清皇帝举行大典的地方，中国古代建筑艺术的巅峰', '/monuments/beijing/taihebulding_polluted.jpg', '/monuments/beijing/taihebulding_clean.jpg', 60, -15, 25),
('天坛祈年殿', 'Hall of Prayer for Good Harvests', 'beijing', '天坛的主要建筑', '明清皇帝祈求丰年的场所，体现了天人合一的思想', '/monuments/beijing/qiniandian_polluted.jpg', '/monuments/beijing/qiniandian_clean.jpg', 50, -10, 20),
('颐和园长廊', 'Long Corridor of Summer Palace', 'beijing', '颐和园的标志性建筑', '世界上最长的画廊，展现了中国古典园林艺术', '/monuments/beijing/changsung_polluted.jpg', '/monuments/beijing/changsung_clean.jpg', 45, -10, 18),

-- 上海古迹
('豫园三穗堂', 'Sansueitang Hall', 'shanghai', '豫园的主要厅堂建筑', '明代私家园林的杰出代表，江南园林艺术精品', '/monuments/shanghai/sansuitang_polluted.jpg', '/monuments/shanghai/sansuitang_clean.jpg', 50, -10, 20),
('外滩和平饭店', 'Peace Hotel', 'shanghai', '外滩标志性历史建筑', '见证了上海近现代历史变迁的重要建筑', '/monuments/shanghai/peace_hotel_polluted.jpg', '/monuments/shanghai/peace_hotel_clean.jpg', 45, -10, 18);

-- 插入问题数据 - 故宫太和殿
INSERT INTO monument_questions (monument_id, question_text, question_text_en, question_type, difficulty, explanation) VALUES
(1, '太和殿是紫禁城中轴线上最重要的建筑，它主要用于什么场合？', 'What was the Hall of Supreme Harmony mainly used for?', 'single_choice', 'medium', '太和殿是皇帝举行登基、大婚、册封等重大典礼的场所'),
(1, '太和殿的屋顶采用什么颜色的琉璃瓦？', 'What color are the glazed tiles on the roof of the Hall of Supreme Harmony?', 'single_choice', 'easy', '太和殿采用金黄色琉璃瓦，象征皇权至上'),
(1, '太和殿前有多少只铜狮子？', 'How many bronze lions are there in front of the Hall of Supreme Harmony?', 'single_choice', 'hard', '太和殿前有一对铜狮子，雄狮在东，雌狮在西');

-- 插入选项数据
INSERT INTO question_options (question_id, option_text, option_text_en, is_correct, option_order) VALUES
-- 第一题选项
(1, '皇帝举行重大典礼', 'Major imperial ceremonies', TRUE, 1),
(1, '皇帝日常办公', 'Daily imperial office work', FALSE, 2),
(1, '皇帝休息娱乐', 'Imperial rest and entertainment', FALSE, 3),
(1, '存放宝物', 'Storing treasures', FALSE, 4),

-- 第二题选项
(2, '金黄色', 'Golden yellow', TRUE, 1),
(2, '深蓝色', 'Deep blue', FALSE, 2),
(2, '翠绿色', 'Emerald green', FALSE, 3),
(2, '紫色', 'Purple', FALSE, 4),

-- 第三题选项
(3, '一对(2只)', 'One pair (2 lions)', TRUE, 1),
(3, '三对(6只)', 'Three pairs (6 lions)', FALSE, 2),
(3, '五对(10只)', 'Five pairs (10 lions)', FALSE, 3),
(3, '没有狮子', 'No lions', FALSE, 4);