-- ====================================
-- 清理旧系统 - 删除货币和大炮相关的所有数据表和字段
-- ====================================

-- 警告：此脚本将删除所有旧系统数据，请在执行前备份重要数据！

-- 1. 删除大炮系统相关表
DROP TABLE IF EXISTS cannon_types;
DROP TABLE IF EXISTS user_cannons;  
DROP TABLE IF EXISTS cannon_upgrades;
DROP TABLE IF EXISTS cannon_stats;
DROP TABLE IF EXISTS cannon_purchases;
DROP TABLE IF EXISTS cannon_levels;

-- 2. 删除货币系统相关表
DROP TABLE IF EXISTS user_transactions;
DROP TABLE IF EXISTS purchase_records;
DROP TABLE IF EXISTS shop_items;
DROP TABLE IF EXISTS payment_records;
DROP TABLE IF EXISTS currency_logs;
DROP TABLE IF EXISTS user_purchases;
DROP TABLE IF EXISTS store_products;

-- 3. 删除旧的游戏机制表
DROP TABLE IF EXISTS user_weapons;
DROP TABLE IF EXISTS weapon_upgrades;
DROP TABLE IF EXISTS combat_records;
DROP TABLE IF EXISTS battle_logs;
DROP TABLE IF EXISTS shooting_stats;

-- 4. 从用户表删除旧系统字段
ALTER TABLE users 
DROP COLUMN IF EXISTS gold,
DROP COLUMN IF EXISTS silver,
DROP COLUMN IF EXISTS diamond,
DROP COLUMN IF EXISTS coins,
DROP COLUMN IF EXISTS gems,
DROP COLUMN IF EXISTS current_cannon,
DROP COLUMN IF EXISTS cannon_level,
DROP COLUMN IF EXISTS cannon_damage,
DROP COLUMN IF EXISTS cannon_crit_rate,
DROP COLUMN IF EXISTS cannon_accuracy,
DROP COLUMN IF EXISTS cannon_fire_rate,
DROP COLUMN IF EXISTS weapon_id,
DROP COLUMN IF EXISTS ammo_count,
DROP COLUMN IF EXISTS total_shots_fired,
DROP COLUMN IF EXISTS total_hits,
DROP COLUMN IF EXISTS accuracy_rate,
DROP COLUMN IF EXISTS combat_level,
DROP COLUMN IF EXISTS battle_wins,
DROP COLUMN IF EXISTS battle_losses;

-- 5. 从游戏会话表删除旧字段（如果存在）
ALTER TABLE game_sessions 
DROP COLUMN IF EXISTS gold_earned,
DROP COLUMN IF EXISTS diamond_earned,
DROP COLUMN IF EXISTS shots_fired,
DROP COLUMN IF EXISTS enemies_defeated,
DROP COLUMN IF EXISTS accuracy_percentage;

-- 6. 从热点表删除旧的奖励配置字段
ALTER TABLE hotspots 
DROP COLUMN IF EXISTS gold_reward,
DROP COLUMN IF EXISTS diamond_reward,
DROP COLUMN IF EXISTS coin_reward,
DROP COLUMN IF EXISTS weapon_drop_rate,
DROP COLUMN IF EXISTS ammo_reward;

-- 7. 删除旧的统计和日志表
DROP TABLE IF EXISTS daily_gold_logs;
DROP TABLE IF EXISTS diamond_purchase_logs;
DROP TABLE IF EXISTS weapon_usage_stats;
DROP TABLE IF EXISTS combat_analytics;
DROP TABLE IF EXISTS shooting_accuracy_logs;

-- 8. 删除旧的商店和购买系统表
DROP TABLE IF EXISTS shop_categories;
DROP TABLE IF EXISTS product_catalog;
DROP TABLE IF EXISTS user_inventory;
DROP TABLE IF EXISTS purchase_history;

-- 9. 删除旧的成就和排行榜表（基于货币的）
DROP TABLE IF EXISTS wealth_rankings;
DROP TABLE IF EXISTS combat_rankings;
DROP TABLE IF EXISTS weapon_master_rankings;

-- 10. 清理旧的索引（如果它们还存在的话）
-- MySQL会在删除列时自动删除相关索引，但为了确保清理彻底
DROP INDEX IF EXISTS idx_users_gold ON users;
DROP INDEX IF EXISTS idx_users_diamond ON users;
DROP INDEX IF EXISTS idx_users_cannon_level ON users;
DROP INDEX IF EXISTS idx_combat_level ON users;

-- 11. 清理旧的存储过程和函数（如果有的话）
DROP PROCEDURE IF EXISTS sp_update_user_gold;
DROP PROCEDURE IF EXISTS sp_upgrade_cannon;
DROP PROCEDURE IF EXISTS sp_calculate_combat_stats;
DROP FUNCTION IF EXISTS fn_get_cannon_damage;
DROP FUNCTION IF EXISTS fn_calculate_accuracy;

-- 12. 清理旧的触发器
DROP TRIGGER IF EXISTS trg_update_gold_on_purchase;
DROP TRIGGER IF EXISTS trg_log_cannon_upgrade;
DROP TRIGGER IF EXISTS trg_update_combat_stats;

-- 13. 清理旧的视图
DROP VIEW IF EXISTS v_user_wealth_stats;
DROP VIEW IF EXISTS v_cannon_leaderboard;
DROP VIEW IF EXISTS v_combat_rankings;
DROP VIEW IF EXISTS v_user_combat_summary;

-- ====================================
-- 验证清理结果
-- ====================================

-- 查看剩余的表
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME LIKE '%cannon%' 
   OR TABLE_NAME LIKE '%gold%' 
   OR TABLE_NAME LIKE '%diamond%' 
   OR TABLE_NAME LIKE '%weapon%' 
   OR TABLE_NAME LIKE '%combat%'
   OR TABLE_NAME LIKE '%shop%'
   OR TABLE_NAME LIKE '%purchase%';

-- 查看用户表当前结构
DESCRIBE users;

-- 验证没有货币相关字段
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'users' 
  AND (COLUMN_NAME LIKE '%gold%' 
    OR COLUMN_NAME LIKE '%diamond%' 
    OR COLUMN_NAME LIKE '%cannon%' 
    OR COLUMN_NAME LIKE '%weapon%'
    OR COLUMN_NAME LIKE '%combat%');