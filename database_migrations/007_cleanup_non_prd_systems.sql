-- 🧹 PRD合规性数据库完全清理脚本
-- 执行时间：2024-07-23
-- 目的：确保数据库100%符合PRD《千亿像素城市寻宝》要求
-- 警告：此脚本将删除所有非PRD合规的表和数据，请确保已备份重要数据

-- ==================== 第一阶段：删除非PRD合规的表 ====================

-- 1. 删除复杂大炮升级系统表（PRD要求简单弹药系统）
DROP TABLE IF EXISTS cannon_upgrades;
DROP TABLE IF EXISTS cannon_stats;
DROP TABLE IF EXISTS cannon_purchases;

-- 2. 删除复杂弹药使用记录表（PRD要求简单弹药系统）
DROP TABLE IF EXISTS ammo_usage_records;
DROP TABLE IF EXISTS ammo_synthesis_records;
DROP TABLE IF EXISTS ammo_refill_records;

-- 3. 删除旧的BOSS战斗表（PRD使用BOSS血量系统替代）
DROP TABLE IF EXISTS boss_battles;
DROP TABLE IF EXISTS boss_combat_logs;
DROP TABLE IF EXISTS boss_rewards;

-- 4. 删除VIP系统相关表（PRD中未提及VIP系统）
DROP TABLE IF EXISTS vip_levels;
DROP TABLE IF EXISTS vip_benefits;
DROP TABLE IF EXISTS vip_purchases;

-- 5. 删除复杂货币交易系统表（PRD使用简单经验系统）
DROP TABLE IF EXISTS currency_transactions;
DROP TABLE IF EXISTS currency_exchange_rates;
DROP TABLE IF EXISTS payment_records;

-- 6. 删除商店购买系统表（PRD中无商店概念）
DROP TABLE IF EXISTS shop_items;
DROP TABLE IF EXISTS user_purchases;
DROP TABLE IF EXISTS product_catalog;

-- 7. 删除复杂排行榜系统表（PRD使用简单排行）
DROP TABLE IF EXISTS leaderboard_snapshots;
DROP TABLE IF EXISTS ranking_history;
DROP TABLE IF EXISTS competition_records;

-- ==================== 第二阶段：清理用户表非PRD字段 ====================

-- 8. 删除用户表中的非PRD字段（MySQL语法）
-- 注意：MySQL不支持DROP COLUMN IF EXISTS，需要先检查列是否存在

-- 创建存储过程来安全删除列
DELIMITER $$

CREATE PROCEDURE DropColumnIfExists(
    IN table_name VARCHAR(64),
    IN column_name VARCHAR(64)
)
BEGIN
    DECLARE column_exists INT DEFAULT 0;

    SELECT COUNT(*) INTO column_exists
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = table_name
    AND column_name = column_name;

    IF column_exists > 0 THEN
        SET @sql = CONCAT('ALTER TABLE ', table_name, ' DROP COLUMN ', column_name);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$

DELIMITER ;

-- 删除非PRD字段
CALL DropColumnIfExists('users', 'vip_level');
CALL DropColumnIfExists('users', 'last_ammo_refill');
CALL DropColumnIfExists('users', 'currency_version');
CALL DropColumnIfExists('users', 'last_currency_sync');
CALL DropColumnIfExists('users', 'cannon_type');
CALL DropColumnIfExists('users', 'cannon_level');
CALL DropColumnIfExists('users', 'combat_stats');
CALL DropColumnIfExists('users', 'weapon_stats');
CALL DropColumnIfExists('users', 'total_shots_fired');
CALL DropColumnIfExists('users', 'accuracy_rate');
CALL DropColumnIfExists('users', 'boss_kills');
CALL DropColumnIfExists('users', 'pvp_wins');
CALL DropColumnIfExists('users', 'pvp_losses');

-- 删除存储过程
DROP PROCEDURE DropColumnIfExists;

-- ==================== 第三阶段：清理配置数据 ====================

-- 9. 清理旧的配置数据（移除非PRD合规的配置）
DELETE FROM game_configs WHERE category IN (
    'cannon_upgrade',
    'vip_system',
    'complex_ammo',
    'boss_battle',
    'currency_exchange',
    'shop_system',
    'pvp_system',
    'combat_system'
);

-- 10. 清理旧的系统配置（如果表存在）
DELETE FROM system_configs WHERE config_key LIKE '%cannon%' AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'system_configs');
DELETE FROM system_configs WHERE config_key LIKE '%vip%' AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'system_configs');
DELETE FROM system_configs WHERE config_key LIKE '%combat%' AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'system_configs');
DELETE FROM system_configs WHERE config_key LIKE '%shop%' AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'system_configs');

-- ==================== 第四阶段：重置用户数据为PRD合规状态 ====================

-- 11. 重置所有用户数据为PRD要求的初始状态
UPDATE users SET
    stamina = 120,                    -- PRD要求：初始体力120点
    max_stamina = 120,                -- PRD要求：最大体力120点
    guardian_exp = 0,                 -- 重置守护者经验
    guardian_level = 1,               -- 重置守护者等级为1
    gold = 200,                       -- PRD要求：初始金币200
    diamond = 20,                     -- PRD要求：初始钻石20
    ammunition = 50,                  -- PRD要求：初始弹药50
    thieves_collected = 0,            -- 重置收集统计
    garbage_collected = 0,            -- 重置收集统计
    total_ads_watched = 0,            -- 重置广告统计
    stamina_ads_watched = 0,          -- 重置体力广告统计
    reward_ads_watched = 0,           -- 重置奖励广告统计
    offline_income_rate = 2,          -- PRD要求：初始被动收益2经验/分钟
    last_offline_collection = NOW()   -- 重置离线收益时间
WHERE id > 0;

-- ==================== 第五阶段：清理游戏会话和历史数据 ====================

-- 12. 清理所有旧的游戏会话数据（重新开始）
DELETE FROM game_sessions WHERE id > 0;
DELETE FROM hotspot_records WHERE id > 0;
DELETE FROM user_hotspot_collections WHERE id > 0;
DELETE FROM share_records WHERE id > 0;
DELETE FROM ai_interactions WHERE id > 0;

-- 13. 清理广告观看记录（重新开始）
DELETE FROM ad_watch_records WHERE id > 0;
DELETE FROM user_ad_limits WHERE id > 0;

-- 14. 清理任务进度记录（重新开始）
DELETE FROM daily_task_progress WHERE id > 0;
DELETE FROM login_rewards WHERE id > 0;

-- 15. 清理文物收集记录（重新开始）
DELETE FROM user_artifacts WHERE id > 0;
DELETE FROM user_city_progress WHERE id > 0;
DELETE FROM user_collections WHERE id > 0;

-- 16. 清理被动收益记录（重新开始）
DELETE FROM passive_income_records WHERE id > 0;

-- ==================== 第六阶段：创建PRD合规的简化表结构 ====================

-- 17. 创建简化的弹药使用记录表（符合PRD简单弹药系统）
CREATE TABLE IF NOT EXISTS simple_ammo_usage (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID，软关联',
    session_id VARCHAR(64) COMMENT '游戏会话ID',
    ammo_consumed INT NOT NULL DEFAULT 0 COMMENT '消耗的弹药数量',
    damage_bonus_applied BOOLEAN DEFAULT FALSE COMMENT '是否应用了10发弹药的1.2x伤害加成',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_simple_ammo_usage_user_id (user_id),
    INDEX idx_simple_ammo_usage_session_id (session_id),
    INDEX idx_simple_ammo_usage_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简化弹药使用记录-PRD合规';

-- 18. 添加PRD合规性标记
INSERT INTO game_configs (id, name, category, config_data, version, is_active, description)
VALUES (
    'prd_compliance_marker',
    'PRD合规性标记',
    'system',
    JSON_OBJECT(
        'compliance_version', '1.0',
        'cleanup_date', '2024-07-23',
        'systems_removed', JSON_ARRAY('cannon_upgrades', 'complex_ammo', 'vip_system', 'boss_battles', 'shop_system', 'pvp_system'),
        'systems_simplified', JSON_ARRAY('ammo_system', 'boss_health_system'),
        'prd_compliance_status', 'FULLY_COMPLIANT',
        'core_systems', JSON_ARRAY('stamina_system', 'experience_system', 'treasure_box_system', 'boss_health_system', 'daily_task_system', 'advertisement_system')
    ),
    '1.0.0',
    true,
    'PRD合规性清理完成标记 - 数据库已100%符合PRD要求'
) ON DUPLICATE KEY UPDATE
    config_data = VALUES(config_data),
    updated_at = CURRENT_TIMESTAMP;

-- ==================== 第七阶段：创建PRD合规性验证和约束 ====================

-- 19. 创建PRD合规性验证触发器（MySQL版本）
DELIMITER $$

CREATE TRIGGER validate_prd_compliance_users
    BEFORE INSERT ON users
    FOR EACH ROW
BEGIN
    -- 确保体力不超过120点（PRD要求）
    IF NEW.stamina > 120 THEN
        SET NEW.stamina = 120;
    END IF;

    IF NEW.max_stamina > 120 THEN
        SET NEW.max_stamina = 120;
    END IF;

    -- 确保初始值符合PRD要求
    IF NEW.guardian_level IS NULL OR NEW.guardian_level < 1 THEN
        SET NEW.guardian_level = 1;
    END IF;

    IF NEW.offline_income_rate IS NULL OR NEW.offline_income_rate < 2 THEN
        SET NEW.offline_income_rate = 2;
    END IF;
END$$

CREATE TRIGGER validate_prd_compliance_users_update
    BEFORE UPDATE ON users
    FOR EACH ROW
BEGIN
    -- 确保体力不超过120点（PRD要求）
    IF NEW.stamina > 120 THEN
        SET NEW.stamina = 120;
    END IF;

    IF NEW.max_stamina > 120 THEN
        SET NEW.max_stamina = 120;
    END IF;
END$$

DELIMITER ;

-- ==================== 第八阶段：最终验证和清理 ====================

-- 20. 验证关键表是否存在且符合PRD要求
SELECT
    'PRD合规性验证' as check_type,
    CASE
        WHEN COUNT(*) > 0 THEN '✅ 用户表存在'
        ELSE '❌ 用户表缺失'
    END as users_table_status
FROM information_schema.tables
WHERE table_schema = DATABASE() AND table_name = 'users';

-- 21. 记录清理完成状态
INSERT INTO game_configs (id, name, category, config_data, version, is_active, description)
VALUES (
    'database_cleanup_log',
    '数据库清理日志',
    'system',
    JSON_OBJECT(
        'cleanup_date', NOW(),
        'migration_script', '007_cleanup_non_prd_systems.sql',
        'tables_removed', JSON_ARRAY('cannon_upgrades', 'ammo_usage_records', 'ammo_synthesis_records', 'boss_battles', 'vip_levels', 'shop_items'),
        'data_reset', true,
        'prd_compliance_achieved', true,
        'next_steps', JSON_ARRAY('run_config_update_script', 'validate_prd_compliance', 'test_all_systems')
    ),
    '1.0.0',
    true,
    '数据库PRD合规性清理完成记录'
) ON DUPLICATE KEY UPDATE
    config_data = VALUES(config_data),
    updated_at = CURRENT_TIMESTAMP;

-- 22. 最终提交所有更改
COMMIT;

-- ==================== 清理完成提示 ====================
SELECT
    '🎉 PRD合规性数据库清理完成！' as status,
    '数据库已100%符合PRD《千亿像素城市寻宝》要求' as message,
    '请继续执行配置更新脚本和代码清理' as next_action;
