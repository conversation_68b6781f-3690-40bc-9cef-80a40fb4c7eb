-- ====================================
-- 每日任务系统表
-- ====================================

-- 1. 任务配置表
CREATE TABLE task_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    task_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    task_name_en VARCHAR(100) COMMENT '任务英文名称',
    task_type ENUM('catch_thieves', 'clean_garbage', 'collect_artifacts', 'protect_monuments', 'defeat_boss', 'login', 'special') NOT NULL COMMENT '任务类型',
    description TEXT NOT NULL COMMENT '任务描述',
    description_en TEXT COMMENT '任务英文描述',
    target_count INT NOT NULL DEFAULT 1 COMMENT '目标数量',
    base_exp_reward INT NOT NULL DEFAULT 10 COMMENT '基础经验奖励',
    difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'easy' COMMENT '难度等级',
    task_category ENUM('daily', 'weekly', 'achievement') DEFAULT 'daily' COMMENT '任务分类',
    is_repeatable BOOLEAN DEFAULT TRUE COMMENT '是否可重复',
    unlock_guardian_level INT DEFAULT 1 COMMENT '解锁需要的守望者等级',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type_category (task_type, task_category),
    INDEX idx_active_level (is_active, unlock_guardian_level)
) COMMENT='任务配置表';

-- 2. 用户每日任务表
CREATE TABLE user_daily_tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    task_config_id INT NOT NULL,
    task_date DATE NOT NULL COMMENT '任务日期',
    current_progress INT DEFAULT 0 COMMENT '当前进度',
    target_count INT NOT NULL COMMENT '目标数量',
    is_completed BOOLEAN DEFAULT FALSE COMMENT '是否完成',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    exp_reward INT NOT NULL COMMENT '经验奖励',
    is_double_rewarded BOOLEAN DEFAULT FALSE COMMENT '是否已领取双倍奖励',
    double_reward_at TIMESTAMP NULL COMMENT '双倍奖励领取时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (task_config_id) REFERENCES task_configs(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_task_date (user_id, task_config_id, task_date),
    INDEX idx_user_date (user_id, task_date DESC),
    INDEX idx_date_completed (task_date, is_completed),
    INDEX idx_completed_reward (is_completed, is_double_rewarded)
) COMMENT='用户每日任务表';

-- 3. 任务进度记录表
CREATE TABLE task_progress_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_daily_task_id INT NOT NULL,
    progress_increment INT NOT NULL COMMENT '进度增量',
    source_type ENUM('thief', 'garbage', 'monument', 'collection', 'boss', 'login', 'manual') NOT NULL COMMENT '进度来源',
    source_id VARCHAR(100) COMMENT '来源标识',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_daily_task_id) REFERENCES user_daily_tasks(id) ON DELETE CASCADE,
    INDEX idx_task_time (user_daily_task_id, created_at DESC),
    INDEX idx_source_time (source_type, created_at DESC)
) COMMENT='任务进度记录表';

-- 4. 任务完成统计表
CREATE TABLE user_task_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    date DATE NOT NULL COMMENT '统计日期',
    total_tasks INT DEFAULT 0 COMMENT '当日总任务数',
    completed_tasks INT DEFAULT 0 COMMENT '已完成任务数',
    total_exp_earned INT DEFAULT 0 COMMENT '总获得经验',
    double_rewards_claimed INT DEFAULT 0 COMMENT '已领取双倍奖励次数',
    completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率',
    consecutive_days INT DEFAULT 0 COMMENT '连续完成天数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_date (user_id, date),
    INDEX idx_date_completion (date, completion_rate DESC),
    INDEX idx_consecutive_days (consecutive_days DESC)
) COMMENT='任务完成统计表';

-- 插入默认任务配置
INSERT INTO task_configs (task_name, task_name_en, task_type, description, description_en, target_count, base_exp_reward, difficulty) VALUES
-- 抓捕小偷任务
('初级清道夫', 'Junior Street Cleaner', 'catch_thieves', '抓捕10个小偷，维护城市治安', 'Catch 10 thieves to maintain city security', 10, 50, 'easy'),
('中级守护者', 'Intermediate Guardian', 'catch_thieves', '抓捕50个小偷，成为合格的城市守护者', 'Catch 50 thieves to become a qualified city guardian', 50, 200, 'medium'),
('高级执法者', 'Senior Law Enforcer', 'catch_thieves', '抓捕100个小偷，成为执法专家', 'Catch 100 thieves to become a law enforcement expert', 100, 500, 'hard'),

-- 清理垃圾任务
('环保新手', 'Environmental Beginner', 'clean_garbage', '清理10个垃圾，保持环境整洁', 'Clean 10 pieces of garbage to keep the environment clean', 10, 40, 'easy'),
('环保达人', 'Environmental Expert', 'clean_garbage', '清理50个垃圾，成为环保达人', 'Clean 50 pieces of garbage to become an environmental expert', 50, 180, 'medium'),
('清洁大师', 'Cleanliness Master', 'clean_garbage', '清理100个垃圾，获得清洁大师称号', 'Clean 100 pieces of garbage to earn the title of Cleanliness Master', 100, 450, 'hard'),

-- 文物收集任务
('文物收藏家', 'Artifact Collector', 'collect_artifacts', '收集1个文化图鉴，了解城市文化', 'Collect 1 cultural artifact to learn about city culture', 1, 60, 'easy'),
('文化学者', 'Cultural Scholar', 'collect_artifacts', '收集5个文化图鉴，深入了解历史文化', 'Collect 5 cultural artifacts to deeply understand historical culture', 5, 300, 'medium'),

-- 保护古迹任务
('古迹守护者', 'Monument Guardian', 'protect_monuments', '保护1个古迹，传承历史文明', 'Protect 1 monument to preserve historical civilization', 1, 80, 'medium'),
('文明卫士', 'Civilization Defender', 'protect_monuments', '保护5个古迹，成为文明的守护者', 'Protect 5 monuments to become a guardian of civilization', 5, 400, 'hard'),

-- 每日登录任务
('每日签到', 'Daily Check-in', 'login', '每日登录游戏，获得签到奖励', 'Login daily to get check-in rewards', 1, 20, 'easy');

-- 为今天创建示例任务(假设有用户ID为1的用户)
-- 这部分在实际运行时应该通过程序逻辑来生成
INSERT INTO user_daily_tasks (user_id, task_config_id, task_date, target_count, exp_reward) 
SELECT 1, id, CURDATE(), target_count, base_exp_reward 
FROM task_configs 
WHERE task_category = 'daily' AND is_active = TRUE;