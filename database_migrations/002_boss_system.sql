-- ====================================
-- BOSS系统相关表
-- ====================================

-- 1. BOSS配置表
CREATE TABLE boss_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    city_id VARCHAR(50) NOT NULL COMMENT '城市ID',
    boss_name VARCHAR(100) NOT NULL COMMENT 'BOSS名称',
    max_hp INT NOT NULL DEFAULT 1000 COMMENT 'BOSS最大血量',
    avatar_url VARCHAR(255) COMMENT 'BOSS头像',
    background_url VARCHAR(255) COMMENT 'BOSS背景图',
    description TEXT COMMENT 'BOSS描述',
    defeat_reward_exp INT DEFAULT 200 COMMENT '击败奖励经验',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_city (city_id)
) COMMENT='BOSS配置表';

-- 2. BOSS状态表
CREATE TABLE boss_states (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    city_id VARCHAR(50) NOT NULL,
    current_hp INT NOT NULL DEFAULT 1000 COMMENT 'BOSS当前血量',
    max_hp INT NOT NULL DEFAULT 1000 COMMENT 'BOSS最大血量',
    stage ENUM('100', '80', '60', '40', '20', 'defeated') DEFAULT '100' COMMENT 'BOSS血量阶段',
    last_dialogue_stage ENUM('100', '80', '60', '40', '20', 'defeated') COMMENT '上次触发对话的阶段',
    last_damage_time TIMESTAMP COMMENT '最后一次受到伤害的时间',
    is_defeated BOOLEAN DEFAULT FALSE COMMENT '是否已被击败',
    defeat_time TIMESTAMP NULL COMMENT '被击败时间',
    total_damage_dealt INT DEFAULT 0 COMMENT '用户对此BOSS造成的总伤害',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_city (user_id, city_id),
    INDEX idx_city_defeated (city_id, is_defeated),
    INDEX idx_user_city (user_id, city_id)
) COMMENT='用户BOSS战斗状态表';

-- 3. BOSS对话配置表
CREATE TABLE boss_dialogues (
    id INT PRIMARY KEY AUTO_INCREMENT,
    city_id VARCHAR(50) NOT NULL COMMENT '城市ID',
    stage ENUM('intro', '80', '60', '40', '20', 'defeated') NOT NULL COMMENT '对话触发阶段',
    dialogue_text TEXT NOT NULL COMMENT '对话内容',
    dialogue_type ENUM('taunt', 'anger', 'bribe', 'beg', 'threat', 'defeat') DEFAULT 'taunt' COMMENT '对话类型',
    reward_exp INT DEFAULT 0 COMMENT '看完对话的经验奖励',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_city_stage (city_id, stage)
) COMMENT='BOSS对话配置表';

-- 4. BOSS伤害记录表
CREATE TABLE boss_damage_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    city_id VARCHAR(50) NOT NULL,
    damage_amount INT NOT NULL COMMENT '伤害量',
    damage_source ENUM('thief', 'garbage', 'monument', 'special') NOT NULL COMMENT '伤害来源',
    source_id VARCHAR(100) COMMENT '来源标识',
    boss_hp_before INT NOT NULL COMMENT '攻击前BOSS血量',
    boss_hp_after INT NOT NULL COMMENT '攻击后BOSS血量',
    triggered_dialogue BOOLEAN DEFAULT FALSE COMMENT '是否触发了对话',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_city_time (user_id, city_id, created_at DESC),
    INDEX idx_city_time (city_id, created_at DESC)
) COMMENT='BOSS伤害记录表';

-- 插入默认BOSS配置数据
INSERT INTO boss_configs (city_id, boss_name, max_hp, description, defeat_reward_exp) VALUES
('beijing', '垃圾大王·北京', 1000, '占领北京的垃圾军团头目，狡猾而贪婪，妄图将古都变成垃圾堆', 300),
('shanghai', '垃圾大王·上海', 1200, '盘踞在魔都的垃圾霸主，企图污染这座现代化都市', 350),
('guangzhou', '垃圾大王·广州', 1100, '南方的垃圾军阀，试图破坏这座历史名城', 320),
('shenzhen', '垃圾大王·深圳', 1300, '科技之都的垃圾病毒，想要摧毁创新之城', 380);

-- 插入默认BOSS对话
INSERT INTO boss_dialogues (city_id, stage, dialogue_text, dialogue_type, reward_exp) VALUES
-- 北京BOSS对话
('beijing', 'intro', '哼哼，又来了一个不自量力的守护者！这座古都很快就会被我的垃圾军团完全占领！', 'taunt', 5),
('beijing', '80', '什么？你竟然能清理这么多垃圾？不过这才刚刚开始！我还有更多的小偷大军！', 'anger', 10),
('beijing', '60', '可恶！你这个讨厌的守护者！我愿意分给你一半的垃圾王国，放过我吧！', 'bribe', 15),
('beijing', '40', '不！这不可能！我的精英小偷部队都被你击败了！求求你手下留情吧！', 'beg', 20),
('beijing', '20', '我不会放过你的！即使我被打败，我还会派出更强大的垃圾军团回来报复！', 'threat', 25),
('beijing', 'defeated', '不...不可能...我堂堂垃圾大王竟然败给了一个守护者...北京...还是属于人类的...', 'defeat', 50),

-- 上海BOSS对话  
('shanghai', 'intro', '欢迎来到我的垃圾魔都！你以为凭借一己之力就能阻止我的计划？太天真了！', 'taunt', 5),
('shanghai', '80', '居然敢挑战我在上海的统治？看来我得加派更多垃圾兵团来对付你！', 'anger', 10),
('shanghai', '60', '等等！也许我们可以谈谈...我可以给你外滩最好的位置建立垃圾宫殿！', 'bribe', 15),
('shanghai', '40', '不要再攻击了！我投降！我愿意立刻撤出上海！', 'beg', 20),
('shanghai', '20', '你会后悔的！我的垃圾帝国遍布全球，总有一天会重新占领上海！', 'threat', 25),
('shanghai', 'defeated', '魔都...原来真的属于人类...我的垃圾帝国...就这样结束了...', 'defeat', 50);