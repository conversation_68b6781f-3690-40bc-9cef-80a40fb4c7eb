-- ====================================
-- 移除货币系统，添加文明守护者系统
-- ====================================

-- 1. 修改用户表 - 移除货币字段，添加文明经验值系统
ALTER TABLE users 
DROP COLUMN IF EXISTS gold,
DROP COLUMN IF EXISTS diamond,
DROP COLUMN IF EXISTS coins,
ADD COLUMN civilization_exp INT DEFAULT 0 COMMENT '文明经验值 - 主要进度指标',
ADD COLUMN guardian_level INT DEFAULT 1 COMMENT '守望者等级(1-14档)',
ADD COLUMN stamina INT DEFAULT 100 COMMENT '当前体力值',
ADD COLUMN max_stamina INT DEFAULT 100 COMMENT '最大体力值',
ADD COLUMN guardian_avatar_position JSON COMMENT '守望者头像在地图上的位置 {"x": 100, "y": 200, "city_id": "beijing"}',
ADD COLUMN collections_count INT DEFAULT 0 COMMENT '收集的文化图鉴数量',
ADD COLUMN total_thieves_captured INT DEFAULT 0 COMMENT '总抓捕小偷数量',
ADD COLUMN total_garbage_cleaned INT DEFAULT 0 COMMENT '总清理垃圾数量',
ADD COLUMN total_monuments_protected INT DEFAULT 0 COMMENT '总保护古迹数量',
ADD INDEX idx_civilization_exp (civilization_exp DESC),
ADD INDEX idx_collections_count (collections_count DESC),
ADD INDEX idx_guardian_level (guardian_level DESC);

-- 2. 删除货币相关的表
DROP TABLE IF EXISTS user_transactions;
DROP TABLE IF EXISTS purchase_records; 
DROP TABLE IF EXISTS shop_items;
DROP TABLE IF EXISTS cannon_types;
DROP TABLE IF EXISTS user_cannons;
DROP TABLE IF EXISTS cannon_upgrades;

-- 3. 创建文明经验值记录表
CREATE TABLE civilization_exp_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    amount INT NOT NULL COMMENT '获得的经验值(可为负数)',
    source ENUM('thief', 'garbage', 'monument_success', 'monument_failure', 'task', 'boss', 'daily_reward') NOT NULL COMMENT '经验来源',
    source_id VARCHAR(100) COMMENT '来源标识(热点ID、任务ID等)',
    city_id VARCHAR(50) COMMENT '所在城市',
    description TEXT COMMENT '详细描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_source (user_id, source),
    INDEX idx_created_at (created_at DESC)
) COMMENT='文明经验值获得记录';

-- 4. 创建守望者等级配置表
CREATE TABLE guardian_levels (
    level INT PRIMARY KEY,
    level_name VARCHAR(50) NOT NULL COMMENT '等级名称',
    required_exp INT NOT NULL COMMENT '升级所需文明经验值',
    level_description TEXT COMMENT '等级描述',
    avatar_effect VARCHAR(100) COMMENT '头像特效标识',
    passive_exp_rate DECIMAL(3,2) DEFAULT 1.00 COMMENT '被动经验获取倍率',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) COMMENT='守望者等级配置';

-- 插入默认守望者等级数据
INSERT INTO guardian_levels (level, level_name, required_exp, level_description, avatar_effect, passive_exp_rate) VALUES
(1, '新手守卫', 0, '刚刚踏上守护文明之路的新手', 'normal', 1.00),
(2, '初级守卫', 100, '开始展现守护天赋', 'bronze_glow', 1.05),
(3, '勇敢守卫', 250, '勇敢面对邪恶势力', 'bronze_glow', 1.10),
(4, '坚定守卫', 500, '意志坚定的文明守护者', 'silver_glow', 1.15),
(5, '英勇守卫', 1000, '英勇无畏的城市英雄', 'silver_glow', 1.20),
(6, '精英守卫', 2000, '精英级别的守护者', 'gold_glow', 1.25),
(7, '大师守卫', 4000, '守护技艺炉火纯青', 'gold_glow', 1.30),
(8, '传奇守卫', 8000, '传奇般的守护大师', 'platinum_glow', 1.35),
(9, '史诗守卫', 15000, '史诗级的文明守护者', 'platinum_glow', 1.40),
(10, '神话守卫', 30000, '神话般的存在', 'diamond_glow', 1.50),
(11, '至高守卫', 50000, '至高无上的守护神', 'diamond_glow', 1.60),
(12, '永恒守卫', 100000, '永恒的文明守护神', 'rainbow_glow', 1.70),
(13, '终极守卫', 200000, '终极守护者', 'ultimate_effect', 2.00),
(14, '文明之神', 500000, '文明的守护之神', 'divine_effect', 2.50);