-- PRD系统实现数据库迁移脚本
-- 根据《千亿像素城市寻宝》PRD需求添加所有核心系统表

-- ================================
-- 每日任务系统表
-- ================================

-- 每日任务模板表
CREATE TABLE IF NOT EXISTS daily_task_templates (
    id VARCHAR(36) PRIMARY KEY,
    task_code VARCHAR(50) UNIQUE NOT NULL COMMENT '任务编码',
    task_type ENUM('catch_thief', 'clean_rubbish', 'cultural_quiz', 'perfect_clear') NOT NULL COMMENT '任务类型',
    name VARCHAR(100) NOT NULL COMMENT '任务名称',
    name_en VARCHAR(100) COMMENT '英文名称',
    description TEXT NOT NULL COMMENT '任务描述',
    description_en TEXT COMMENT '英文描述',
    target_amounts JSON NOT NULL COMMENT '目标数量配置[15,30,60]',
    reward_experiences JSON NOT NULL COMMENT '经验奖励配置[50,120,300]',
    estimated_times JSON NOT NULL COMMENT '预估完成时间(分钟)[15,30,60]',
    difficulty_level INT DEFAULT 1 COMMENT '难度等级1-3',
    required_level INT DEFAULT 1 COMMENT '最低等级要求',
    city_requirement VARCHAR(50) COMMENT '城市要求',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    display_order INT DEFAULT 0 COMMENT '显示顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_task_type (task_type),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='每日任务模板表';

-- 用户每日任务表
CREATE TABLE IF NOT EXISTS user_daily_tasks (
    id VARCHAR(36) PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    template_id VARCHAR(36) NOT NULL COMMENT '任务模板ID',
    task_date TIMESTAMP NOT NULL COMMENT '任务日期',
    task_type ENUM('catch_thief', 'clean_rubbish', 'cultural_quiz', 'perfect_clear') NOT NULL COMMENT '任务类型',
    task_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    task_description TEXT NOT NULL COMMENT '任务描述',
    target_amount INT NOT NULL COMMENT '目标数量',
    current_progress INT DEFAULT 0 COMMENT '当前进度',
    completion_rate INT DEFAULT 0 COMMENT '完成百分比',
    base_experience INT NOT NULL COMMENT '基础经验奖励',
    actual_experience INT DEFAULT 0 COMMENT '实际获得经验',
    is_ad_doubled BOOLEAN DEFAULT FALSE COMMENT '是否观看广告翻倍',
    status ENUM('active', 'completed', 'expired') DEFAULT 'active' COMMENT '任务状态',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    claimed_at TIMESTAMP NULL COMMENT '领取时间',
    attempts_count INT DEFAULT 0 COMMENT '尝试次数',
    time_spent INT DEFAULT 0 COMMENT '花费时间(秒)',
    INDEX idx_user_date (user_id, task_date),
    INDEX idx_user_type (user_id, task_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户每日任务表';

-- 任务进度日志表
CREATE TABLE IF NOT EXISTS task_progress_logs (
    id VARCHAR(36) PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    task_id VARCHAR(36) NOT NULL COMMENT '任务ID',
    action_type VARCHAR(20) NOT NULL COMMENT '行为类型',
    progress_before INT DEFAULT 0 COMMENT '行为前进度',
    progress_after INT DEFAULT 0 COMMENT '行为后进度',
    progress_delta INT DEFAULT 0 COMMENT '进度变化',
    session_id VARCHAR(100) COMMENT '游戏会话ID',
    city_id VARCHAR(50) COMMENT '城市ID',
    level_type VARCHAR(20) COMMENT '关卡类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    INDEX idx_user_task (user_id, task_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务进度日志表';

-- 任务奖励历史表
CREATE TABLE IF NOT EXISTS task_reward_histories (
    id VARCHAR(36) PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    task_id VARCHAR(36) NOT NULL COMMENT '任务ID',
    experience_gained INT DEFAULT 0 COMMENT '获得经验',
    was_doubled BOOLEAN DEFAULT FALSE COMMENT '是否翻倍',
    ad_watched BOOLEAN DEFAULT FALSE COMMENT '是否观看广告',
    task_type ENUM('catch_thief', 'clean_rubbish', 'cultural_quiz', 'perfect_clear') NOT NULL COMMENT '任务类型',
    task_difficulty INT DEFAULT 1 COMMENT '任务难度',
    completion_time INT DEFAULT 0 COMMENT '完成时间(秒)',
    rewarded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '奖励时间',
    INDEX idx_user_id (user_id),
    INDEX idx_task_type (task_type),
    INDEX idx_rewarded_at (rewarded_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务奖励历史表';

-- ================================
-- 宝箱系统表
-- ================================

-- 宝箱配置表
CREATE TABLE IF NOT EXISTS treasure_box_configs (
    id VARCHAR(36) PRIMARY KEY,
    box_type ENUM('copper', 'silver', 'gold') NOT NULL COMMENT '宝箱类型',
    drop_source ENUM('catch_thief', 'clean_rubbish', 'cultural_quiz', 'level_complete', 'daily_task') NOT NULL COMMENT '掉落来源',
    drop_rate INT NOT NULL COMMENT '掉落概率(千分比)',
    daily_limit INT DEFAULT 0 COMMENT '每日掉落上限(0=无限制)',
    base_stamina INT DEFAULT 0 COMMENT '基础体力奖励',
    base_items JSON COMMENT '基础道具奖励',
    base_artifacts JSON COMMENT '基础图鉴奖励',
    ad_stamina_multiplier INT DEFAULT 2 COMMENT '广告体力倍数',
    ad_items_multiplier INT DEFAULT 2 COMMENT '广告道具倍数',
    ad_artifacts_multiplier INT DEFAULT 2 COMMENT '广告图鉴倍数',
    rare_item_chance INT DEFAULT 0 COMMENT '稀有道具概率(千分比)',
    rare_items JSON COMMENT '稀有道具列表',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_box_type (box_type),
    INDEX idx_drop_source (drop_source),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='宝箱配置表';

-- 用户宝箱表
CREATE TABLE IF NOT EXISTS user_treasure_boxes (
    id VARCHAR(36) PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    box_type ENUM('copper', 'silver', 'gold') NOT NULL COMMENT '宝箱类型',
    drop_source ENUM('catch_thief', 'clean_rubbish', 'cultural_quiz', 'level_complete', 'daily_task') NOT NULL COMMENT '掉落来源',
    source_context JSON COMMENT '来源上下文',
    obtained_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
    status ENUM('pending', 'opened', 'ad_opened') DEFAULT 'pending' COMMENT '宝箱状态',
    opened_at TIMESTAMP NULL COMMENT '开启时间',
    stamina_reward INT DEFAULT 0 COMMENT '体力奖励',
    items_reward JSON COMMENT '道具奖励',
    artifacts_reward JSON COMMENT '图鉴奖励',
    was_ad_doubled BOOLEAN DEFAULT FALSE COMMENT '是否广告翻倍',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    INDEX idx_user_id (user_id),
    INDEX idx_box_type (box_type),
    INDEX idx_status (status),
    INDEX idx_obtained_at (obtained_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户宝箱表';

-- 宝箱掉落日志表
CREATE TABLE IF NOT EXISTS treasure_box_drop_logs (
    id VARCHAR(36) PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    box_type ENUM('copper', 'silver', 'gold') NOT NULL COMMENT '宝箱类型',
    drop_source ENUM('catch_thief', 'clean_rubbish', 'cultural_quiz', 'level_complete', 'daily_task') NOT NULL COMMENT '掉落来源',
    drop_roll INT NOT NULL COMMENT '掉落随机数',
    drop_rate INT NOT NULL COMMENT '掉落概率',
    drop_success BOOLEAN NOT NULL COMMENT '是否掉落成功',
    session_id VARCHAR(100) COMMENT '游戏会话ID',
    city_id VARCHAR(50) COMMENT '城市ID',
    level_type VARCHAR(20) COMMENT '关卡类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '掉落时间',
    INDEX idx_user_id (user_id),
    INDEX idx_drop_source (drop_source),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='宝箱掉落日志表';

-- 宝箱开启记录表
CREATE TABLE IF NOT EXISTS box_open_records (
    id VARCHAR(36) PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    box_id VARCHAR(36) NOT NULL COMMENT '宝箱ID',
    box_type ENUM('copper', 'silver', 'gold') NOT NULL COMMENT '宝箱类型',
    open_method VARCHAR(20) NOT NULL COMMENT '开启方式：normal/ad',
    stamina_gained INT DEFAULT 0 COMMENT '获得体力',
    items_gained JSON COMMENT '获得道具',
    artifacts_gained JSON COMMENT '获得图鉴',
    total_value INT DEFAULT 0 COMMENT '总价值评估',
    was_lucky BOOLEAN DEFAULT FALSE COMMENT '是否幸运掉落',
    opened_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开启时间',
    INDEX idx_user_id (user_id),
    INDEX idx_box_type (box_type),
    INDEX idx_opened_at (opened_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='宝箱开启记录表';

-- 用户宝箱统计表
CREATE TABLE IF NOT EXISTS user_box_statistics (
    id VARCHAR(36) PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE COMMENT '用户ID',
    total_boxes_obtained INT DEFAULT 0 COMMENT '总获得宝箱数',
    copper_boxes_obtained INT DEFAULT 0 COMMENT '铜宝箱获得数',
    silver_boxes_obtained INT DEFAULT 0 COMMENT '银宝箱获得数',
    gold_boxes_obtained INT DEFAULT 0 COMMENT '金宝箱获得数',
    total_boxes_opened INT DEFAULT 0 COMMENT '总开启宝箱数',
    copper_boxes_opened INT DEFAULT 0 COMMENT '铜宝箱开启数',
    silver_boxes_opened INT DEFAULT 0 COMMENT '银宝箱开启数',
    gold_boxes_opened INT DEFAULT 0 COMMENT '金宝箱开启数',
    total_ad_opens INT DEFAULT 0 COMMENT '广告开启总数',
    total_free_opens INT DEFAULT 0 COMMENT '免费开启总数',
    total_stamina_from_boxes INT DEFAULT 0 COMMENT '宝箱获得体力总数',
    total_items_from_boxes INT DEFAULT 0 COMMENT '宝箱获得道具总数',
    total_artifacts_from_boxes INT DEFAULT 0 COMMENT '宝箱获得图鉴总数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户宝箱统计表';

-- ================================
-- 文化问答系统表
-- ================================

-- 文化问答题库表
CREATE TABLE IF NOT EXISTS cultural_quiz (
    id VARCHAR(36) PRIMARY KEY,
    quiz_code VARCHAR(50) UNIQUE NOT NULL COMMENT '问题编码',
    city_id VARCHAR(50) NOT NULL COMMENT '所属城市ID',
    heritage_type VARCHAR(20) NOT NULL COMMENT '遗产类型：world/national/local',
    question TEXT NOT NULL COMMENT '问题内容',
    options JSON NOT NULL COMMENT '选项列表',
    correct_answer VARCHAR(10) NOT NULL COMMENT '正确答案(A/B/C/D)',
    explanation TEXT COMMENT '答案解释',
    difficulty VARCHAR(10) DEFAULT 'medium' COMMENT '难度：easy/medium/hard',
    category VARCHAR(50) NOT NULL COMMENT '分类：世界遗产/国家文物/地方特色',
    tags JSON COMMENT '标签列表',
    cultural_value TEXT COMMENT '文化价值描述',
    knowledge_points JSON COMMENT '知识点列表',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_city_id (city_id),
    INDEX idx_heritage_type (heritage_type),
    INDEX idx_difficulty (difficulty),
    INDEX idx_category (category),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文化问答题库表';

-- 用户问答记录表
CREATE TABLE IF NOT EXISTS quiz_answers (
    id VARCHAR(36) PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    quiz_id VARCHAR(36) NOT NULL COMMENT '问题ID',
    session_id VARCHAR(100) NOT NULL COMMENT '游戏会话ID',
    user_answer VARCHAR(10) NOT NULL COMMENT '用户答案',
    is_correct BOOLEAN NOT NULL COMMENT '是否正确',
    answer_time INT COMMENT '答题时间(秒)',
    experience_gained INT DEFAULT 0 COMMENT '获得经验值',
    artifact_gained VARCHAR(100) COMMENT '获得图鉴',
    treasure_box_dropped BOOLEAN DEFAULT FALSE COMMENT '是否掉落宝箱',
    answered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '答题时间',
    INDEX idx_user_id (user_id),
    INDEX idx_quiz_id (quiz_id),
    INDEX idx_session_id (session_id),
    INDEX idx_answered_at (answered_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户问答记录表';

-- 文化图鉴表
CREATE TABLE IF NOT EXISTS cultural_artifacts (
    id VARCHAR(36) PRIMARY KEY,
    artifact_code VARCHAR(50) UNIQUE NOT NULL COMMENT '图鉴编码',
    city_id VARCHAR(50) NOT NULL COMMENT '所属城市ID',
    name VARCHAR(100) NOT NULL COMMENT '名称',
    name_en VARCHAR(100) COMMENT '英文名称',
    category VARCHAR(20) NOT NULL COMMENT '类别：非遗技艺/民俗文化/历史人物/特色美食',
    rarity VARCHAR(10) DEFAULT 'common' COMMENT '稀有度：common/rare/epic',
    description TEXT NOT NULL COMMENT '描述',
    cultural_background TEXT COMMENT '文化背景',
    historical_significance TEXT COMMENT '历史意义',
    image_url VARCHAR(255) COMMENT '图片URL',
    audio_url VARCHAR(255) COMMENT '音频URL',
    video_url VARCHAR(255) COMMENT '视频URL',
    unlock_condition VARCHAR(100) COMMENT '解锁条件',
    drop_rate INT DEFAULT 100 COMMENT '掉落概率(‰)',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_city_id (city_id),
    INDEX idx_category (category),
    INDEX idx_rarity (rarity),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文化图鉴表';

-- 用户图鉴收集表
CREATE TABLE IF NOT EXISTS user_artifact_collections (
    id VARCHAR(36) PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    artifact_id VARCHAR(36) NOT NULL COMMENT '图鉴ID',
    obtained_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
    obtained_from VARCHAR(50) COMMENT '获得来源：quiz/treasure_box/task',
    quantity INT DEFAULT 1 COMMENT '数量',
    is_new BOOLEAN DEFAULT TRUE COMMENT '是否新获得',
    last_viewed_at TIMESTAMP NULL COMMENT '最后查看时间',
    INDEX idx_user_id (user_id),
    INDEX idx_artifact_id (artifact_id),
    INDEX idx_obtained_at (obtained_at),
    UNIQUE KEY uk_user_artifact (user_id, artifact_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户图鉴收集表';

-- 古迹修复记录表
CREATE TABLE IF NOT EXISTS monument_restorations (
    id VARCHAR(36) PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    monument_id VARCHAR(50) NOT NULL COMMENT '古迹ID',
    city_id VARCHAR(50) NOT NULL COMMENT '城市ID',
    total_questions INT DEFAULT 0 COMMENT '总问题数',
    answered_questions INT DEFAULT 0 COMMENT '已答问题数',
    correct_answers INT DEFAULT 0 COMMENT '正确答案数',
    restoration_progress INT DEFAULT 0 COMMENT '修复进度百分比',
    is_completed BOOLEAN DEFAULT FALSE COMMENT '是否完成修复',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    total_experience INT DEFAULT 0 COMMENT '总获得经验',
    artifacts_gained JSON COMMENT '获得图鉴列表',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_monument_id (monument_id),
    INDEX idx_city_id (city_id),
    INDEX idx_is_completed (is_completed)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='古迹修复记录表';

-- ================================
-- BOSS对话记录表（补充）
-- ================================

-- BOSS对话记录表
CREATE TABLE IF NOT EXISTS boss_dialogues (
    id VARCHAR(36) PRIMARY KEY,
    boss_health_id VARCHAR(36) NOT NULL COMMENT 'BOSS血量记录ID',
    phase VARCHAR(20) NOT NULL COMMENT '对话阶段',
    dialogue_text TEXT NOT NULL COMMENT '对话内容',
    triggered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '触发时间',
    INDEX idx_boss_health_id (boss_health_id),
    INDEX idx_phase (phase),
    INDEX idx_triggered_at (triggered_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOSS对话记录表';

-- ================================
-- 插入基础宝箱配置数据
-- ================================

-- 插入宝箱配置（根据PRD优化后的数值）
INSERT IGNORE INTO treasure_box_configs (id, box_type, drop_source, drop_rate, daily_limit, base_stamina, base_items, ad_stamina_multiplier) VALUES
(UUID(), 'copper', 'catch_thief', 60, 0, 5, '{"magnifier": 1}', 2),
(UUID(), 'copper', 'clean_rubbish', 30, 0, 5, '{"magnifier": 1}', 2),
(UUID(), 'silver', 'cultural_quiz', 150, 0, 10, '{"artifact": 1}', 2),
(UUID(), 'gold', 'level_complete', 1000, 0, 0, '{"rare_artifact": 1, "item": 1}', 2);

-- ================================
-- 插入基础任务模板数据
-- ================================

-- 插入每日任务模板（根据PRD配置）
INSERT IGNORE INTO daily_task_templates (id, task_code, task_type, name, description, target_amounts, reward_experiences, estimated_times, difficulty_level) VALUES
(UUID(), 'catch_thief_easy', 'catch_thief', '新手守卫', '抓捕15个小偷，保护城市安全', '[15, 30, 60]', '[50, 120, 300]', '[15, 30, 60]', 1),
(UUID(), 'clean_rubbish_easy', 'clean_rubbish', '环保新人', '清理20个垃圾，净化城市环境', '[20, 40, 80]', '[40, 100, 250]', '[20, 40, 80]', 1),
(UUID(), 'cultural_quiz_easy', 'cultural_quiz', '文化学徒', '完成2次文化学习，传承文化知识', '[2, 5, 10]', '[80, 200, 500]', '[10, 25, 50]', 1),
(UUID(), 'perfect_clear_easy', 'perfect_clear', '通关新手', '完美通关1个关卡，展现守护者实力', '[1, 3, 5]', '[100, 300, 800]', '[30, 90, 150]', 1);

-- ================================
-- 更新现有用户表结构（如果需要）
-- ================================

-- 确保用户表包含所有PRD要求的字段
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS total_thieves_caught INT DEFAULT 0 COMMENT '累积抓捕小偷数量',
ADD COLUMN IF NOT EXISTS total_rubbish_cleaned INT DEFAULT 0 COMMENT '累积清理垃圾数量', 
ADD COLUMN IF NOT EXISTS total_monuments_restored INT DEFAULT 0 COMMENT '累积修复古迹数量',
ADD COLUMN IF NOT EXISTS total_quiz_answered INT DEFAULT 0 COMMENT '累积回答问题数量',
ADD COLUMN IF NOT EXISTS total_quiz_correct INT DEFAULT 0 COMMENT '累积正确回答数量',
ADD COLUMN IF NOT EXISTS treasure_boxes_opened INT DEFAULT 0 COMMENT '开启宝箱总数',
ADD COLUMN IF NOT EXISTS copper_boxes_opened INT DEFAULT 0 COMMENT '铜宝箱开启数',
ADD COLUMN IF NOT EXISTS silver_boxes_opened INT DEFAULT 0 COMMENT '银宝箱开启数',
ADD COLUMN IF NOT EXISTS gold_boxes_opened INT DEFAULT 0 COMMENT '金宝箱开启数',
ADD COLUMN IF NOT EXISTS total_ads_watched INT DEFAULT 0 COMMENT '观看广告总次数',
ADD COLUMN IF NOT EXISTS stamina_ads_watched INT DEFAULT 0 COMMENT '体力恢复广告次数',
ADD COLUMN IF NOT EXISTS reward_ads_watched INT DEFAULT 0 COMMENT '奖励翻倍广告次数',
ADD COLUMN IF NOT EXISTS offline_income_rate INT DEFAULT 2 COMMENT '离线收益速率(经验/分钟)',
ADD COLUMN IF NOT EXISTS last_offline_collection TIMESTAMP NULL COMMENT '最后收集离线收益时间';

-- ================================
-- 创建相关索引以优化查询性能
-- ================================

-- 为用户表添加必要索引
CREATE INDEX IF NOT EXISTS idx_guardian_level ON users(guardian_level);
CREATE INDEX IF NOT EXISTS idx_guardian_exp ON users(guardian_exp);
CREATE INDEX IF NOT EXISTS idx_stamina ON users(stamina);
CREATE INDEX IF NOT EXISTS idx_last_stamina_update ON users(last_stamina_update);

-- 为BOSS血量表添加索引
CREATE INDEX IF NOT EXISTS idx_boss_user_session ON boss_health(user_id, session_id);
CREATE INDEX IF NOT EXISTS idx_boss_city_level ON boss_health(city_id, level_type);
CREATE INDEX IF NOT EXISTS idx_boss_completed ON boss_health(is_completed);

-- ================================
-- 数据完整性约束和触发器（可选）
-- ================================

-- 创建触发器以自动更新统计数据（示例）
DELIMITER //

-- 用户经验值更新触发器
CREATE TRIGGER IF NOT EXISTS update_user_stats_after_exp_gain
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    -- 当经验值变化时，可以触发相关统计更新
    IF NEW.guardian_exp != OLD.guardian_exp THEN
        -- 记录经验变化日志到Redis或其他地方
        -- 这里只是示例，实际实现可能在应用层处理
        SET @exp_change = NEW.guardian_exp - OLD.guardian_exp;
    END IF;
END//

DELIMITER ;

-- ================================
-- 完成迁移
-- ================================

-- 迁移完成标记
INSERT IGNORE INTO migration_history (version, description, executed_at) VALUES 
('006', 'PRD系统实现 - 添加任务、宝箱、问答、BOSS等核心系统表', NOW());

-- 查询验证表是否创建成功
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    CREATE_TIME
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN (
    'daily_task_templates', 'user_daily_tasks', 'task_progress_logs', 'task_reward_histories',
    'treasure_box_configs', 'user_treasure_boxes', 'box_open_records', 'user_box_statistics',
    'cultural_quiz', 'quiz_answers', 'cultural_artifacts', 'user_artifact_collections',
    'monument_restorations', 'boss_dialogues'
)
ORDER BY TABLE_NAME;