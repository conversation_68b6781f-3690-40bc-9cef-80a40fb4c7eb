-- ====================================
-- 文化图鉴系统表
-- ====================================

-- 1. 文化图鉴配置表
CREATE TABLE cultural_collections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '图鉴名称',
    name_en VARCHAR(100) COMMENT '英文名称',
    description TEXT NOT NULL COMMENT '图鉴描述',
    description_en TEXT COMMENT '英文描述',
    city_id VARCHAR(50) NOT NULL COMMENT '所属城市',
    category ENUM('food', 'architecture', 'culture', 'history', 'art', 'technology') NOT NULL COMMENT '图鉴分类',
    rarity ENUM('common', 'rare', 'epic', 'legendary') DEFAULT 'common' COMMENT '稀有度',
    image_url VARCHAR(255) NOT NULL COMMENT '图鉴图片URL',
    thumbnail_url VARCHAR(255) COMMENT '缩略图URL',
    exp_reward INT DEFAULT 10 COMMENT '收集获得的文明经验',
    drop_rate DECIMAL(5,4) DEFAULT 0.1000 COMMENT '掉落概率(0.0001-1.0000)',
    unlock_condition TEXT COMMENT '解锁条件',
    cultural_value TEXT COMMENT '文化价值说明',
    historical_significance TEXT COMMENT '历史意义',
    fun_fact TEXT COMMENT '趣闻轶事',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_city_category (city_id, category),
    INDEX idx_rarity_drop_rate (rarity, drop_rate),
    INDEX idx_active_city (is_active, city_id)
) COMMENT='文化图鉴配置表';

-- 2. 用户图鉴收集记录表
CREATE TABLE user_collections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    collection_id INT NOT NULL,
    collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    collected_from ENUM('thief', 'garbage', 'treasure', 'task', 'special') DEFAULT 'thief' COMMENT '获得方式',
    source_location VARCHAR(100) COMMENT '获得位置',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (collection_id) REFERENCES cultural_collections(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_collection (user_id, collection_id),
    INDEX idx_user_collected_at (user_id, collected_at DESC),
    INDEX idx_collection_count (collection_id)
) COMMENT='用户图鉴收集记录表';

-- 3. 图鉴收集统计表
CREATE TABLE collection_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    city_id VARCHAR(50) NOT NULL,
    total_collections INT DEFAULT 0 COMMENT '总收集数量',
    common_count INT DEFAULT 0 COMMENT '普通图鉴数量',
    rare_count INT DEFAULT 0 COMMENT '稀有图鉴数量', 
    epic_count INT DEFAULT 0 COMMENT '史诗图鉴数量',
    legendary_count INT DEFAULT 0 COMMENT '传说图鉴数量',
    completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率',
    first_collection_at TIMESTAMP NULL COMMENT '首次收集时间',
    last_collection_at TIMESTAMP NULL COMMENT '最近收集时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_city (user_id, city_id),
    INDEX idx_city_completion (city_id, completion_rate DESC),
    INDEX idx_total_collections (total_collections DESC)
) COMMENT='图鉴收集统计表';

-- 插入北京文化图鉴数据
INSERT INTO cultural_collections (name, name_en, description, description_en, city_id, category, rarity, image_url, exp_reward, drop_rate, cultural_value) VALUES
-- 北京美食文化
('北京烤鸭', 'Beijing Roast Duck', '北京最著名的传统美食，皮脆肉嫩，配以荷叶饼、甜面酱食用', 'Beijing\'s most famous traditional cuisine, crispy skin and tender meat', 'beijing', 'food', 'epic', '/collections/beijing/peking_duck.jpg', 25, 0.0500, '北京烤鸭是中华美食文化的瑰宝，代表了中国烹饪艺术的最高水准'),
('炸酱面', 'Zhajiangmian', '北京传统面食，香浓的肉酱配以爽滑的面条', 'Traditional Beijing noodles with savory meat sauce', 'beijing', 'food', 'common', '/collections/beijing/zhajiangmian.jpg', 10, 0.1500, '老北京人的日常美食，承载着浓厚的生活气息'),
('豆汁儿', 'Douzhir', '北京特色发酵豆制品饮料，味道独特', 'Beijing\'s unique fermented bean drink', 'beijing', 'food', 'rare', '/collections/beijing/douzhir.jpg', 15, 0.0800, '老北京文化的独特符号，体现了北京人的文化包容性'),

-- 北京建筑文化
('故宫', 'Forbidden City', '明清两代皇宫，现为故宫博物院', 'Imperial palace of Ming and Qing dynasties', 'beijing', 'architecture', 'legendary', '/collections/beijing/forbidden_city.jpg', 50, 0.0100, '中国古代宫殿建筑的巅峰之作，世界文化遗产'),
('天坛', 'Temple of Heaven', '明清皇帝祭天的建筑群', 'Imperial complex where emperors prayed for harvest', 'beijing', 'architecture', 'epic', '/collections/beijing/temple_of_heaven.jpg', 30, 0.0300, '中国古代建筑和祭祀文化的完美结合'),
('四合院', 'Siheyuan', '北京传统民居建筑', 'Traditional Beijing courtyard residence', 'beijing', 'architecture', 'rare', '/collections/beijing/siheyuan.jpg', 20, 0.0600, '北京传统文化和家族文化的重要载体'),

-- 北京历史文化
('长城', 'Great Wall', '中国古代军事防御工程', 'Ancient Chinese military defense project', 'beijing', 'history', 'legendary', '/collections/beijing/great_wall.jpg', 50, 0.0100, '中华民族的象征，人类建筑史上的奇迹'),
('京剧脸谱', 'Peking Opera Mask', '京剧表演艺术的重要组成部分', 'Essential element of Peking Opera performance', 'beijing', 'art', 'epic', '/collections/beijing/opera_mask.jpg', 25, 0.0400, '中国传统戏曲艺术的精华，非物质文化遗产'),

-- 上海文化图鉴
('小笼包', 'Xiaolongbao', '上海传统点心，皮薄汁多', 'Traditional Shanghai steamed buns', 'shanghai', 'food', 'rare', '/collections/shanghai/xiaolongbao.jpg', 15, 0.0800, '上海本帮菜的代表，体现了江南饮食文化的精致'),
('外滩', 'The Bund', '上海标志性的滨江景观带', 'Shanghai\'s iconic waterfront area', 'shanghai', 'architecture', 'legendary', '/collections/shanghai/bund.jpg', 50, 0.0100, '近代中国开埠通商的历史见证，东西文化交融的象征'),
('豫园', 'Yu Garden', '上海著名的古典园林', 'Famous classical garden in Shanghai', 'shanghai', 'architecture', 'epic', '/collections/shanghai/yu_garden.jpg', 30, 0.0300, '江南园林艺术的杰出代表'),
('上海中心大厦', 'Shanghai Tower', '上海现代化建筑的代表', 'Representative of Shanghai\'s modern architecture', 'shanghai', 'architecture', 'epic', '/collections/shanghai/shanghai_tower.jpg', 25, 0.0400, '中国现代建筑技术的巅峰之作');