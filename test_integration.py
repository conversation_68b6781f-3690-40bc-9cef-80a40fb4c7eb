#!/usr/bin/env python3
"""
前后端集成测试脚本
测试新的PRD系统API是否正常工作
"""
import requests
import json
import time
from pathlib import Path


class IntegrationTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.headers = {'Content-Type': 'application/json'}
        self.auth_token = None
    
    def test_api_endpoint(self, method, endpoint, data=None, expected_status=200):
        """测试API端点"""
        url = f"{self.base_url}{endpoint}"
        headers = self.headers.copy()
        
        if self.auth_token:
            headers['Authorization'] = f"Bearer {self.auth_token}"
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, params=data)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=headers, json=data)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=headers, json=data)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=headers)
            else:
                return False, f"不支持的HTTP方法: {method}"
            
            success = response.status_code == expected_status
            return success, {
                'status_code': response.status_code,
                'response': response.json() if response.content else None,
                'expected': expected_status
            }
        except requests.exceptions.ConnectionError:
            return False, "连接错误：后端服务未启动"
        except requests.exceptions.RequestException as e:
            return False, f"请求错误: {str(e)}"
        except json.JSONDecodeError:
            return False, f"JSON解析错误，状态码: {response.status_code}"
    
    def test_health_check(self):
        """测试健康检查"""
        print("🔍 测试健康检查...")
        success, result = self.test_api_endpoint("GET", "/health")
        if success:
            print("✓ 健康检查通过")
            return True
        else:
            print(f"✗ 健康检查失败: {result}")
            return False
    
    def test_guardian_system(self):
        """测试守护者系统API"""
        print("\n🛡️ 测试守护者系统...")
        
        # 测试经验值信息获取
        tests = [
            ("GET", "/api/v1/guardian/experience/info", None, "获取经验信息"),
            ("GET", "/api/v1/guardian/stamina/info", None, "获取体力信息"),
            ("POST", "/api/v1/guardian/experience/thief", {"thief_count": 1}, "抓捕小偷获得经验"),
            ("POST", "/api/v1/guardian/action/catch_thief", {"count": 1}, "抓捕小偷综合行为"),
        ]
        
        passed = 0
        for method, endpoint, data, description in tests:
            success, result = self.test_api_endpoint(method, endpoint, data, expected_status=401)  # 未认证会返回401
            if success or result.get('status_code') == 401:  # 401表示API存在但需要认证
                print(f"✓ {description}")
                passed += 1
            else:
                print(f"✗ {description}: {result}")
        
        return passed == len(tests)
    
    def test_task_system(self):
        """测试任务系统API"""
        print("\n📋 测试任务系统...")
        
        tests = [
            ("GET", "/api/v1/tasks/daily", None, "获取每日任务"),
            ("POST", "/api/v1/tasks/progress/thief", {"count": 1}, "更新抓捕小偷任务进度"),
            ("POST", "/api/v1/tasks/progress/rubbish", {"count": 1}, "更新清理垃圾任务进度"),
            ("POST", "/api/v1/tasks/progress/quiz", {"count": 1}, "更新文化问答任务进度"),
        ]
        
        passed = 0
        for method, endpoint, data, description in tests:
            success, result = self.test_api_endpoint(method, endpoint, data, expected_status=401)
            if success or result.get('status_code') == 401:
                print(f"✓ {description}")
                passed += 1
            else:
                print(f"✗ {description}: {result}")
        
        return passed == len(tests)
    
    def test_treasure_system(self):
        """测试宝箱系统API"""
        print("\n🎁 测试宝箱系统...")
        
        tests = [
            ("GET", "/api/v1/treasure/user/boxes", None, "获取用户宝箱列表"),
            ("GET", "/api/v1/treasure/config", None, "获取宝箱配置"),
            ("GET", "/api/v1/treasure/drop_rates", None, "获取宝箱掉落率"),
            ("POST", "/api/v1/treasure/trigger/thief", {"count": 1}, "触发小偷宝箱"),
        ]
        
        passed = 0
        for method, endpoint, data, description in tests:
            success, result = self.test_api_endpoint(method, endpoint, data, expected_status=401)
            if success or result.get('status_code') == 401:
                print(f"✓ {description}")
                passed += 1
            else:
                print(f"✗ {description}: {result}")
        
        return passed == len(tests)
    
    def test_cultural_system(self):
        """测试文化问答系统API"""
        print("\n📚 测试文化问答系统...")
        
        tests = [
            ("GET", "/api/v1/culture/quiz/random", None, "获取随机问答题目"),
            ("GET", "/api/v1/culture/artifacts", None, "获取用户文化图鉴"),
            ("GET", "/api/v1/culture/categories", None, "获取文化分类"),
            ("GET", "/api/v1/culture/difficulty_levels", None, "获取难度等级"),
        ]
        
        passed = 0
        for method, endpoint, data, description in tests:
            success, result = self.test_api_endpoint(method, endpoint, data, expected_status=401)
            if success or result.get('status_code') == 401:
                print(f"✓ {description}")
                passed += 1
            else:
                print(f"✗ {description}: {result}")
        
        return passed == len(tests)
    
    def test_existing_apis(self):
        """测试现有API是否仍然工作"""
        print("\n🔧 测试现有API...")
        
        tests = [
            ("GET", "/api/v1/health", None, "健康检查"),
            ("GET", "/api/v1/config", None, "获取配置"),
        ]
        
        passed = 0
        for method, endpoint, data, description in tests:
            success, result = self.test_api_endpoint(method, endpoint, data)
            if success:
                print(f"✓ {description}")
                passed += 1
            else:
                print(f"✗ {description}: {result}")
        
        return passed == len(tests)
    
    def run_all_tests(self):
        """运行所有测试"""
        print("《千亿像素城市寻宝》前后端集成测试")
        print("=" * 60)
        
        # 测试后端是否启动
        if not self.test_health_check():
            print("\n❌ 后端服务未启动，无法进行集成测试")
            print("请先启动后端服务：")
            print("  cd backend && python -m uvicorn app.main:app --reload")
            return False
        
        # 运行各系统测试
        results = []
        results.append(("现有API", self.test_existing_apis()))
        results.append(("守护者系统", self.test_guardian_system()))
        results.append(("任务系统", self.test_task_system()))
        results.append(("宝箱系统", self.test_treasure_system()))
        results.append(("文化问答系统", self.test_cultural_system()))
        
        # 生成测试报告
        print("\n" + "=" * 60)
        print("测试结果汇总:")
        print("=" * 60)
        
        total_passed = 0
        total_tests = len(results)
        
        for system_name, passed in results:
            status = "✓ 通过" if passed else "✗ 失败"
            print(f"{system_name:15} {status}")
            if passed:
                total_passed += 1
        
        print("-" * 60)
        print(f"总体结果: {total_passed}/{total_tests} 通过")
        
        if total_passed == total_tests:
            print("\n🎉 所有测试通过！前后端集成成功")
            print("游戏的新PRD系统已准备就绪")
            return True
        else:
            print(f"\n⚠️ {total_tests - total_passed} 个系统测试失败")
            print("请检查失败的API端点并修复问题")
            return False


def main():
    """主函数"""
    tester = IntegrationTester()
    
    print("注意: 此测试需要后端服务运行在 http://localhost:8000")
    print("如果后端运行在其他地址，请修改脚本中的base_url")
    print()
    
    success = tester.run_all_tests()
    
    if success:
        print("\n📝 下一步操作建议:")
        print("1. 运行数据库迁移脚本: python3 run_migration.py")
        print("2. 启动前端开发服务器测试完整游戏流程")
        print("3. 测试具体的游戏功能：抓捕小偷、清理垃圾、文化问答等")
    else:
        print("\n🔧 故障排除建议:")
        print("1. 确保后端服务正在运行")
        print("2. 检查数据库连接和迁移是否完成")
        print("3. 查看后端日志了解具体错误信息")
        print("4. 确认所有新的服务类都已正确导入")

if __name__ == "__main__":
    main()