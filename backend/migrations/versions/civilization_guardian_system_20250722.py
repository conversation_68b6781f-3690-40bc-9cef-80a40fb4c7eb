"""Civilization Guardian System Migration

Revision ID: civilization_guardian_20250722
Revises: 
Create Date: 2025-07-22

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from datetime import datetime

# revision identifiers, used by Alembic.
revision = 'civilization_guardian_20250722'
down_revision = None  # This will be updated to the latest revision
depends_on = None


def upgrade():
    """升级到文明守护者系统"""
    
    # =================================
    # 第一步：清理旧系统表和字段
    # =================================
    
    # 1. 删除大炮系统相关表（如果存在）
    op.execute("DROP TABLE IF EXISTS cannon_types")
    op.execute("DROP TABLE IF EXISTS user_cannons")
    op.execute("DROP TABLE IF EXISTS cannon_upgrades")
    op.execute("DROP TABLE IF EXISTS cannon_stats")
    op.execute("DROP TABLE IF EXISTS cannon_purchases")
    op.execute("DROP TABLE IF EXISTS cannon_levels")
    
    # 2. 删除货币系统相关表（如果存在）
    op.execute("DROP TABLE IF EXISTS user_transactions")
    op.execute("DROP TABLE IF EXISTS purchase_records")
    op.execute("DROP TABLE IF EXISTS shop_items")
    op.execute("DROP TABLE IF EXISTS payment_records")
    op.execute("DROP TABLE IF EXISTS currency_logs")
    op.execute("DROP TABLE IF EXISTS user_purchases")
    op.execute("DROP TABLE IF EXISTS store_products")
    
    # 3. 删除旧的游戏机制表（如果存在）
    op.execute("DROP TABLE IF EXISTS user_weapons")
    op.execute("DROP TABLE IF EXISTS weapon_upgrades")
    op.execute("DROP TABLE IF EXISTS combat_records")
    op.execute("DROP TABLE IF EXISTS battle_logs")
    op.execute("DROP TABLE IF EXISTS shooting_stats")
    
    # 4. 从用户表删除旧系统字段（如果存在）
    try:
        op.drop_column('users', 'gold')
    except:
        pass
    try:
        op.drop_column('users', 'silver')
    except:
        pass
    try:
        op.drop_column('users', 'diamond')
    except:
        pass
    try:
        op.drop_column('users', 'coins')
    except:
        pass
    try:
        op.drop_column('users', 'gems')
    except:
        pass
    try:
        op.drop_column('users', 'current_cannon')
    except:
        pass
    try:
        op.drop_column('users', 'cannon_level')
    except:
        pass
    try:
        op.drop_column('users', 'cannon_damage')
    except:
        pass
    try:
        op.drop_column('users', 'cannon_crit_rate')
    except:
        pass
    try:
        op.drop_column('users', 'cannon_accuracy')
    except:
        pass
    try:
        op.drop_column('users', 'cannon_fire_rate')
    except:
        pass
    try:
        op.drop_column('users', 'weapon_id')
    except:
        pass
    try:
        op.drop_column('users', 'ammo_count')
    except:
        pass
    try:
        op.drop_column('users', 'total_shots_fired')
    except:
        pass
    try:
        op.drop_column('users', 'total_hits')
    except:
        pass
    try:
        op.drop_column('users', 'accuracy_rate')
    except:
        pass
    try:
        op.drop_column('users', 'combat_level')
    except:
        pass
    try:
        op.drop_column('users', 'battle_wins')
    except:
        pass
    try:
        op.drop_column('users', 'battle_losses')
    except:
        pass
    
    # =================================
    # 第二步：添加文明守护者系统字段到用户表
    # =================================
    
    # 添加文明守护者系统字段
    op.add_column('users', sa.Column('civilization_exp', sa.Integer(), default=0, comment='文明经验值'))
    op.add_column('users', sa.Column('guardian_level', sa.Integer(), default=1, comment='守望者等级'))
    op.add_column('users', sa.Column('stamina', sa.Integer(), default=100, comment='体力值'))
    op.add_column('users', sa.Column('max_stamina', sa.Integer(), default=100, comment='最大体力值'))
    op.add_column('users', sa.Column('total_thieves_captured', sa.Integer(), default=0, comment='累计抓捕小偷数'))
    op.add_column('users', sa.Column('total_garbage_cleaned', sa.Integer(), default=0, comment='累计清理垃圾数'))
    op.add_column('users', sa.Column('total_monuments_protected', sa.Integer(), default=0, comment='累计保护古迹数'))
    op.add_column('users', sa.Column('collections_count', sa.Integer(), default=0, comment='图鉴收集数量'))
    op.add_column('users', sa.Column('last_stamina_recovery', sa.DateTime(), comment='最后体力恢复时间'))
    
    # =================================
    # 第三步：创建BOSS系统表
    # =================================
    
    # 1. 城市BOSS表
    op.create_table('city_bosses',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('city_id', sa.String(length=50), nullable=False, comment='城市标识'),
        sa.Column('boss_name', sa.String(length=100), nullable=False, comment='BOSS名称'),
        sa.Column('boss_name_en', sa.String(length=100), comment='BOSS英文名称'),
        sa.Column('max_hp', sa.Integer(), nullable=False, default=1000, comment='BOSS最大血量'),
        sa.Column('current_hp', sa.Integer(), nullable=False, default=1000, comment='BOSS当前血量'),
        sa.Column('is_defeated', sa.Boolean(), default=False, comment='是否被击败'),
        sa.Column('defeat_count', sa.Integer(), default=0, comment='被击败次数'),
        sa.Column('total_damage_received', sa.BigInteger(), default=0, comment='累计受到伤害'),
        sa.Column('avatar_url', sa.String(length=255), comment='BOSS头像URL'),
        sa.Column('background_url', sa.String(length=255), comment='BOSS背景图URL'),
        sa.Column('last_reset_time', sa.DateTime(), comment='最后重置时间'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_city_id', 'city_id'),
        sa.Index('idx_defeated_status', 'is_defeated'),
        comment='城市BOSS表'
    )
    
    # 2. BOSS对话表
    op.create_table('boss_dialogues',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('city_id', sa.String(length=50), nullable=False, comment='城市标识'),
        sa.Column('stage', sa.String(length=20), nullable=False, comment='对话阶段(100, 80, 60, 40, 20, 0)'),
        sa.Column('dialogue_text', sa.Text(), nullable=False, comment='对话内容'),
        sa.Column('dialogue_text_en', sa.Text(), comment='英文对话内容'),
        sa.Column('dialogue_type', sa.Enum('threat', 'anger', 'bribe', 'despair', 'defeat'), default='threat', comment='对话类型'),
        sa.Column('reward_exp', sa.Integer(), default=0, comment='对话奖励经验'),
        sa.Column('boss_avatar', sa.String(length=255), comment='BOSS表情头像URL'),
        sa.Column('is_active', sa.Boolean(), default=True, comment='是否启用'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_city_stage', 'city_id', 'stage'),
        sa.Index('idx_dialogue_type', 'dialogue_type'),
        comment='BOSS对话表'
    )
    
    # 3. 用户BOSS战斗记录表
    op.create_table('user_boss_battles',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
        sa.Column('city_id', sa.String(length=50), nullable=False, comment='城市标识'),
        sa.Column('damage_dealt', sa.Integer(), nullable=False, comment='造成伤害'),
        sa.Column('damage_source', sa.Enum('thief', 'garbage', 'monument_quiz', 'collection', 'manual'), nullable=False, comment='伤害来源'),
        sa.Column('source_id', sa.String(length=100), comment='来源标识'),
        sa.Column('exp_gained', sa.Integer(), default=0, comment='获得经验'),
        sa.Column('triggered_dialogue', sa.Boolean(), default=False, comment='是否触发对话'),
        sa.Column('dialogue_stage', sa.String(length=20), comment='触发的对话阶段'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_user_city', 'user_id', 'city_id'),
        sa.Index('idx_damage_source', 'damage_source'),
        sa.Index('idx_created_time', 'created_at'),
        comment='用户BOSS战斗记录表'
    )
    
    # =================================
    # 第四步：创建文化图鉴系统表
    # =================================
    
    # 1. 文化图鉴配置表
    op.create_table('cultural_collections',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('city_id', sa.String(length=50), nullable=False, comment='城市标识'),
        sa.Column('name', sa.String(length=100), nullable=False, comment='图鉴名称'),
        sa.Column('name_en', sa.String(length=100), comment='英文名称'),
        sa.Column('description', sa.Text(), comment='描述'),
        sa.Column('description_en', sa.Text(), comment='英文描述'),
        sa.Column('category', sa.Enum('food', 'architecture', 'culture', 'art', 'history', 'nature'), nullable=False, comment='分类'),
        sa.Column('rarity', sa.Enum('common', 'rare', 'epic', 'legendary'), default='common', comment='稀有度'),
        sa.Column('image_url', sa.String(length=255), comment='图片URL'),
        sa.Column('cultural_value', sa.Text(), comment='文化价值说明'),
        sa.Column('exp_reward', sa.Integer(), default=10, comment='收集奖励经验'),
        sa.Column('drop_rate', sa.Float(), default=0.1, comment='掉落概率'),
        sa.Column('unlock_level', sa.Integer(), default=1, comment='解锁等级'),
        sa.Column('is_active', sa.Boolean(), default=True, comment='是否启用'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_city_category', 'city_id', 'category'),
        sa.Index('idx_rarity_active', 'rarity', 'is_active'),
        sa.Index('idx_drop_rate', 'drop_rate'),
        comment='文化图鉴配置表'
    )
    
    # 2. 用户图鉴收集记录表
    op.create_table('user_collections',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
        sa.Column('collection_id', sa.Integer(), nullable=False, comment='图鉴ID'),
        sa.Column('collected_from', sa.Enum('thief', 'garbage', 'monument', 'treasure_box', 'achievement'), nullable=False, comment='收集来源'),
        sa.Column('source_location', sa.String(length=100), comment='收集位置'),
        sa.Column('exp_gained', sa.Integer(), default=0, comment='获得经验'),
        sa.Column('collected_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), comment='收集时间'),
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_user_collection', 'user_id', 'collection_id'),
        sa.Index('idx_collected_from', 'collected_from'),
        sa.Index('idx_collected_time', 'collected_at'),
        sa.UniqueConstraint('user_id', 'collection_id', name='uk_user_collection'),
        comment='用户图鉴收集记录表'
    )
    
    # =================================
    # 第五步：创建古迹问答系统表
    # =================================
    
    # 1. 古迹配置表
    op.create_table('monuments',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('city_id', sa.String(length=50), nullable=False, comment='城市标识'),
        sa.Column('monument_name', sa.String(length=100), nullable=False, comment='古迹名称'),
        sa.Column('monument_name_en', sa.String(length=100), comment='古迹英文名称'),
        sa.Column('location_description', sa.Text(), comment='位置描述'),
        sa.Column('location_description_en', sa.Text(), comment='位置英文描述'),
        sa.Column('polluted_image_url', sa.String(length=255), comment='被污染图片URL'),
        sa.Column('clean_image_url', sa.String(length=255), comment='清洁图片URL'),
        sa.Column('restoration_animation_url', sa.String(length=255), comment='清理动画URL'),
        sa.Column('difficulty_level', sa.Enum('easy', 'medium', 'hard'), default='medium', comment='难度等级'),
        sa.Column('questions_count', sa.Integer(), default=3, comment='问题数量'),
        sa.Column('required_correct', sa.Integer(), default=2, comment='及格答对数'),
        sa.Column('success_exp_reward', sa.Integer(), default=50, comment='成功奖励经验'),
        sa.Column('failure_exp_penalty', sa.Integer(), default=-10, comment='失败经验惩罚'),
        sa.Column('boss_damage_on_success', sa.Integer(), default=20, comment='成功对BOSS伤害'),
        sa.Column('time_limit_seconds', sa.Integer(), default=300, comment='答题时限(秒)'),
        sa.Column('unlock_level', sa.Integer(), default=1, comment='解锁等级'),
        sa.Column('is_active', sa.Boolean(), default=True, comment='是否启用'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_city_active', 'city_id', 'is_active'),
        sa.Index('idx_difficulty_level', 'difficulty_level'),
        comment='古迹配置表'
    )
    
    # 2. 古迹问题题库表
    op.create_table('monument_questions',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('monument_id', sa.Integer(), nullable=False, comment='古迹ID'),
        sa.Column('question_text', sa.Text(), nullable=False, comment='问题内容'),
        sa.Column('question_text_en', sa.Text(), comment='问题英文内容'),
        sa.Column('question_type', sa.Enum('single_choice', 'multiple_choice', 'true_false'), default='single_choice', comment='问题类型'),
        sa.Column('difficulty', sa.Enum('easy', 'medium', 'hard'), default='medium', comment='问题难度'),
        sa.Column('explanation', sa.Text(), comment='答案解释'),
        sa.Column('explanation_en', sa.Text(), comment='答案英文解释'),
        sa.Column('is_active', sa.Boolean(), default=True, comment='是否启用'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_monument_active', 'monument_id', 'is_active'),
        sa.Index('idx_question_type', 'question_type'),
        comment='古迹问题题库表'
    )
    
    # 3. 问题选项表
    op.create_table('monument_question_options',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('question_id', sa.Integer(), nullable=False, comment='问题ID'),
        sa.Column('option_text', sa.String(length=255), nullable=False, comment='选项内容'),
        sa.Column('option_text_en', sa.String(length=255), comment='选项英文内容'),
        sa.Column('is_correct', sa.Boolean(), default=False, comment='是否正确答案'),
        sa.Column('option_order', sa.Integer(), default=1, comment='选项顺序'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_question_order', 'question_id', 'option_order'),
        sa.Index('idx_correct_answer', 'question_id', 'is_correct'),
        comment='问题选项表'
    )
    
    # 4. 用户古迹挑战记录表
    op.create_table('user_monument_challenges',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
        sa.Column('monument_id', sa.Integer(), nullable=False, comment='古迹ID'),
        sa.Column('challenge_id', sa.String(length=100), nullable=False, comment='挑战ID'),
        sa.Column('questions_total', sa.Integer(), nullable=False, comment='总题目数'),
        sa.Column('questions_correct', sa.Integer(), default=0, comment='答对题目数'),
        sa.Column('is_success', sa.Boolean(), default=False, comment='是否成功'),
        sa.Column('time_spent_seconds', sa.Integer(), comment='答题耗时'),
        sa.Column('exp_gained', sa.Integer(), default=0, comment='获得经验'),
        sa.Column('boss_damage_dealt', sa.Integer(), default=0, comment='对BOSS造成伤害'),
        sa.Column('is_double_rewarded', sa.Boolean(), default=False, comment='是否观看广告获得双倍奖励'),
        sa.Column('double_reward_at', sa.DateTime(), comment='双倍奖励时间'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_user_monument', 'user_id', 'monument_id'),
        sa.Index('idx_challenge_id', 'challenge_id'),
        sa.Index('idx_success_time', 'is_success', 'created_at'),
        comment='用户古迹挑战记录表'
    )
    
    # =================================
    # 第六步：创建每日任务系统表
    # =================================
    
    # 1. 任务配置表
    op.create_table('task_configs',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('task_name', sa.String(length=100), nullable=False, comment='任务名称'),
        sa.Column('task_name_en', sa.String(length=100), comment='任务英文名称'),
        sa.Column('task_type', sa.Enum('catch_thieves', 'clean_garbage', 'collect_artifacts', 'protect_monuments', 'defeat_boss', 'login', 'special'), nullable=False, comment='任务类型'),
        sa.Column('description', sa.Text(), nullable=False, comment='任务描述'),
        sa.Column('description_en', sa.Text(), comment='任务英文描述'),
        sa.Column('target_count', sa.Integer(), nullable=False, default=1, comment='目标数量'),
        sa.Column('base_exp_reward', sa.Integer(), nullable=False, default=10, comment='基础经验奖励'),
        sa.Column('difficulty', sa.Enum('easy', 'medium', 'hard'), default='easy', comment='难度等级'),
        sa.Column('task_category', sa.Enum('daily', 'weekly', 'achievement'), default='daily', comment='任务分类'),
        sa.Column('is_repeatable', sa.Boolean(), default=True, comment='是否可重复'),
        sa.Column('unlock_guardian_level', sa.Integer(), default=1, comment='解锁需要的守望者等级'),
        sa.Column('is_active', sa.Boolean(), default=True, comment='是否启用'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_type_category', 'task_type', 'task_category'),
        sa.Index('idx_active_level', 'is_active', 'unlock_guardian_level'),
        comment='任务配置表'
    )
    
    # 2. 用户每日任务表
    op.create_table('user_daily_tasks',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
        sa.Column('task_config_id', sa.Integer(), nullable=False, comment='任务配置ID'),
        sa.Column('task_date', sa.Date(), nullable=False, comment='任务日期'),
        sa.Column('current_progress', sa.Integer(), default=0, comment='当前进度'),
        sa.Column('target_count', sa.Integer(), nullable=False, comment='目标数量'),
        sa.Column('is_completed', sa.Boolean(), default=False, comment='是否完成'),
        sa.Column('completed_at', sa.DateTime(), comment='完成时间'),
        sa.Column('exp_reward', sa.Integer(), nullable=False, comment='经验奖励'),
        sa.Column('is_double_rewarded', sa.Boolean(), default=False, comment='是否已领取双倍奖励'),
        sa.Column('double_reward_at', sa.DateTime(), comment='双倍奖励领取时间'),
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_user_date', 'user_id', 'task_date'),
        sa.Index('idx_date_completed', 'task_date', 'is_completed'),
        sa.UniqueConstraint('user_id', 'task_config_id', 'task_date', name='uk_user_task_date'),
        comment='用户每日任务表'
    )
    
    # 3. 任务进度记录表
    op.create_table('task_progress_logs',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('user_daily_task_id', sa.Integer(), nullable=False, comment='用户每日任务ID'),
        sa.Column('progress_increment', sa.Integer(), nullable=False, comment='进度增量'),
        sa.Column('source_type', sa.Enum('thief', 'garbage', 'monument', 'collection', 'boss', 'login', 'manual'), nullable=False, comment='进度来源'),
        sa.Column('source_id', sa.String(length=100), comment='来源标识'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id'),
        sa.Index('idx_task_time', 'user_daily_task_id', 'created_at'),
        sa.Index('idx_source_time', 'source_type', 'created_at'),
        comment='任务进度记录表'
    )
    
    # =================================
    # 第七步：插入初始数据
    # =================================
    
    # 插入默认BOSS数据
    op.execute("""
        INSERT INTO city_bosses (city_id, boss_name, boss_name_en, max_hp, current_hp, avatar_url, background_url) VALUES
        ('beijing', '垃圾大王·北京', 'Garbage King Beijing', 1000, 1000, '/bosses/beijing/avatar.png', '/bosses/beijing/background.jpg'),
        ('shanghai', '垃圾大王·上海', 'Garbage King Shanghai', 1000, 1000, '/bosses/shanghai/avatar.png', '/bosses/shanghai/background.jpg'),
        ('venice', '垃圾大王·威尼斯', 'Garbage King Venice', 1000, 1000, '/bosses/venice/avatar.png', '/bosses/venice/background.jpg')
    """)
    
    # 插入BOSS对话数据
    op.execute("""
        INSERT INTO boss_dialogues (city_id, stage, dialogue_text, dialogue_type, reward_exp, boss_avatar) VALUES
        ('beijing', '100', '哈哈哈！你这个弱小的守护者，我要把这座美丽的城市变成垃圾王国！', 'threat', 5, '/bosses/beijing/threat.png'),
        ('beijing', '80', '什么？你居然能伤到我？不可能！', 'anger', 10, '/bosses/beijing/angry.png'),
        ('beijing', '60', '可恶！你这个讨厌的守护者！我愿意分给你一半的垃圾王国，放过我吧！', 'bribe', 15, '/bosses/beijing/bribe.png'),
        ('beijing', '40', '不！我的力量在减弱...这不可能...', 'despair', 20, '/bosses/beijing/despair.png'),
        ('beijing', '20', '你...你不能这样对我...我是垃圾之王！', 'despair', 25, '/bosses/beijing/despair2.png'),
        ('beijing', '0', '算你厉害，我们先撤，但我还会回来的！', 'defeat', 50, '/bosses/beijing/defeat.png')
    """)
    
    # 插入默认任务配置
    op.execute("""
        INSERT INTO task_configs (task_name, task_name_en, task_type, description, description_en, target_count, base_exp_reward, difficulty) VALUES
        ('初级清道夫', 'Junior Street Cleaner', 'catch_thieves', '抓捕10个小偷，维护城市治安', 'Catch 10 thieves to maintain city security', 10, 50, 'easy'),
        ('中级守护者', 'Intermediate Guardian', 'catch_thieves', '抓捕50个小偷，成为合格的城市守护者', 'Catch 50 thieves to become a qualified city guardian', 50, 200, 'medium'),
        ('环保新手', 'Environmental Beginner', 'clean_garbage', '清理10个垃圾，保持环境整洁', 'Clean 10 pieces of garbage to keep the environment clean', 10, 40, 'easy'),
        ('文物收藏家', 'Artifact Collector', 'collect_artifacts', '收集1个文化图鉴，了解城市文化', 'Collect 1 cultural artifact to learn about city culture', 1, 60, 'easy'),
        ('古迹守护者', 'Monument Guardian', 'protect_monuments', '保护1个古迹，传承历史文明', 'Protect 1 monument to preserve historical civilization', 1, 80, 'medium'),
        ('每日签到', 'Daily Check-in', 'login', '每日登录游戏，获得签到奖励', 'Login daily to get check-in rewards', 1, 20, 'easy')
    """)


def downgrade():
    """降级回旧系统"""
    
    # 删除新系统的表
    op.drop_table('task_progress_logs')
    op.drop_table('user_daily_tasks')
    op.drop_table('task_configs')
    op.drop_table('user_monument_challenges')
    op.drop_table('monument_question_options')
    op.drop_table('monument_questions')
    op.drop_table('monuments')
    op.drop_table('user_collections')
    op.drop_table('cultural_collections')
    op.drop_table('user_boss_battles')
    op.drop_table('boss_dialogues')
    op.drop_table('city_bosses')
    
    # 删除用户表的新字段
    op.drop_column('users', 'last_stamina_recovery')
    op.drop_column('users', 'collections_count')
    op.drop_column('users', 'total_monuments_protected')
    op.drop_column('users', 'total_garbage_cleaned')
    op.drop_column('users', 'total_thieves_captured')
    op.drop_column('users', 'max_stamina')
    op.drop_column('users', 'stamina')
    op.drop_column('users', 'guardian_level')
    op.drop_column('users', 'civilization_exp')
    
    # 恢复旧系统字段
    op.add_column('users', sa.Column('gold', sa.Integer(), default=0, comment='金币数量'))
    op.add_column('users', sa.Column('diamond', sa.Integer(), default=0, comment='钻石数量'))
    op.add_column('users', sa.Column('ammo_count', sa.Integer(), default=0, comment='当前弹药数量'))