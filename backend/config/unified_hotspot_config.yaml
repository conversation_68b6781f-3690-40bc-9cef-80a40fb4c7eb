# 统一热点配置系统
# 支持全局默认 → 场景级别 → 精细化配置的继承体系

# ==================== 全局默认配置 ====================
global_defaults:
  # 默认奖励配置（所有热点类型的基础配置）
  rewards:
    thief:
      type: "treasure_box"
      contents:
    garbage:
      type: "treasure_box"
      contents:
    treasure:
      type: "treasure_box"
      contents:
    boss_thief:
      type: "treasure_box"
      contents:
  
  # 默认位置配置
  position:
    mode: "random"
    random_area:
      x_min: -180
      x_max: 180
      y_min: -90
      y_max: 90
    avoid_overlap:
      enabled: true
      min_distance: 0.01
  
  # 默认生成规则
  generation:
    mode: "batch"  # batch: 批量配置, precise: 精确配置, mixed: 混合配置
    total_hotspots: { min: 5, max: 8 }

# ==================== 场景配置 ====================
scenes:
  scene_level_1:
    # 场景信息
    info:
      name: "北京胡同第一关"
      description: "初级寻物场景"
      difficulty_level: 1
    
    # 场景级别配置（继承全局默认，可覆盖）
    scene_defaults:
      rewards:
        # thief:
       
        # garbage 和 treasure 继承全局默认
      generation:
        mode: "batch"
        total_hotspots: { min: 6, max: 6 }
    
    # 热点类型批量配置
    hotspot_types:
      thief:
        enabled: true
        count: 5
        available_names: ["thief_1", "thief_2", "thief_3", "thief_4", "thief_5"]
        # 继承 scene_defaults.rewards.thief 配置
        
      garbage:
        enabled: true
        count: 2
        available_names: ["garbage_1", "garbage_2"]
        # 可以在此处覆盖奖励配置
        reward_override:
          type: "treasure_box"
          contents:
           
        
      treasure:
        enabled: true
        count: 1
        available_names: ["treasure_1"]
        position_override:
          mode: "predefined"
          predefined_positions: [{ x: 117.5, y: 40.2 }]
    
    # 精细化配置（最高优先级，覆盖所有其他配置）
    precise_hotspots:
      # 如果需要精确控制特定热点，在此配置
      # thief_1:
      #   position: { x: 0.2, y: 0.3 }

  scene_level_2:
    info:
      name: "北京胡同第二关"
      description: "中级挑战场景"
      difficulty_level: 2
    
    # 第二关的场景默认配置
    scene_defaults:
      rewards:
        thief:
          type: "treasure_box"
          contents:
          
        boss_thief:
          type: "treasure_box"
          contents:
    
    hotspot_types:
      thief:
        enabled: true
        count: 4
        available_names: ["thief_lvl2_1", "thief_lvl2_2", "thief_lvl2_3", "thief_lvl2_4"]
        
      garbage:
        enabled: false  # 第二关没有垃圾
        
      treasure:
        enabled: false  # 第二关没有宝箱
        
      boss_thief:
        enabled: true
        count: 1
        available_names: ["boss_thief_main"]
        position_override:
          mode: "random"
          random_area: { x_min: 115.5, x_max: 117.5, y_min: 39.5, y_max: 40.5 }

# ==================== 特殊事件配置 ====================
events:
  christmas_special:
    # 圣诞节特殊配置，完全覆盖场景配置
    active: false  # 是否启用
    applicable_scenes: ["scene_level_1", "scene_level_2"]
    
    # 事件期间的全局覆盖
    global_overrides:
      rewards:
        thief:
          type: ""
          amount_range: { min: 50, max: 80 }  # 节日期间奖励翻倍
        treasure:
          type: ""
          amount_range: { min: 10, max: 20 }
    
    # 特殊热点（精确配置）
    special_hotspots:
      santa_thief:
        type: "thief"
        reward: { type: "", amount: 100 }
        position: { x: 0.5, y: 0.5 }
        replace_existing: "thief_1"  # 替换现有热点

# ==================== 城市特定配置 ====================
cities:
  beijing:
    difficulty_multiplier: 1.0
    position_variance: 0.05
    
  shanghai:
    difficulty_multiplier: 1.2    # 上海难度更高
    position_variance: 0.08
    # 城市级别的奖励倍数
    reward_multipliers:

# ==================== 配置继承说明 ====================
# 优先级（从高到低）：
# 1. precise_hotspots（精确热点配置）
# 2. events.special_hotspots（特殊事件热点）  
# 3. hotspot_types.reward_override（类型级别覆盖）
# 4. scene_defaults.rewards（场景级别默认）
# 5. global_defaults.rewards（全局默认）
#
# 奖励类型说明：
# - "treasure_box": 宝箱奖励，使用 contents 配置
#
# 宝箱配置示例：
# type: "treasure_box"
# contents:
#
# 使用示例：
# - 批量配置：只配置 hotspot_types，继承 scene_defaults
# - 精细配置：在 precise_hotspots 中配置特定热点
# - 混合配置：大部分用批量，少数用精细配置
# - 事件配置：临时启用 events，自动覆盖现有配置
#