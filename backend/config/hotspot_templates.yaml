# 热点模板配置
# 定义不同类型热点的基础模板

hotspot_templates:
  thief:
    name: "小偷"
    type: "thief"
    icon: "thief_icon.png"
    description: "狡猾的小偷，需要快速抓捕"
    base_rewards:
      exp: 5
    probability_weight: 0.4
    
  garbage:
    name: "垃圾军团"
    type: "garbage"
    icon: "garbage_icon.png"
    description: "垃圾军团成员，收集后可用作弹药"
    base_rewards:
      exp: 3
    probability_weight: 0.5
    
  treasure:
    name: "宝箱"
    type: "treasure"
    icon: "treasure_icon.png"
    description: "神秘宝箱，可能包含珍贵奖励"
    base_rewards:
      exp: 20
    probability_weight: 0.1

# 热点生成规则
generation_rules:
  max_hotspots_per_scene: 300
  min_distance_between_hotspots: 50
  respawn_interval: 3600  # 秒
  
# 奖励倍率配置
reward_multipliers:
  exp:
    min: 0.9
    max: 1.2
