#!/usr/bin/env python3
"""
分区维护脚本 - 定期创建新分区和清理旧分区
建议通过cron定期执行: 0 0 1 * * /path/to/this/script
"""
import asyncio
import logging
from datetime import datetime, timedelta
from sqlalchemy import text
from app.core.database import get_db

async def maintain_partitions():
    """维护分区 - 创建未来分区，删除过期分区"""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    async for db in get_db():
        try:
            # 计算下个月的分区
            next_month = datetime.now() + timedelta(days=32)
            next_partition = next_month.strftime("p%Y%m")
            next_value = next_month.strftime("%Y%m")
            
            tables = ['user_hotspot_collections', 'user_login_logs', 'game_sessions']
            
            for table in tables:
                # 检查分区是否已存在
                result = await db.execute(text(f"""
                    SELECT COUNT(*) FROM information_schema.partitions 
                    WHERE table_schema = DATABASE() 
                    AND table_name = '{table}'
                    AND partition_name = '{next_partition}'
                """))
                
                if result.scalar() == 0:
                    # 创建新分区
                    await db.execute(text(f"""
                        ALTER TABLE {table} 
                        REORGANIZE PARTITION p_future INTO (
                            PARTITION {next_partition} VALUES LESS THAN ({next_value}),
                            PARTITION p_future VALUES LESS THAN MAXVALUE
                        )
                    """))
                    logger.info(f"创建新分区: {table}.{next_partition}")
            
            # 删除6个月前的旧分区（可选）
            # old_month = datetime.now() - timedelta(days=180)
            # old_partition = old_month.strftime("p%Y%m")
            # await db.execute(text(f"ALTER TABLE {table} DROP PARTITION IF EXISTS {old_partition}"))
            
            await db.commit()
            logger.info("分区维护完成")
            
        except Exception as e:
            logger.error(f"分区维护失败: {e}")
        finally:
            break

if __name__ == "__main__":
    asyncio.run(maintain_partitions())
