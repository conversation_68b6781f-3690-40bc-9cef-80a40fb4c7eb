#!/usr/bin/env python3
"""
PRD合规性验证脚本
验证所有游戏系统是否完全符合PRD要求
"""

import yaml
from pathlib import Path
from typing import Dict, List, Any
import json


class PRDComplianceValidator:
    """PRD合规性验证器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.config_path = self.project_root / "config" / "game_config.yaml"
        self.prd_path = self.project_root.parent / "PRD_千亿像素城市寻宝.md"
        self.validation_results = []
        
    def load_config(self) -> Dict[str, Any]:
        """加载游戏配置"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def validate_stamina_system(self, config: Dict[str, Any]) -> bool:
        """验证体力系统是否符合PRD要求"""
        print("🔍 验证体力系统...")
        
        stamina_config = config.get('stamina_system', {})
        issues = []
        
        # PRD要求：最大体力120点
        if stamina_config.get('max_stamina') != 120:
            issues.append(f"最大体力应为120点，当前为{stamina_config.get('max_stamina')}")
        
        # PRD要求：每3分钟恢复1点体力
        if stamina_config.get('recovery_rate_minutes') != 3:
            issues.append(f"体力恢复速率应为3分钟/点，当前为{stamina_config.get('recovery_rate_minutes')}")
        
        # PRD要求：抓捕小偷和清理垃圾各消耗1点体力
        consumption = stamina_config.get('consumption_rates', {})
        if consumption.get('catch_thief') != 1:
            issues.append(f"抓捕小偷体力消耗应为1点，当前为{consumption.get('catch_thief')}")
        if consumption.get('clean_garbage') != 1:
            issues.append(f"清理垃圾体力消耗应为1点，当前为{consumption.get('clean_garbage')}")
        if consumption.get('cultural_quiz') != 5:
            issues.append(f"古迹问答体力消耗应为5点，当前为{consumption.get('cultural_quiz')}")
        
        # PRD要求：广告恢复30点体力，每小时3次
        ad_recovery = stamina_config.get('ad_recovery', {})
        if ad_recovery.get('stamina_amount') != 30:
            issues.append(f"广告恢复体力应为30点，当前为{ad_recovery.get('stamina_amount')}")
        if ad_recovery.get('hourly_limit') != 3:
            issues.append(f"广告恢复限制应为每小时3次，当前为{ad_recovery.get('hourly_limit')}")
        
        self.validation_results.append({
            'system': '体力系统',
            'compliant': len(issues) == 0,
            'issues': issues
        })
        
        return len(issues) == 0
    
    def validate_experience_system(self, config: Dict[str, Any]) -> bool:
        """验证经验系统是否符合PRD要求"""
        print("🔍 验证经验系统...")
        
        exp_config = config.get('experience_system', {})
        issues = []
        
        # PRD要求：基础经验值
        base_exp = exp_config.get('base_experience', {})
        expected_exp = {
            'catch_thief': 12,
            'clean_garbage': 8,
            'cultural_quiz': 35,
            'cultural_artifact': 25,
            'level_complete': 200
        }
        
        for activity, expected in expected_exp.items():
            actual = base_exp.get(activity)
            if actual != expected:
                issues.append(f"{activity}经验值应为{expected}点，当前为{actual}")
        
        # PRD要求：守护者等级1-10的具体配置
        guardian_levels = exp_config.get('guardian_levels', {})
        expected_levels = {
            1: {'required_exp': 100, 'passive_income': 2},
            2: {'required_exp': 150, 'passive_income': 3},
            3: {'required_exp': 250, 'passive_income': 5},
            4: {'required_exp': 500, 'passive_income': 8},
            5: {'required_exp': 1000, 'passive_income': 12}
        }
        
        for level, expected in expected_levels.items():
            actual = guardian_levels.get(level, {})
            if actual.get('required_exp') != expected['required_exp']:
                issues.append(f"等级{level}所需经验应为{expected['required_exp']}点，当前为{actual.get('required_exp')}")
            if actual.get('passive_income') != expected['passive_income']:
                issues.append(f"等级{level}被动收益应为{expected['passive_income']}点/分钟，当前为{actual.get('passive_income')}")
        
        self.validation_results.append({
            'system': '经验系统',
            'compliant': len(issues) == 0,
            'issues': issues
        })
        
        return len(issues) == 0
    
    def validate_treasure_box_system(self, config: Dict[str, Any]) -> bool:
        """验证宝箱系统是否符合PRD要求"""
        print("🔍 验证宝箱系统...")
        
        treasure_config = config.get('treasure_box_system', {})
        issues = []
        
        # PRD要求：宝箱掉落率
        drop_rates = treasure_config.get('drop_rates', {})
        expected_rates = {
            'catch_thief': {'drop_rate': 0.06},
            'clean_garbage': {'drop_rate': 0.03},
            'cultural_quiz': {'drop_rate': 0.15},
            'level_complete': {'drop_rate': 1.0}
        }
        
        for activity, expected in expected_rates.items():
            actual = drop_rates.get(activity, {})
            if actual.get('drop_rate') != expected['drop_rate']:
                issues.append(f"{activity}宝箱掉落率应为{expected['drop_rate']*100}%，当前为{actual.get('drop_rate', 0)*100}%")
        
        # PRD要求：宝箱奖励配置
        box_rewards = treasure_config.get('box_rewards', {})
        expected_rewards = {
            'copper': {'free_rewards': {'stamina': 5, 'items': 1}, 'ad_rewards': {'stamina': 10, 'items': 2}},
            'silver': {'free_rewards': {'stamina': 10, 'artifact': 1}, 'ad_rewards': {'stamina': 20, 'artifact': 2}},
            'gold': {'free_rewards': {'rare_artifact': 1, 'items': 1}, 'ad_rewards': {'rare_artifact': 2, 'items': 2}}
        }
        
        for box_type, expected in expected_rewards.items():
            actual = box_rewards.get(box_type, {})
            free_rewards = actual.get('free_rewards', {})
            ad_rewards = actual.get('ad_rewards', {})
            
            if free_rewards.get('stamina') != expected['free_rewards'].get('stamina'):
                issues.append(f"{box_type}宝箱基础体力奖励不符合PRD要求")
            if ad_rewards.get('stamina') != expected['ad_rewards'].get('stamina'):
                issues.append(f"{box_type}宝箱广告体力奖励不符合PRD要求")
        
        self.validation_results.append({
            'system': '宝箱系统',
            'compliant': len(issues) == 0,
            'issues': issues
        })
        
        return len(issues) == 0
    
    def validate_boss_health_system(self, config: Dict[str, Any]) -> bool:
        """验证BOSS血量系统是否符合PRD要求"""
        print("🔍 验证BOSS血量系统...")
        
        boss_config = config.get('boss_health_system', {})
        issues = []
        
        # PRD要求：BOSS血量与收集关系
        damage_mapping = boss_config.get('damage_mapping', {})
        expected_damage = {
            'catch_thief': {'health_damage': 2},
            'clean_garbage': {'health_damage': 1},
            'cultural_quiz': {'health_damage': 10}
        }
        
        for activity, expected in expected_damage.items():
            actual = damage_mapping.get(activity, {})
            if actual.get('health_damage') != expected['health_damage']:
                issues.append(f"{activity}对BOSS伤害应为{expected['health_damage']}%，当前为{actual.get('health_damage')}%")
        
        # PRD要求：BOSS对话阶段
        dialogues = boss_config.get('dialogues', {})
        required_phases = ['arrogant_phase', 'angry_phase', 'begging_phase', 'defeat_phase']
        
        for phase in required_phases:
            if phase not in dialogues:
                issues.append(f"缺少BOSS对话阶段: {phase}")
        
        self.validation_results.append({
            'system': 'BOSS血量系统',
            'compliant': len(issues) == 0,
            'issues': issues
        })
        
        return len(issues) == 0
    
    def validate_advertisement_system(self, config: Dict[str, Any]) -> bool:
        """验证广告系统是否符合PRD要求"""
        print("🔍 验证广告系统...")
        
        ad_config = config.get('advertisement_system', {})
        issues = []
        
        # PRD要求：广告触发场景
        trigger_scenarios = ad_config.get('trigger_scenarios', {})
        required_scenarios = [
            'stamina_recovery', 'treasure_box_double', 'quiz_experience_double',
            'task_reward_double', 'passive_income_double', 'level_up_boost'
        ]
        
        for scenario in required_scenarios:
            if scenario not in trigger_scenarios:
                issues.append(f"缺少广告触发场景: {scenario}")
        
        # PRD要求：体力恢复广告配置
        stamina_recovery = trigger_scenarios.get('stamina_recovery', {})
        if stamina_recovery.get('stamina_amount') != 30:
            issues.append(f"广告体力恢复应为30点，当前为{stamina_recovery.get('stamina_amount')}")
        if stamina_recovery.get('hourly_limit') != 3:
            issues.append(f"广告体力恢复限制应为每小时3次，当前为{stamina_recovery.get('hourly_limit')}")
        
        self.validation_results.append({
            'system': '广告系统',
            'compliant': len(issues) == 0,
            'issues': issues
        })
        
        return len(issues) == 0
    
    def generate_report(self) -> str:
        """生成验证报告"""
        report = ["=" * 60]
        report.append("🎯 PRD合规性验证报告")
        report.append("=" * 60)
        
        total_systems = len(self.validation_results)
        compliant_systems = sum(1 for result in self.validation_results if result['compliant'])
        
        report.append(f"\n📊 总体状态: {compliant_systems}/{total_systems} 系统完全合规")
        report.append(f"合规率: {(compliant_systems/total_systems)*100:.1f}%")
        
        for result in self.validation_results:
            status = "✅ 合规" if result['compliant'] else "❌ 不合规"
            report.append(f"\n{status} {result['system']}")
            
            if result['issues']:
                for issue in result['issues']:
                    report.append(f"  - {issue}")
        
        if compliant_systems == total_systems:
            report.append("\n🎉 所有系统完全符合PRD要求！")
        else:
            report.append(f"\n⚠️  需要修复 {total_systems - compliant_systems} 个系统的问题")
        
        return "\n".join(report)
    
    def run_validation(self) -> bool:
        """运行完整验证"""
        print("🚀 开始PRD合规性验证...")
        
        try:
            config = self.load_config()
            
            # 验证各个系统
            self.validate_stamina_system(config)
            self.validate_experience_system(config)
            self.validate_treasure_box_system(config)
            self.validate_boss_health_system(config)
            self.validate_advertisement_system(config)
            
            # 生成报告
            report = self.generate_report()
            print(report)
            
            # 保存报告
            report_path = self.project_root / "prd_compliance_report.txt"
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"\n📄 详细报告已保存至: {report_path}")
            
            # 返回是否完全合规
            return all(result['compliant'] for result in self.validation_results)
            
        except Exception as e:
            print(f"❌ 验证过程中出现错误: {e}")
            return False


def main():
    """主函数"""
    validator = PRDComplianceValidator()
    is_compliant = validator.run_validation()
    
    if is_compliant:
        print("\n🎊 恭喜！游戏实现完全符合PRD要求！")
        exit(0)
    else:
        print("\n🔧 请根据报告修复不合规的问题")
        exit(1)


if __name__ == "__main__":
    main()
