#!/usr/bin/env python3
"""
PRD配置更新脚本
将game_config.yaml中的PRD合规配置加载到数据库中
确保所有游戏机制完全符合PRD要求
"""

import asyncio
import sys
import yaml
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from app.core.database import get_db
from app.models.config import GameConfig
from sqlalchemy import select, delete


async def load_yaml_config():
    """加载YAML配置文件"""
    config_path = project_root / "config" / "game_config.yaml"
    
    if not config_path.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


async def update_database_config():
    """更新数据库配置"""
    print("🔄 开始更新PRD合规配置...")
    
    # 加载YAML配置
    config_data = await load_yaml_config()
    
    async for db in get_db():
        try:
            # 1. 清理旧的非PRD合规配置
            print("🧹 清理旧配置...")
            await db.execute(
                delete(GameConfig).where(
                    GameConfig.category.in_([
                        'cannon_upgrade',
                        'vip_system',
                        'complex_ammo',
                        'boss_battle',
                        'currency_exchange'
                    ])
                )
            )
            
            # 2. 更新核心系统配置
            core_configs = [
                {
                    'id': 'currency_system',
                    'name': '货币系统配置',
                    'category': 'core',
                    'data': config_data.get('currency', {}),
                    'description': 'PRD合规的货币系统配置'
                },
                {
                    'id': 'stamina_system',
                    'name': '体力系统配置',
                    'category': 'core',
                    'data': config_data.get('stamina_system', {}),
                    'description': 'PRD合规的体力系统配置'
                },
                {
                    'id': 'experience_system',
                    'name': '经验系统配置',
                    'category': 'core',
                    'data': config_data.get('experience_system', {}),
                    'description': 'PRD合规的经验系统配置'
                },
                {
                    'id': 'treasure_box_system',
                    'name': '宝箱系统配置',
                    'category': 'gameplay',
                    'data': config_data.get('treasure_box_system', {}),
                    'description': 'PRD合规的宝箱系统配置'
                },
                {
                    'id': 'boss_health_system',
                    'name': 'BOSS血量系统配置',
                    'category': 'gameplay',
                    'data': config_data.get('boss_health_system', {}),
                    'description': 'PRD合规的BOSS血量系统配置'
                },
                {
                    'id': 'daily_task_system',
                    'name': '每日任务系统配置',
                    'category': 'gameplay',
                    'data': config_data.get('daily_task_system', {}),
                    'description': 'PRD合规的每日任务系统配置'
                },
                {
                    'id': 'advertisement_system',
                    'name': '广告系统配置',
                    'category': 'monetization',
                    'data': config_data.get('advertisement_system', {}),
                    'description': 'PRD合规的广告系统配置'
                },
                {
                    'id': 'passive_income_system',
                    'name': '被动收益系统配置',
                    'category': 'progression',
                    'data': config_data.get('passive_income_system', {}),
                    'description': 'PRD合规的被动收益系统配置'
                },
                {
                    'id': 'cultural_education_system',
                    'name': '文化教育系统配置',
                    'category': 'education',
                    'data': config_data.get('cultural_education_system', {}),
                    'description': 'PRD合规的文化教育系统配置'
                },
                {
                    'id': 'ammunition_system',
                    'name': '弹药系统配置',
                    'category': 'gameplay',
                    'data': config_data.get('ammunition_system', {}),
                    'description': 'PRD合规的简单弹药系统配置'
                },
                {
                    'id': 'game_balance',
                    'name': '游戏平衡配置',
                    'category': 'balance',
                    'data': config_data.get('game_balance', {}),
                    'description': 'PRD合规的游戏平衡配置'
                }
            ]
            
            # 3. 插入或更新配置
            for config in core_configs:
                print(f"📝 更新配置: {config['name']}")
                
                # 检查是否存在
                result = await db.execute(
                    select(GameConfig).where(GameConfig.id == config['id'])
                )
                existing = result.scalar_one_or_none()
                
                if existing:
                    # 更新现有配置
                    existing.name = config['name']
                    existing.category = config['category']
                    existing.config_data = config['data']
                    existing.description = config['description']
                    existing.version = "1.0.0"
                    existing.is_active = True
                else:
                    # 创建新配置
                    new_config = GameConfig(
                        id=config['id'],
                        name=config['name'],
                        category=config['category'],
                        config_data=config['data'],
                        version="1.0.0",
                        is_active=True,
                        description=config['description']
                    )
                    db.add(new_config)
            
            # 4. 添加PRD合规性标记
            prd_compliance = {
                'id': 'prd_compliance',
                'name': 'PRD合规性标记',
                'category': 'system',
                'data': config_data.get('prd_compliance', {}),
                'description': 'PRD合规性验证和标记'
            }
            
            result = await db.execute(
                select(GameConfig).where(GameConfig.id == 'prd_compliance')
            )
            existing = result.scalar_one_or_none()
            
            if existing:
                existing.config_data = prd_compliance['data']
                existing.description = prd_compliance['description']
            else:
                new_config = GameConfig(
                    id=prd_compliance['id'],
                    name=prd_compliance['name'],
                    category=prd_compliance['category'],
                    config_data=prd_compliance['data'],
                    version="1.0.0",
                    is_active=True,
                    description=prd_compliance['description']
                )
                db.add(new_config)
            
            # 5. 提交更改
            await db.commit()
            print("✅ PRD合规配置更新完成!")
            
            # 6. 验证配置
            result = await db.execute(select(GameConfig))
            configs = result.scalars().all()
            
            print(f"\n📊 当前配置统计:")
            categories = {}
            for config in configs:
                categories[config.category] = categories.get(config.category, 0) + 1
            
            for category, count in categories.items():
                print(f"  - {category}: {count}个配置")
            
            print(f"\n🎯 PRD合规性状态: {config_data.get('prd_compliance', {}).get('compliance_status', 'UNKNOWN')}")
            
            break
            
        except Exception as e:
            print(f"❌ 配置更新失败: {e}")
            await db.rollback()
            raise


async def main():
    """主函数"""
    try:
        await update_database_config()
        print("\n🎉 PRD合规配置更新成功完成!")
    except Exception as e:
        print(f"\n💥 更新失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
