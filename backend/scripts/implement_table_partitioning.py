#!/usr/bin/env python3
"""
表分区实施脚本
对高频写入的大表实施分区策略，提升查询和写入性能
"""
import asyncio
import sys
import logging
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到 Python 路径
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import text
from app.core.database import get_db
from app.core.database_optimization import mysql_optimizer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TablePartitioner:
    """表分区实施器"""
    
    def __init__(self):
        self.partitioned_tables = []
        
    async def implement_partitioning(self):
        """实施表分区策略"""
        logger.info("🚀 开始实施表分区优化...")
        
        async for db in get_db():
            try:
                # 1. 备份数据前先检查表结构
                await self._check_table_structure(db)
                
                # 2. 实施热点收集表分区（按月分区）
                await self._partition_user_hotspot_collections(db)
                
                # 3. 实施登录日志表分区（按月分区）
                await self._partition_user_login_logs(db)
                
                # 4. 实施游戏会话表分区（按月分区）
                await self._partition_game_sessions(db)
                
                # 5. 验证分区效果
                await self._verify_partitions(db)
                
                logger.info("✅ 表分区优化完成")
                
            except Exception as e:
                logger.error(f"❌ 表分区实施失败: {e}")
                raise
            finally:
                break
    
    async def _check_table_structure(self, db):
        """检查表结构和数据量"""
        logger.info("📊 检查表结构和数据量...")
        
        tables_to_check = [
            'user_hotspot_collections',
            'user_login_logs', 
            'game_sessions',
            'ad_watch_records'
        ]
        
        for table in tables_to_check:
            try:
                # 检查表是否存在
                result = await db.execute(text(f"""
                    SELECT COUNT(*) as row_count,
                           ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE() 
                    AND table_name = '{table}'
                """))
                
                row = result.fetchone()
                if row and row.row_count is not None:
                    logger.info(f"📋 表 {table}: {row.row_count} 行, {row.size_mb or 0} MB")
                else:
                    logger.info(f"📋 表 {table}: 不存在或无数据")
                    
            except Exception as e:
                logger.warning(f"检查表 {table} 失败: {e}")
    
    async def _partition_user_hotspot_collections(self, db):
        """分区用户热点收集表"""
        logger.info("🔧 开始分区 user_hotspot_collections 表...")
        
        try:
            # 检查表是否已经分区
            result = await db.execute(text("""
                SELECT COUNT(*) as partition_count
                FROM information_schema.partitions 
                WHERE table_schema = DATABASE() 
                AND table_name = 'user_hotspot_collections'
                AND partition_name IS NOT NULL
            """))
            
            partition_count = result.scalar()
            if partition_count > 0:
                logger.info(f"表已存在 {partition_count} 个分区，跳过分区创建")
                return
            
            # 创建分区（按月分区）
            partition_sql = """
            ALTER TABLE user_hotspot_collections 
            PARTITION BY RANGE (YEAR(collected_at) * 100 + MONTH(collected_at)) (
                PARTITION p202501 VALUES LESS THAN (202502),
                PARTITION p202502 VALUES LESS THAN (202503),
                PARTITION p202503 VALUES LESS THAN (202504),
                PARTITION p202504 VALUES LESS THAN (202505),
                PARTITION p202505 VALUES LESS THAN (202506),
                PARTITION p202506 VALUES LESS THAN (202507),
                PARTITION p202507 VALUES LESS THAN (202508),
                PARTITION p202508 VALUES LESS THAN (202509),
                PARTITION p202509 VALUES LESS THAN (202510),
                PARTITION p202510 VALUES LESS THAN (202511),
                PARTITION p202511 VALUES LESS THAN (202512),
                PARTITION p202512 VALUES LESS THAN (202601),
                PARTITION p202601 VALUES LESS THAN (202602),
                PARTITION p202602 VALUES LESS THAN (202603),
                PARTITION p202603 VALUES LESS THAN (202604),
                PARTITION p202604 VALUES LESS THAN (202605),
                PARTITION p202605 VALUES LESS THAN (202606),
                PARTITION p202606 VALUES LESS THAN (202607),
                PARTITION p_future VALUES LESS THAN MAXVALUE
            )
            """
            
            await db.execute(text(partition_sql))
            await db.commit()
            
            self.partitioned_tables.append('user_hotspot_collections')
            logger.info("✅ user_hotspot_collections 表分区创建成功")
            
        except Exception as e:
            logger.error(f"❌ 分区 user_hotspot_collections 失败: {e}")
            # 如果分区失败，继续其他表的分区
            pass
    
    async def _partition_user_login_logs(self, db):
        """分区用户登录日志表"""
        logger.info("🔧 开始分区 user_login_logs 表...")
        
        try:
            # 检查表是否已经分区
            result = await db.execute(text("""
                SELECT COUNT(*) as partition_count
                FROM information_schema.partitions 
                WHERE table_schema = DATABASE() 
                AND table_name = 'user_login_logs'
                AND partition_name IS NOT NULL
            """))
            
            partition_count = result.scalar()
            if partition_count > 0:
                logger.info(f"表已存在 {partition_count} 个分区，跳过分区创建")
                return
            
            # 创建分区（按月分区）
            partition_sql = """
            ALTER TABLE user_login_logs 
            PARTITION BY RANGE (YEAR(login_time) * 100 + MONTH(login_time)) (
                PARTITION p202501 VALUES LESS THAN (202502),
                PARTITION p202502 VALUES LESS THAN (202503),
                PARTITION p202503 VALUES LESS THAN (202504),
                PARTITION p202504 VALUES LESS THAN (202505),
                PARTITION p202505 VALUES LESS THAN (202506),
                PARTITION p202506 VALUES LESS THAN (202507),
                PARTITION p202507 VALUES LESS THAN (202508),
                PARTITION p202508 VALUES LESS THAN (202509),
                PARTITION p202509 VALUES LESS THAN (202510),
                PARTITION p202510 VALUES LESS THAN (202511),
                PARTITION p202511 VALUES LESS THAN (202512),
                PARTITION p202512 VALUES LESS THAN (202601),
                PARTITION p202601 VALUES LESS THAN (202602),
                PARTITION p202602 VALUES LESS THAN (202603),
                PARTITION p202603 VALUES LESS THAN (202604),
                PARTITION p202604 VALUES LESS THAN (202605),
                PARTITION p202605 VALUES LESS THAN (202606),
                PARTITION p202606 VALUES LESS THAN (202607),
                PARTITION p_future VALUES LESS THAN MAXVALUE
            )
            """
            
            await db.execute(text(partition_sql))
            await db.commit()
            
            self.partitioned_tables.append('user_login_logs')
            logger.info("✅ user_login_logs 表分区创建成功")
            
        except Exception as e:
            logger.error(f"❌ 分区 user_login_logs 失败: {e}")
            pass
    
    async def _partition_game_sessions(self, db):
        """分区游戏会话表"""
        logger.info("🔧 开始分区 game_sessions 表...")
        
        try:
            # 检查表是否已经分区
            result = await db.execute(text("""
                SELECT COUNT(*) as partition_count
                FROM information_schema.partitions 
                WHERE table_schema = DATABASE() 
                AND table_name = 'game_sessions'
                AND partition_name IS NOT NULL
            """))
            
            partition_count = result.scalar()
            if partition_count > 0:
                logger.info(f"表已存在 {partition_count} 个分区，跳过分区创建")
                return
            
            # 创建分区（按月分区）
            partition_sql = """
            ALTER TABLE game_sessions 
            PARTITION BY RANGE (YEAR(started_at) * 100 + MONTH(started_at)) (
                PARTITION p202501 VALUES LESS THAN (202502),
                PARTITION p202502 VALUES LESS THAN (202503),
                PARTITION p202503 VALUES LESS THAN (202504),
                PARTITION p202504 VALUES LESS THAN (202505),
                PARTITION p202505 VALUES LESS THAN (202506),
                PARTITION p202506 VALUES LESS THAN (202507),
                PARTITION p202507 VALUES LESS THAN (202508),
                PARTITION p202508 VALUES LESS THAN (202509),
                PARTITION p202509 VALUES LESS THAN (202510),
                PARTITION p202510 VALUES LESS THAN (202511),
                PARTITION p202511 VALUES LESS THAN (202512),
                PARTITION p202512 VALUES LESS THAN (202601),
                PARTITION p202601 VALUES LESS THAN (202602),
                PARTITION p202602 VALUES LESS THAN (202603),
                PARTITION p202603 VALUES LESS THAN (202604),
                PARTITION p202604 VALUES LESS THAN (202605),
                PARTITION p202605 VALUES LESS THAN (202606),
                PARTITION p202606 VALUES LESS THAN (202607),
                PARTITION p_future VALUES LESS THAN MAXVALUE
            )
            """
            
            await db.execute(text(partition_sql))
            await db.commit()
            
            self.partitioned_tables.append('game_sessions')
            logger.info("✅ game_sessions 表分区创建成功")
            
        except Exception as e:
            logger.error(f"❌ 分区 game_sessions 失败: {e}")
            pass
    
    async def _verify_partitions(self, db):
        """验证分区效果"""
        logger.info("🔍 验证分区效果...")
        
        for table in self.partitioned_tables:
            try:
                # 查询分区信息
                result = await db.execute(text(f"""
                    SELECT partition_name, table_rows, 
                           ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                    FROM information_schema.partitions 
                    WHERE table_schema = DATABASE() 
                    AND table_name = '{table}'
                    AND partition_name IS NOT NULL
                    ORDER BY partition_ordinal_position
                """))
                
                partitions = result.fetchall()
                logger.info(f"📊 表 {table} 分区信息:")
                for partition in partitions:
                    logger.info(f"  - {partition.partition_name}: {partition.table_rows or 0} 行, {partition.size_mb or 0} MB")
                    
            except Exception as e:
                logger.warning(f"验证表 {table} 分区失败: {e}")
    
    async def create_partition_maintenance_script(self):
        """创建分区维护脚本"""
        logger.info("📝 创建分区维护脚本...")
        
        maintenance_script = '''#!/usr/bin/env python3
"""
分区维护脚本 - 定期创建新分区和清理旧分区
建议通过cron定期执行: 0 0 1 * * /path/to/this/script
"""
import asyncio
import logging
from datetime import datetime, timedelta
from sqlalchemy import text
from app.core.database import get_db

async def maintain_partitions():
    """维护分区 - 创建未来分区，删除过期分区"""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    async for db in get_db():
        try:
            # 计算下个月的分区
            next_month = datetime.now() + timedelta(days=32)
            next_partition = next_month.strftime("p%Y%m")
            next_value = next_month.strftime("%Y%m")
            
            tables = ['user_hotspot_collections', 'user_login_logs', 'game_sessions']
            
            for table in tables:
                # 检查分区是否已存在
                result = await db.execute(text(f"""
                    SELECT COUNT(*) FROM information_schema.partitions 
                    WHERE table_schema = DATABASE() 
                    AND table_name = '{table}'
                    AND partition_name = '{next_partition}'
                """))
                
                if result.scalar() == 0:
                    # 创建新分区
                    await db.execute(text(f"""
                        ALTER TABLE {table} 
                        REORGANIZE PARTITION p_future INTO (
                            PARTITION {next_partition} VALUES LESS THAN ({next_value}),
                            PARTITION p_future VALUES LESS THAN MAXVALUE
                        )
                    """))
                    logger.info(f"创建新分区: {table}.{next_partition}")
            
            # 删除6个月前的旧分区（可选）
            # old_month = datetime.now() - timedelta(days=180)
            # old_partition = old_month.strftime("p%Y%m")
            # await db.execute(text(f"ALTER TABLE {table} DROP PARTITION IF EXISTS {old_partition}"))
            
            await db.commit()
            logger.info("分区维护完成")
            
        except Exception as e:
            logger.error(f"分区维护失败: {e}")
        finally:
            break

if __name__ == "__main__":
    asyncio.run(maintain_partitions())
'''
        
        script_path = Path(__file__).parent / "maintain_partitions.py"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(maintenance_script)
        
        logger.info(f"✅ 分区维护脚本已创建: {script_path}")


async def main():
    """主函数"""
    partitioner = TablePartitioner()
    
    try:
        await partitioner.implement_partitioning()
        await partitioner.create_partition_maintenance_script()
        
        logger.info("🎉 表分区优化全部完成！")
        logger.info("📋 优化建议:")
        logger.info("  1. 定期运行维护脚本创建新分区")
        logger.info("  2. 监控分区大小，必要时调整分区策略")
        logger.info("  3. 考虑删除过期分区以节省存储空间")
        
    except Exception as e:
        logger.error(f"分区优化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main()) 