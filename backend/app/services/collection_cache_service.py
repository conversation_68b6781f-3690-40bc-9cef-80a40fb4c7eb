"""
收集缓存服务 - 优化高频收集操作的性能
"""
import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from collections import defaultdict

from app.core.redis_client import redis_manager
from app.core.database import get_db
from app.models.game import UserHotspotCollection
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import insert

logger = logging.getLogger(__name__)


class CollectionCacheService:
    """收集缓存服务"""
    
    def __init__(self):
        self.collection_queue = asyncio.Queue(maxsize=10000)
        self.batch_size = 100
        self.batch_timeout = 5  # 5秒超时批量处理
        self.is_processing = False
        
    async def start_batch_processor(self):
        """启动批量处理器"""
        if not self.is_processing:
            self.is_processing = True
            asyncio.create_task(self._batch_processor())
            logger.info("收集缓存批量处理器已启动")
    
    async def cache_collection(
        self, 
        user_id: int, 
        session_id: str, 
        hotspot_id: str, 
        hotspot_type: str,
        hotspot_name: str,
        city_id: str,
        scene_id: str
    ) -> Dict[str, Any]:
        """
        缓存收集操作 - 快速响应，异步持久化
        """
        try:
            # 1. 检查是否已收集（防重复）
            hotspot_key = f"hotspot:{session_id}:{hotspot_id}"
            is_collected = await redis_manager.hget(hotspot_key, "collected")
            if is_collected:
                return {"error": "热点已收集"}
            
            # 2. 更新Redis缓存
            session_key = f"session:{session_id}:collections"
            
            # 更新收集计数
            if hotspot_type in ["thief", "boss_thief"]:
                new_count = await redis_manager.hincrby(session_key, "thieves_collected", 1)
                collection_type = "thieves_collected"
            elif hotspot_type == "garbage":
                new_count = await redis_manager.hincrby(session_key, "garbage_collected", 1)
                collection_type = "garbage_collected"
            else:
                new_count = 1
                collection_type = "other"
            
            # 标记热点已收集
            await redis_manager.hset(hotspot_key, "collected", True)
            await redis_manager.hset(hotspot_key, "collected_at", datetime.utcnow().isoformat())
            await redis_manager.hset(hotspot_key, "user_id", user_id)
            
            # 设置过期时间（会话结束后清理）
            await redis_manager.expire(hotspot_key, 7200)  # 2小时
            await redis_manager.expire(session_key, 7200)
            
            # 3. 加入异步处理队列
            collection_data = {
                "user_id": user_id,
                "session_id": session_id,
                "hotspot_id": hotspot_id,
                "hotspot_name": hotspot_name,
                "hotspot_type": hotspot_type,
                "city_id": city_id,
                "scene_id": scene_id,
                "collected_at": datetime.utcnow().isoformat(),
                "collection_type": collection_type
            }
            
            # 非阻塞入队
            try:
                self.collection_queue.put_nowait(collection_data)
            except asyncio.QueueFull:
                logger.warning("收集队列已满，丢弃部分数据")
            
            # 4. 返回缓存结果
            cached_progress = await self.get_cached_progress(session_id, user_id)
            
            return {
                "success": True,
                "hotspot": {
                    "id": hotspot_id,
                    "name": hotspot_name,
                    "type": hotspot_type
                },
                "progress": cached_progress,
                "new_count": new_count,
                "cached": True  # 标识这是缓存响应
            }
            
        except Exception as e:
            logger.error(f"缓存收集操作失败: {e}")
            return {"error": f"缓存操作失败: {str(e)}"}
    
    async def get_cached_progress(self, session_id: str, user_id: int) -> Dict[str, Any]:
        """获取缓存的进度数据"""
        try:
            session_key = f"session:{session_id}:collections"
            cached_data = await redis_manager.hgetall(session_key)
            
            thieves = int(cached_data.get("thieves_collected", 0))
            garbage = int(cached_data.get("garbage_collected", 0))
            total_ammo = thieves + garbage
            
            return {
                "thieves_collected": thieves,
                "garbage_collected": garbage,
                "ammo_count": total_ammo,
                "total_ammo": total_ammo,
                "can_charge_attack": total_ammo >= 10,
                "synthesis_progress": 0,  # 新系统无合成概念
                "gold": 0,  # 需要从用户数据获取
                "diamond": 0,  # 需要从用户数据获取
                "cached": True
            }
            
        except Exception as e:
            logger.error(f"获取缓存进度失败: {e}")
            return {
                "thieves_collected": 0,
                "garbage_collected": 0,
                "ammo_count": 0,
                "total_ammo": 0,
                "can_charge_attack": False,
                "synthesis_progress": 0,
                "cached": False
            }
    
    async def _batch_processor(self):
        """批量处理收集数据"""
        logger.info("开始批量处理收集数据")
        
        while self.is_processing:
            batch = []
            start_time = datetime.utcnow()
            
            try:
                # 收集一批数据
                while len(batch) < self.batch_size:
                    try:
                        # 计算剩余等待时间
                        elapsed = (datetime.utcnow() - start_time).total_seconds()
                        remaining_timeout = max(0, self.batch_timeout - elapsed)
                        
                        if remaining_timeout <= 0:
                            break
                            
                        item = await asyncio.wait_for(
                            self.collection_queue.get(), 
                            timeout=remaining_timeout
                        )
                        batch.append(item)
                        
                    except asyncio.TimeoutError:
                        break
                
                # 批量写入数据库
                if batch:
                    await self._batch_insert_collections(batch)
                    logger.info(f"批量处理了 {len(batch)} 条收集记录")
                    
            except Exception as e:
                logger.error(f"批量处理失败: {e}")
                # 等待一段时间后重试
                await asyncio.sleep(1)
    
    async def _batch_insert_collections(self, batch: List[Dict[str, Any]]):
        """批量插入收集记录"""
        if not batch:
            return
            
        try:
            async with get_db() as db:
                # 准备批量插入数据
                insert_data = []
                for item in batch:
                    insert_data.append({
                        "user_id": item["user_id"],
                        "city_id": item["city_id"],
                        "scene_id": item["scene_id"],
                        "hotspot_name": item["hotspot_name"],
                        "hotspot_type": item["hotspot_type"],
                        "collected_at": datetime.fromisoformat(item["collected_at"].replace('Z', '+00:00'))
                    })
                
                # 批量插入（忽略重复）
                stmt = insert(UserHotspotCollection).values(insert_data)
                # 使用 ON DUPLICATE KEY UPDATE 或类似机制处理重复
                await db.execute(stmt)
                await db.commit()
                
                logger.info(f"成功批量插入 {len(insert_data)} 条收集记录")
                
        except Exception as e:
            logger.error(f"批量插入失败: {e}")
    
    async def sync_cache_to_database(self, user_id: int, session_id: str):
        """将缓存数据同步到数据库（会话结束时调用）"""
        try:
            session_key = f"session:{session_id}:collections"
            cached_data = await redis_manager.hgetall(session_key)
            
            if cached_data:
                async with get_db() as db:
                    # 更新用户总收集数
                    from app.models.user import User
                    from sqlalchemy import select, update
                    
                    # 获取用户当前数据
                    result = await db.execute(select(User).where(User.id == user_id))
                    user = result.scalar_one_or_none()
                    
                    if user:
                        # 累加收集数
                        thieves_inc = int(cached_data.get("thieves_collected", 0))
                        garbage_inc = int(cached_data.get("garbage_collected", 0))
                        
                        await db.execute(
                            update(User).where(User.id == user_id).values(
                                thieves_collected=(user.thieves_collected or 0) + thieves_inc,
                                garbage_collected=(user.garbage_collected or 0) + garbage_inc
                            )
                        )
                        await db.commit()
                        
                        logger.info(f"用户 {user_id} 缓存数据已同步到数据库")
                
        except Exception as e:
            logger.error(f"同步缓存到数据库失败: {e}")
    
    async def get_user_session_stats(self, user_id: int, session_id: str) -> Dict[str, Any]:
        """获取用户会话统计（优先从缓存读取）"""
        try:
            session_key = f"session:{session_id}:collections"
            cached_data = await redis_manager.hgetall(session_key)
            
            if cached_data:
                return {
                    "thieves_collected": int(cached_data.get("thieves_collected", 0)),
                    "garbage_collected": int(cached_data.get("garbage_collected", 0)),
                    "source": "cache"
                }
            else:
                # 缓存未命中，从数据库读取
                async with get_db() as db:
                    from app.models.user import User
                    from sqlalchemy import select
                    
                    result = await db.execute(select(User).where(User.id == user_id))
                    user = result.scalar_one_or_none()
                    
                    if user:
                        return {
                            "thieves_collected": user.thieves_collected or 0,
                            "garbage_collected": user.garbage_collected or 0,
                            "source": "database"
                        }
                    
                return {
                    "thieves_collected": 0,
                    "garbage_collected": 0,
                    "source": "default"
                }
                
        except Exception as e:
            logger.error(f"获取用户会话统计失败: {e}")
            return {
                "thieves_collected": 0,
                "garbage_collected": 0,
                "source": "error"
            }
    
    async def stop_batch_processor(self):
        """停止批量处理器"""
        self.is_processing = False
        logger.info("收集缓存批量处理器已停止")


# 全局实例
collection_cache_service = CollectionCacheService() 