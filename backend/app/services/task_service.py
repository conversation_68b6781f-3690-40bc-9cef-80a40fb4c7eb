"""
增强任务服务模块
处理每日任务、任务进度、奖励领取、登录奖励等功能
"""
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
from sqlalchemy import select, and_, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import game_config
from app.models.user import User
from app.models.daily_task import (
    DailyTaskTemplate, UserDailyTask, TaskProgressLog, TaskRewardHistory,
    DailyTaskProgress, DailyTaskExperience, DailyTaskRewardLevel
)
# 🚫 PRD合规性清理：需要创建缺失的任务进度模型或简化任务系统
from app.analytics.metrics import analytics_service
import logging

logger = logging.getLogger(__name__)


class TaskService:
    """任务服务类"""
    
    def __init__(self):
        self.daily_tasks = self._load_daily_tasks()
        self.reward_levels = self._load_reward_levels()
        self._last_config_check = datetime.utcnow()
    
    async def reload_config(self):
        """重新加载配置（支持热更新）"""
        try:
            # 重新加载配置文件
            global game_config
            from app.core.config_manager import load_game_config
            game_config = load_game_config()
            
            # 保存旧配置用于兼容性检查
            old_tasks = self.daily_tasks.copy()
            old_reward_levels = self.reward_levels.copy()
            
            # 重新加载任务配置
            self.daily_tasks = self._load_daily_tasks()
            self.reward_levels = self._load_reward_levels()
            self._last_config_check = datetime.utcnow()
            
            # 记录配置变更
            changes = self._detect_config_changes(old_tasks, old_reward_levels)
            
            logger.info(f"Task configuration reloaded successfully. Changes: {changes}")
            return {
                "success": True, 
                "message": "配置重新加载成功",
                "changes": changes,
                "timestamp": self._last_config_check.isoformat()
            }
        except Exception as e:
            logger.error(f"Failed to reload config: {e}")
            return {"error": f"配置重新加载失败: {str(e)}"}
    
    def _detect_config_changes(self, old_tasks: List[Dict], old_reward_levels: List[Dict]) -> Dict[str, Any]:
        """检测配置变更"""
        changes = {
            "tasks": {
                "added": [],
                "modified": [],
                "removed": []
            },
            "reward_levels": {
                "added": [],
                "modified": [],
                "removed": []
            }
        }
        
        # 检测任务变更
        old_task_ids = {task["task_id"] for task in old_tasks}
        new_task_ids = {task["task_id"] for task in self.daily_tasks}
        
        changes["tasks"]["added"] = list(new_task_ids - old_task_ids)
        changes["tasks"]["removed"] = list(old_task_ids - new_task_ids)
        
        # 检测任务修改
        for task in self.daily_tasks:
            task_id = task["task_id"]
            old_task = next((t for t in old_tasks if t["task_id"] == task_id), None)
            if old_task and task != old_task:
                changes["tasks"]["modified"].append({
                    "task_id": task_id,
                    "old": old_task,
                    "new": task
                })
        
        # 检测奖励等级变更
        old_levels = {level["level"] for level in old_reward_levels}
        new_levels = {level["level"] for level in self.reward_levels}
        
        changes["reward_levels"]["added"] = list(new_levels - old_levels)
        changes["reward_levels"]["removed"] = list(old_levels - new_levels)
        
        # 检测等级修改
        for level in self.reward_levels:
            level_num = level["level"]
            old_level = next((l for l in old_reward_levels if l["level"] == level_num), None)
            if old_level and level != old_level:
                changes["reward_levels"]["modified"].append({
                    "level": level_num,
                    "old": old_level,
                    "new": level
                })
        
        return changes
    
    def _load_daily_tasks(self) -> List[Dict[str, Any]]:
        """加载每日任务配置"""
        tasks_config = game_config.get("tasks", {}).get("daily", {})
        tasks = tasks_config.get("tasks", [])
        
        # 如果YAML配置存在，确保每个任务都有task_id字段（从id字段复制）
        if tasks:
            for task in tasks:
                if 'id' in task and 'task_id' not in task:
                    task['task_id'] = task['id']
            return tasks
        
        # 如果YAML配置不存在，使用默认配置
            return [
                {
                    "task_id": "daily_login",
                    "name": "每日登录",
                    "description": "登录游戏",
                    "target": 1,
                    "rewards": {"gold": 100, "exp": 20},
                    "type": "login"
                },
                {
                    "task_id": "collect_hotspots",
                    "name": "收集热点",
                    "description": "收集5个热点",
                    "target": 5,
                    "rewards": {"gold": 200, "exp": 20},
                    "type": "collect"
                },
                {
                    "task_id": "defeat_boss",
                    "name": "击败BOSS",
                    "description": "击败1个BOSS",
                    "target": 1,
                    "rewards": {"diamond": 5, "exp": 20},
                    "type": "boss"
                },
                {
                    "task_id": "complete_sessions",
                    "name": "完成关卡",
                    "description": "完成3个游戏会话",
                    "target": 3,
                    "rewards": {"gold": 300, "exp": 20},
                    "type": "session"
                },
                {
                    "task_id": "watch_ads",
                    "name": "观看广告",
                    "description": "观看2个广告",
                    "target": 2,
                    "rewards": {"gold": 150, "exp": 20},
                    "type": "ad"
                }
            ]
        
        # 确保每个任务都有task_id字段（从id字段复制）
        for task in tasks:
            if 'id' in task and 'task_id' not in task:
                task['task_id'] = task['id']
        
        return tasks
    
    def _load_reward_levels(self) -> List[Dict[str, Any]]:
        """加载奖励等级配置"""
        reward_levels_config = game_config.get("tasks", {}).get("daily", {}).get("reward_levels", [])
        
        # 如果YAML配置不存在，使用默认配置
        if not reward_levels_config:
            return [
                {"level": 1, "required_exp": 20, "rewards": {"gold": 20}, "icon": "/img/page_icons/reward_level_1.png"},
                {"level": 2, "required_exp": 40, "rewards": {"gold": 40}, "icon": "/img/page_icons/reward_level_2.png"},
                {"level": 3, "required_exp": 60, "rewards": {"gold": 60}, "icon": "/img/page_icons/reward_level_3.png"},
                {"level": 4, "required_exp": 80, "rewards": {"diamond": 10}, "icon": "/img/page_icons/reward_level_4.png"},
                {"level": 5, "required_exp": 100, "rewards": {"diamond": 30}, "icon": "/img/page_icons/reward_level_5.png"}
            ]
        
        return reward_levels_config
    
    async def get_daily_tasks(
        self, 
        db: AsyncSession, 
        user: User
    ) -> List[Dict[str, Any]]:
        """
        获取用户的每日任务列表
        
        Returns:
            任务列表，包含进度和状态
        """
        today = date.today()
        tasks = []
        
        # TODO: 从数据库获取任务进度
        # 这里先返回模拟数据
        for task in self.daily_tasks:
            task_data = task.copy()
            task_data["current_progress"] = 0
            task_data["is_completed"] = False
            task_data["is_claimed"] = False
            tasks.append(task_data)
        
        return tasks
    
    async def update_task_progress(
        self,
        db: AsyncSession,
        user: User,
        task_type: str,
        increment: int = 1
    ) -> Dict[str, Any]:
        """
        更新任务进度
        
        Args:
            task_type: 任务类型 (login, collect, boss, session, ad)
            increment: 进度增量
            
        Returns:
            更新后的任务信息
        """
        today = date.today()
        
        # TODO: 实现任务进度更新逻辑
        # 1. 查找对应类型的任务
        # 2. 更新进度
        # 3. 检查是否完成
        # 4. 返回更新结果
        
        logger.info(f"Task progress updated: user={user.user_id}, type={task_type}, increment={increment}")
        
        return {
            "success": True,
            "task_type": task_type,
            "message": "任务进度更新功能待实现"
        }
    
    async def claim_task_reward(
        self,
        db: AsyncSession,
        user: User,
        task_id: str
    ) -> Dict[str, Any]:
        """
        领取任务奖励
        
        Args:
            task_id: 任务ID
            
        Returns:
            奖励信息
        """
        # TODO: 实现奖励领取逻辑
        # 1. 验证任务是否完成
        # 2. 验证是否已领取
        # 3. 发放奖励
        # 4. 标记为已领取
        
        # 查找任务配置
        task_config = None
        for task in self.daily_tasks:
            if task["task_id"] == task_id:
                task_config = task
                break
        
        if not task_config:
            return {"error": "Invalid task"}
        
        logger.info(f"Task reward claimed: user={user.user_id}, task={task_id}")
        
        return {
            "success": True,
            "task_id": task_id,
            "rewards": task_config["rewards"],
            "message": "任务奖励领取功能待实现"
        }
    
    async def reset_daily_tasks(self, db: AsyncSession):
        """
        重置所有用户的每日任务（定时任务调用）
        """
        # TODO: 实现每日任务重置
        # 1. 清理过期的任务进度
        # 2. 创建新的任务记录
        logger.info("Daily tasks reset completed")

    async def get_daily_tasks_with_progress(
        self,
        db: AsyncSession,
        user: User
    ) -> Dict[str, Any]:
        """
        获取带进度的每日任务列表

        Args:
            db: 数据库会话
            user: 当前用户

        Returns:
            每日任务信息
        """
        try:
            current_date = datetime.utcnow().strftime("%Y-%m-%d")

            # 查询用户今日任务进度
            tasks_query = await db.execute(
                select(DailyTaskProgress)
                .where(and_(
                    DailyTaskProgress.user_id == user.id,
                    DailyTaskProgress.date == current_date
                ))
            )
            user_tasks = {task.task_id: task for task in tasks_query.scalars().all()}

            # 构建任务列表
            tasks = []
            completed_count = 0

            for task_config in self.daily_tasks:
                task_id = task_config["task_id"]
                user_task = user_tasks.get(task_id)

                if user_task:
                    # 使用数据库中的进度
                    task_data = {
                        "task_id": task_id,
                        "name": task_config["name"],
                        "description": task_config["description"],
                        "type": task_config["type"],
                        "target": task_config["target"],
                        "current_progress": user_task.current_progress,
                        "is_completed": user_task.is_completed,
                        "is_claimed": user_task.is_rewarded,
                        "rewards": task_config["rewards"],
                        "exp_reward": task_config["rewards"].get("exp", 20),
                        "completed_at": user_task.completed_at
                    }
                else:
                    # 创建新的任务进度
                    task_data = {
                        "task_id": task_id,
                        "name": task_config["name"],
                        "description": task_config["description"],
                        "type": task_config["type"],
                        "target": task_config["target"],
                        "current_progress": 0,
                        "is_completed": False,
                        "is_claimed": False,
                        "rewards": task_config["rewards"],
                        "exp_reward": task_config["rewards"].get("exp", 20),
                        "completed_at": None
                    }

                tasks.append(task_data)
                if task_data["is_completed"]:
                    completed_count += 1

            return {
                "date": current_date,
                "tasks": tasks,
                "completed_count": completed_count,
                "total_count": len(self.daily_tasks),
                "completion_rate": completed_count / len(self.daily_tasks) * 100 if self.daily_tasks else 0
            }

        except Exception as e:
            logger.error(f"获取每日任务失败: {e}")
            return {"error": f"获取每日任务失败: {str(e)}"}

    async def update_task_progress_enhanced(
        self,
        db: AsyncSession,
        user: User,
        task_type: str,
        progress_increment: int = 1
    ) -> Dict[str, Any]:
        """
        更新任务进度（增强版）

        Args:
            db: 数据库会话
            user: 当前用户
            task_type: 任务类型
            progress_increment: 进度增量

        Returns:
            更新结果
        """
        try:
            current_date = datetime.utcnow().strftime("%Y-%m-%d")

            # 找到对应类型的任务
            matching_tasks = [
                task for task in self.daily_tasks
                if task["type"] == task_type
            ]

            updated_tasks = []

            for task_config in matching_tasks:
                task_id = task_config["task_id"]

                # 查询或创建任务进度
                task_query = await db.execute(
                    select(DailyTaskProgress)
                    .where(and_(
                        DailyTaskProgress.user_id == user.id,
                        DailyTaskProgress.task_id == task_id,
                        DailyTaskProgress.date == current_date
                    ))
                )
                task_progress = task_query.scalar_one_or_none()

                if not task_progress:
                    # 创建新的任务进度
                    task_progress = DailyTaskProgress(
                        user_id=user.id,
                        task_id=task_id,
                        task_type=task_type,
                        date=current_date,
                        current_progress=0,
                        target_progress=task_config["target"],
                        reward_type="gold",
                        reward_amount=task_config["rewards"].get("gold", 0)
                    )
                    db.add(task_progress)

                # 更新进度（如果未完成）
                if not task_progress.is_completed:
                    task_progress.current_progress = min(
                        task_progress.current_progress + progress_increment,
                        task_progress.target_progress
                    )

                    # 检查是否完成
                    if task_progress.current_progress >= task_progress.target_progress:
                        task_progress.is_completed = True
                        task_progress.completed_at = datetime.utcnow()

                updated_tasks.append({
                    "task_id": task_id,
                    "current_progress": task_progress.current_progress,
                    "target_progress": task_progress.target_progress,
                    "is_completed": task_progress.is_completed
                })

            # 不在这里提交事务，让调用方控制事务边界
            # await db.commit()

            return {
                "success": True,
                "updated_tasks": updated_tasks
            }

        except Exception as e:
            # 不在这里回滚事务，让调用方处理异常
            logger.error(f"更新任务进度失败: {e}")
            return {"error": f"更新任务进度失败: {str(e)}"}

    async def claim_task_reward_enhanced(
        self,
        db: AsyncSession,
        user: User,
        task_id: str,
        use_double_reward: bool = False
    ) -> Dict[str, Any]:
        """
        领取任务奖励（增强版）- 改进事务管理

        Args:
            db: 数据库会话
            user: 当前用户
            task_id: 任务ID
            use_double_reward: 是否使用双倍奖励

        Returns:
            领取结果，包含更新后的完整状态
        """
        async with db.begin():  # 使用事务确保数据一致性
            try:
                current_date = datetime.utcnow().strftime("%Y-%m-%d")

                # 查询任务进度
                task_query = await db.execute(
                    select(DailyTaskProgress)
                    .where(and_(
                        DailyTaskProgress.user_id == user.id,
                        DailyTaskProgress.task_id == task_id,
                        DailyTaskProgress.date == current_date
                    ))
                )
                task_progress = task_query.scalar_one_or_none()

                if not task_progress:
                    return {"error": "任务不存在"}

                if not task_progress.is_completed:
                    return {"error": "任务未完成"}

                if task_progress.is_rewarded:
                    return {"error": "奖励已领取"}

                # 获取任务配置
                task_config = None
                for task in self.daily_tasks:
                    if task["task_id"] == task_id:
                        task_config = task
                        break
                
                if not task_config:
                    return {"error": "任务配置不存在"}
                
                # 计算奖励
                base_exp = task_config.get("rewards", {}).get("exp", 20)
                base_gold = task_config.get("rewards", {}).get("gold", 0)
                base_diamond = task_config.get("rewards", {}).get("diamond", 0)
                
                multiplier = 2 if use_double_reward else 1
                total_exp = base_exp * multiplier
                total_gold = base_gold * multiplier
                total_diamond = base_diamond * multiplier

                # 更新任务状态
                task_progress.is_rewarded = True
                task_progress.reward_multiplier = multiplier
                task_progress.rewarded_at = datetime.utcnow()

                # 发放奖励到用户账户
                if total_gold > 0:
                    user.gold = (user.gold or 0) + total_gold
                if total_diamond > 0:
                    user.diamond = (user.diamond or 0) + total_diamond

                # 更新经验值
                exp_result = await self.update_task_experience(db, user, task_id, total_exp)

                # 获取更新后的状态
                updated_user_resources = {
                    "gold": user.gold or 0,
                    "diamond": user.diamond or 0,
                    "thieves_collected": user.thieves_collected or 0,
                    "garbage_collected": user.garbage_collected or 0,
                    "ammo_count": user.ammo_count or 0
                }

                return {
                    "success": True,
                    "task_id": task_id,
                    "rewards": {
                        "exp": total_exp,
                        "gold": total_gold,
                        "diamond": total_diamond
                    },
                    "is_double_reward": use_double_reward,
                    "multiplier": multiplier,
                    "user_resources": updated_user_resources,
                    "experience": exp_result if exp_result.get("success") else None
                }

            except Exception as e:
                logger.error(f"领取任务奖励失败: {e}")
                return {"error": f"领取任务奖励失败: {str(e)}"}

    async def get_daily_tasks_with_experience(
        self,
        db: AsyncSession,
        user: User
    ) -> Dict[str, Any]:
        """
        获取带经验值系统的每日任务列表
        
        Args:
            db: 数据库会话
            user: 当前用户
            
        Returns:
            完整的每日任务信息包含经验值和奖励等级
        """
        try:
            current_date = datetime.utcnow().strftime("%Y-%m-%d")
            
            # 获取或创建每日经验值记录
            exp_record = await self._get_or_create_daily_experience(db, user, current_date)
            
            # 获取任务进度
            tasks_data = await self.get_daily_tasks_with_progress(db, user)
            
            # 获取奖励等级状态
            reward_levels = await self._get_reward_levels_status(db, user, current_date, exp_record.current_exp)
            
            return {
                "date": current_date,
                "tasks": tasks_data.get("tasks", []),
                "experience": {
                    "current_exp": exp_record.current_exp,
                    "total_exp": exp_record.total_exp,
                    "progress": exp_record.current_exp / exp_record.total_exp if exp_record.total_exp > 0 else 0
                },
                "reward_levels": reward_levels,
                "completed_tasks": tasks_data.get("completed_count", 0),
                "total_tasks": tasks_data.get("total_count", 0),
                "completion_rate": tasks_data.get("completion_rate", 0)
            }
            
        except Exception as e:
            logger.error(f"获取每日任务失败: {e}")
            return {"error": f"获取每日任务失败: {str(e)}"}

    async def _get_or_create_daily_experience(
        self,
        db: AsyncSession,
        user: User,
        date: str
    ) -> DailyTaskExperience:
        """获取或创建每日经验值记录"""
        exp_query = await db.execute(
            select(DailyTaskExperience).where(
                and_(
                    DailyTaskExperience.user_id == user.id,
                    DailyTaskExperience.date == date
                )
            )
        )
        exp_record = exp_query.scalar_one_or_none()
        
        if not exp_record:
            exp_record = DailyTaskExperience(
                user_id=user.id,
                date=date,
                current_exp=0,
                total_exp=100,
                completed_tasks=0,
                total_tasks=len(self.daily_tasks)
            )
            db.add(exp_record)
            await db.commit()
            await db.refresh(exp_record)
        
        return exp_record

    async def _get_reward_levels_status(
        self,
        db: AsyncSession,
        user: User,
        date: str,
        current_exp: int
    ) -> List[Dict[str, Any]]:
        """获取奖励等级状态"""
        # 查询已存在的奖励等级记录
        levels_query = await db.execute(
            select(DailyTaskRewardLevel).where(
                and_(
                    DailyTaskRewardLevel.user_id == user.id,
                    DailyTaskRewardLevel.date == date
                )
            )
        )
        existing_levels = {level.level: level for level in levels_query.scalars().all()}
        
        reward_levels = []
        
        for level_config in self.reward_levels:
            level = level_config["level"]
            required_exp = level_config["required_exp"]
            rewards = level_config["rewards"]
            
            # 获取或创建奖励等级记录
            level_record = existing_levels.get(level)
            if not level_record:
                level_record = DailyTaskRewardLevel(
                    user_id=user.id,
                    date=date,
                    level=level,
                    required_exp=required_exp,
                    is_unlocked=current_exp >= required_exp,
                    reward_type=list(rewards.keys())[0] if rewards else "gold",
                    reward_amount=list(rewards.values())[0] if rewards else 0
                )
                if level_record.is_unlocked:
                    level_record.unlocked_at = datetime.utcnow()
                db.add(level_record)
                existing_levels[level] = level_record
            else:
                # 更新解锁状态
                if not level_record.is_unlocked and current_exp >= required_exp:
                    level_record.is_unlocked = True
                    level_record.unlocked_at = datetime.utcnow()
            
            # 构建返回数据
            level_data = level_record.to_dict()
            level_data.update({
                "icon": level_config.get("icon", ""),
                "rewards": rewards,
                "can_claim": level_record.is_unlocked and not level_record.is_claimed
            })
            reward_levels.append(level_data)
        
        await db.commit()
        return reward_levels

    async def update_task_experience(
        self,
        db: AsyncSession,
        user: User,
        task_id: str,
        exp_amount: int
    ) -> Dict[str, Any]:
        """更新任务经验值"""
        try:
            current_date = datetime.utcnow().strftime("%Y-%m-%d")
            
            # 获取经验值记录
            exp_record = await self._get_or_create_daily_experience(db, user, current_date)
            
            # 更新经验值
            exp_record.current_exp = min(exp_record.current_exp + exp_amount, exp_record.total_exp)
            
            # 更新已完成任务数
            completed_tasks_query = await db.execute(
                select(func.count(DailyTaskProgress.id)).where(
                    and_(
                        DailyTaskProgress.user_id == user.id,
                        DailyTaskProgress.date == current_date,
                        DailyTaskProgress.is_completed == True
                    )
                )
            )
            exp_record.completed_tasks = completed_tasks_query.scalar() or 0
            
            await db.commit()
            
            # 检查并更新奖励等级
            await self._check_and_update_reward_levels(db, user, current_date, exp_record.current_exp)
            
            return {
                "success": True,
                "current_exp": exp_record.current_exp,
                "total_exp": exp_record.total_exp,
                "completed_tasks": exp_record.completed_tasks
            }
            
        except Exception as e:
            await db.rollback()
            logger.error(f"更新任务经验值失败: {e}")
            return {"error": f"更新任务经验值失败: {str(e)}"}

    async def _check_and_update_reward_levels(
        self,
        db: AsyncSession,
        user: User,
        date: str,
        current_exp: int
    ):
        """检查并更新奖励等级解锁状态"""
        for level_config in self.reward_levels:
            level = level_config["level"]
            required_exp = level_config["required_exp"]
            
            if current_exp >= required_exp:
                level_query = await db.execute(
                    select(DailyTaskRewardLevel).where(
                        and_(
                            DailyTaskRewardLevel.user_id == user.id,
                            DailyTaskRewardLevel.date == date,
                            DailyTaskRewardLevel.level == level
                        )
                    )
                )
                level_record = level_query.scalar_one_or_none()
                
                if level_record and not level_record.is_unlocked:
                    level_record.is_unlocked = True
                    level_record.unlocked_at = datetime.utcnow()
        
        await db.commit()

    async def claim_reward_level(
        self,
        db: AsyncSession,
        user: User,
        level: int
    ) -> Dict[str, Any]:
        """领取奖励等级奖励 - 改进事务管理"""
        async with db.begin():  # 使用事务确保数据一致性
            try:
                current_date = datetime.utcnow().strftime("%Y-%m-%d")
                
                # 查询奖励等级记录
                level_query = await db.execute(
                    select(DailyTaskRewardLevel).where(
                        and_(
                            DailyTaskRewardLevel.user_id == user.id,
                            DailyTaskRewardLevel.date == current_date,
                            DailyTaskRewardLevel.level == level
                        )
                    )
                )
                level_record = level_query.scalar_one_or_none()
                
                if not level_record:
                    return {"error": "奖励等级不存在"}
                
                if not level_record.is_unlocked:
                    return {"error": "奖励等级未解锁"}
                
                if level_record.is_claimed:
                    return {"error": "奖励已领取"}
                
                # 发放奖励
                if level_record.reward_type == "gold":
                    user.gold = (user.gold or 0) + level_record.reward_amount
                elif level_record.reward_type == "diamond":
                    user.diamond = (user.diamond or 0) + level_record.reward_amount
                
                # 更新领取状态
                level_record.is_claimed = True
                level_record.claimed_at = datetime.utcnow()
                
                # 获取更新后的用户资源
                updated_user_resources = {
                    "gold": user.gold or 0,
                    "diamond": user.diamond or 0,
                    "thieves_collected": user.thieves_collected or 0,
                    "garbage_collected": user.garbage_collected or 0,
                    "ammo_count": user.ammo_count or 0
                }
                
                return {
                    "success": True,
                    "level": level,
                    "reward_type": level_record.reward_type,
                    "reward_amount": level_record.reward_amount,
                    "user_resources": updated_user_resources
                }
                
            except Exception as e:
                logger.error(f"领取奖励等级失败: {e}")
                return {"error": f"领取奖励等级失败: {str(e)}"}

    async def get_daily_tasks_unified(
        self,
        db: AsyncSession,
        user: User
    ) -> Dict[str, Any]:
        """
        统一的每日任务数据获取方法
        
        Returns:
            包含所有任务相关数据的完整响应
        """
        try:
            current_date = datetime.utcnow().strftime("%Y-%m-%d")
            
            # 获取或创建每日经验值记录
            exp_record = await self._get_or_create_daily_experience(db, user, current_date)
            
            # 获取任务进度
            tasks_data = await self.get_daily_tasks_with_progress(db, user)
            
            # 获取奖励等级状态
            reward_levels = await self._get_reward_levels_status(db, user, current_date, exp_record.current_exp)
            
            # 获取用户当前资源（避免额外API调用）
            user_resources = {
                "gold": user.gold or 0,
                "diamond": user.diamond or 0,
                "thieves_collected": user.thieves_collected or 0,
                "garbage_collected": user.garbage_collected or 0,
                "ammo_count": user.ammo_count or 0
            }
            
            return {
                "success": True,
                "date": current_date,
                "tasks": tasks_data.get("tasks", []),
                "experience": {
                    "current_exp": exp_record.current_exp,
                    "total_exp": exp_record.total_exp,
                    "progress": exp_record.current_exp / exp_record.total_exp if exp_record.total_exp > 0 else 0
                },
                "reward_levels": reward_levels,
                "user_resources": user_resources,
                "summary": {
                    "completed_tasks": tasks_data.get("completed_count", 0),
                    "total_tasks": tasks_data.get("total_count", 0),
                    "completion_rate": tasks_data.get("completion_rate", 0)
                }
            }
            
        except Exception as e:
            logger.error(f"获取统一任务数据失败: {e}")
            return {"error": f"获取任务数据失败: {str(e)}"}


# 创建全局实例
task_service = TaskService() 