"""
游戏核心服务
处理游戏会话、热点收集、BOSS战斗等核心逻辑
"""
import logging
import uuid
import random
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_, func, text
from sqlalchemy.orm import selectinload

from app.core.config import game_config, cities_config, hotspot_templates_config, scene_hotspots_config
from app.core.redis_client import redis_manager
from app.core.database import get_db
from app.services.config_service import config_service
from app.models.game import GameSession, HotspotRecord, BossBattle, SessionStatus, HotspotType, RewardType, UserHotspotCollection, SceneHotspot
from app.models.user import User
# 🚫 PRD合规性清理：移除CannonUpgrade导入 - 复杂大炮系统不符合PRD要求
from app.models.artifact import UserArtifact, UserCityProgress
# 🚫 PRD合规性清理：移除AmmoUsageType导入 - 复杂弹药系统不符合PRD要求
from app.analytics.metrics import analytics_service
# 🚫 PRD合规性清理：完全移除弹药系统 - PRD中没有弹药概念
from app.services.guardian_service import guardian_service

logger = logging.getLogger(__name__)


class GameService:
    """游戏服务"""
    
    async def ensure_user_hotspot_collection_table(self, db: AsyncSession):
        """确保用户热点收集表存在"""
        try:
            # 尝试查询表，如果表不存在会抛出异常
            await db.execute(text("SELECT 1 FROM user_hotspot_collections LIMIT 1"))
            logger.info("用户热点收集表已存在")
        except Exception:
            logger.info("创建用户热点收集表...")
            try:
                # 创建表的SQL（兼容MySQL和SQLite）
                create_table_sql = """
                CREATE TABLE IF NOT EXISTS user_hotspot_collections (
                    id BIGINT PRIMARY KEY AUTO_INCREMENT,
                    user_id BIGINT NOT NULL,
                    city_id VARCHAR(50) NOT NULL,
                    scene_id VARCHAR(50) NOT NULL,
                    hotspot_name VARCHAR(50) NOT NULL,
                    hotspot_type VARCHAR(20) NOT NULL,
                    collected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    
                    UNIQUE KEY unique_user_hotspot (user_id, city_id, scene_id, hotspot_name)
                )
                """
                await db.execute(text(create_table_sql))
                await db.commit()
                logger.info("✅ 用户热点收集表创建成功")
            except Exception as e:
                logger.error(f"创建用户热点收集表失败: {e}")

    async def ensure_scene_hotspots_table(self, db: AsyncSession):
        """确保场景固定热点表存在"""
        try:
            await db.execute(text("SELECT 1 FROM scene_hotspots LIMIT 1"))
            logger.info("场景固定热点表已存在")
        except Exception:
            logger.info("创建场景固定热点表...")
            try:
                create_table_sql = """
                CREATE TABLE IF NOT EXISTS scene_hotspots (
                    id BIGINT PRIMARY KEY AUTO_INCREMENT,
                    city_id VARCHAR(50) NOT NULL,
                    scene_id VARCHAR(50) NOT NULL,
                    hotspot_name VARCHAR(50) NOT NULL,
                    hotspot_type VARCHAR(20) NOT NULL,
                    position_x DECIMAL(10,6) NOT NULL,
                    position_y DECIMAL(10,6) NOT NULL,
                    reward_type VARCHAR(20) NOT NULL,
                    reward_amount INT DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    
                    UNIQUE KEY unique_scene_hotspot (city_id, scene_id, hotspot_name)
                )
                """
                await db.execute(text(create_table_sql))
                await db.commit()
                logger.info("✅ 场景固定热点表创建成功")
            except Exception as e:
                logger.error(f"创建场景固定热点表失败: {e}")

    async def initialize_scene_hotspots(self, db: AsyncSession, city_id: str, scene_id: str):
        """初始化场景的固定热点数据 - 支持新配置格式"""
        try:
            # 检查是否已有热点数据
            result = await db.execute(
                select(func.count(SceneHotspot.id)).where(
                    and_(
                        SceneHotspot.city_id == city_id,
                        SceneHotspot.scene_id == scene_id
                    )
                )
            )
            existing_count = result.scalar() or 0
            
            if existing_count > 0:
                logger.info(f"场景 {scene_id} 已有 {existing_count} 个固定热点")
                return
            
            logger.info(f"初始化场景 {scene_id} 的固定热点数据...")
            
            # 获取场景热点配置 - 支持新格式
            scene_config = scene_hotspots_config.get(f"scenes.{scene_id}")
            if not scene_config:
                logger.warning(f"场景 {scene_id} 没有配置数据")
                return
            
            # 检查配置格式：新格式有hotspot_config，旧格式有available_hotspots
            hotspot_config = scene_config.get("hotspot_config")
            if hotspot_config:
                # 新格式配置
                logger.info(f"使用新格式配置初始化场景 {scene_id}")
                await self._initialize_from_new_config(db, city_id, scene_id, hotspot_config)
            else:
                # 旧格式配置（向后兼容）
                logger.info(f"使用旧格式配置初始化场景 {scene_id}")
                await self._initialize_from_old_config(db, city_id, scene_id, scene_config)
            
            await db.commit()
            
        except Exception as e:
            logger.error(f"初始化场景热点失败: {e}")
            raise

    async def _initialize_from_new_config(self, db: AsyncSession, city_id: str, scene_id: str, hotspot_config: Dict[str, Any]):
        """从新格式配置初始化热点数据"""
        import hashlib
        import random
        import json
        
        hotspot_types = hotspot_config.get("hotspot_types", {})
        created_count = 0
        
        for hotspot_type_str, type_config in hotspot_types.items():
            # 检查是否启用
            if not type_config.get("enabled", False):
                continue
            
            # 获取数量和可用名称
            count = type_config.get("count", 0)
            available_names = type_config.get("available_names", [])
            
            if count <= 0 or not available_names:
                continue
            
            # 验证热点类型
            valid_types = ["thief", "garbage", "treasure", "boss_thief"]
            if hotspot_type_str not in valid_types:
                logger.warning(f"无效的热点类型: {hotspot_type_str}")
                continue
            
            # 选择热点名称（取前count个）
            selected_names = available_names[:count]
            
            # 获取奖励配置
            reward_config = type_config.get("reward_config", {})
            
            # 为每个名称创建固定热点记录
            for name in selected_names:
                # 生成固定位置 - 基于热点名称哈希确保每次相同
                hash_input = f"{city_id}_{scene_id}_{name}".encode()
                hash_value = int(hashlib.md5(hash_input).hexdigest()[:8], 16)
                
                # 使用哈希值生成固定但随机分布的位置
                random.seed(hash_value)
                
                # 根据位置配置生成坐标
                position_config = type_config.get("position_config", {})
                position_mode = type_config.get("position_mode", "random")
                
                if position_mode == "predefined":
                    # 预定义位置
                    predefined_positions = position_config.get("predefined_positions", [])
                    if predefined_positions:
                        pos_index = hash_value % len(predefined_positions)
                        position_x = predefined_positions[pos_index]["x"]
                        position_y = predefined_positions[pos_index]["y"]
                    else:
                        position_x = 116.407526  # 北京经度
                        position_y = 39.904200   # 北京纬度
                elif position_mode == "fixed":
                    # 固定位置
                    fixed_pos = position_config.get("fixed_position", {"x": 116.407526, "y": 39.904200})
                    position_x = fixed_pos["x"]
                    position_y = fixed_pos["y"]
                else:
                    # 随机位置（默认）
                    random_area = position_config.get("random_area", {})
                    x_min = random_area.get("x_min", 115.0)   # 经度范围：北京西
                    x_max = random_area.get("x_max", 118.0)   # 经度范围：北京东 
                    y_min = random_area.get("y_min", 39.0)    # 纬度范围：北京南
                    y_max = random_area.get("y_max", 41.0)    # 纬度范围：北京北
                    
                    position_x = x_min + random.random() * (x_max - x_min)
                    position_y = y_min + random.random() * (y_max - y_min)
                
                # 重置随机种子
                random.seed()
                
                # 使用统一热点服务生成奖励
                from app.services.unified_hotspot_service import unified_hotspot_service
                config = unified_hotspot_service.get_hotspot_configuration(city_id, scene_id)
                reward_data = unified_hotspot_service.generate_hotspot_reward(hotspot_type_str, name, config)
                
                # 根据奖励类型设置数据库字段
                if reward_data.get("type") == "treasure_box":
                    # 宝箱奖励：将内容序列化为JSON字符串存储
                    contents = reward_data.get("contents", {})
                    total_value = sum(contents.values())  # 用总价值作为reward_amount
                    reward_type = "treasure_box"
                    reward_amount = total_value
                    # 将完整内容存储到扩展字段
                    reward_data_json = json.dumps(reward_data)
                else:
                    # 单一资源奖励
                    reward_type = reward_data.get("type", "gold")
                    reward_amount = reward_data.get("amount", 10)
                    reward_data_json = json.dumps(reward_data)
                
                scene_hotspot = SceneHotspot(
                    city_id=city_id,
                    scene_id=scene_id,
                    hotspot_name=name,
                    hotspot_type=hotspot_type_str,
                    position_x=position_x,
                    position_y=position_y,
                    reward_type=reward_type,
                    reward_amount=reward_amount,
                    reward_data=reward_data_json  # 存储完整奖励数据
                )
                db.add(scene_hotspot)
                created_count += 1
                
                logger.info(f"创建固定热点: {name} ({hotspot_type_str}) at ({position_x:.3f}, {position_y:.3f}) reward: {reward_amount} {reward_type}")
        
        logger.info(f"✅ 场景 {scene_id} 固定热点初始化完成，共创建 {created_count} 个热点")

    async def _initialize_from_old_config(self, db: AsyncSession, city_id: str, scene_id: str, scene_config: Dict[str, Any]):
        """从旧格式配置初始化热点数据（向后兼容）"""
        import hashlib
        import random
        
        available_hotspots = scene_config.get("available_hotspots", {})
        recommended_config = scene_config.get("recommended_config", {})
        
        hotspot_id = 1
        for hotspot_type_str, recommended_count in recommended_config.items():
            if recommended_count <= 0:
                continue
                
            try:
                hotspot_type = HotspotType(hotspot_type_str)
            except ValueError:
                logger.warning(f"无效的热点类型: {hotspot_type_str}")
                continue
            
            # 获取该类型可用的热点名称列表
            available_names = available_hotspots.get(hotspot_type_str, [])
            if not available_names:
                continue
            
            # 选择推荐数量的热点名称
            selected_names = available_names[:recommended_count]
            
            # 为每个名称创建固定热点记录
            for i, name in enumerate(selected_names):
                # 生成固定位置 - 基于热点名称哈希确保每次相同
                hash_input = f"{city_id}_{scene_id}_{name}".encode()
                hash_value = int(hashlib.md5(hash_input).hexdigest()[:8], 16)
                
                # 使用哈希值生成固定但随机分布的位置
                random.seed(hash_value)
                position_x = 115.0 + random.random() * 3.0  # 115.0 - 118.0 经度范围
                position_y = 39.0 + random.random() * 2.0   # 39.0 - 41.0 纬度范围
                
                # 重置随机种子
                random.seed()
                
                # 确定奖励
                reward_type, reward_amount = self._determine_reward(hotspot_type, game_config.get("hotspots.types", {}))
                
                scene_hotspot = SceneHotspot(
                    city_id=city_id,
                    scene_id=scene_id,
                    hotspot_name=name,
                    hotspot_type=hotspot_type_str,
                    position_x=position_x,
                    position_y=position_y,
                    reward_type=reward_type,
                    reward_amount=reward_amount
                )
                db.add(scene_hotspot)
                hotspot_id += 1
                
                logger.info(f"创建固定热点: {name} ({hotspot_type_str}) at ({position_x:.3f}, {position_y:.3f})")
        
        logger.info(f"✅ 场景 {scene_id} 固定热点初始化完成，共创建 {hotspot_id-1} 个热点")

    async def create_session(
        self,
        db: AsyncSession,
        user: User,
        city_id: str,
        scene_id: str
    ) -> Dict[str, Any]:
        """
        创建游戏会话

        - 创建会话记录
        - 返回用户当前状态
        - 热点数据由前端从XML文件加载，不再从后台获取
        """
        try:
            # 确保表存在
            await self.ensure_user_hotspot_collection_table(db)
            await self.ensure_scene_hotspots_table(db)

            # 检查城市是否解锁
            city_progress = await self._get_or_create_city_progress(db, user.id, city_id)
            if not city_progress.is_unlocked and city_id != "beijing":  # 北京默认解锁
                return {"error": "City not unlocked"}

            # 创建会话
            session_id = str(uuid.uuid4())
            session = GameSession(
                session_id=session_id,
                user_id=user.id,
                scene_id=scene_id,
                city_id=city_id,
                status=SessionStatus.ACTIVE
            )
            db.add(session)
            await db.commit()
            await db.refresh(session)

            logger.info(f"Session created: {session_id}")

            # 创建会话随机种子（用于概率奖励控制）
            try:
                from app.services.hotspot_reward_service import hotspot_reward_service
                await hotspot_reward_service.create_session_seed(db, session_id, user)
                logger.info(f"为会话 {session_id} 创建随机种子成功")
            except Exception as e:
                logger.warning(f"创建会话随机种子失败: {e}")

            # 缓存会话数据（不包含热点数据）
            session_data = {
                "session_id": session_id,
                "user_id": user.id,
                "city_id": city_id,
                "scene_id": scene_id,
                "started_at": datetime.utcnow().isoformat()
            }
            await redis_manager.set_json(f"game_session:{session_id}", session_data, expire=7200)

            # 追踪指标
            await analytics_service.track_session_start(session_id, str(user.id), city_id)

            logger.info(f"用户 {user.id} 创建游戏会话: {session_id}")

            # 🚫 PRD合规性清理：移除弹药相关数据，PRD中没有弹药概念
            thieves_collected = user.thieves_collected or 0
            garbage_collected = user.garbage_collected or 0

            return {
                "session_id": session_id,
                "thieves_collected": thieves_collected,
                "garbage_collected": garbage_collected,
                # 🚫 PRD合规性清理：移除弹药相关字段
            }
            
        except Exception as e:
            logger.error(f"Failed to create session: {e}")
            raise
    
    async def collect_hotspot(
        self,
        db: AsyncSession,
        user: User,
        session_id: str,
        hotspot_id: str
    ) -> Dict[str, Any]:
        """收集热点 - 高性能缓存版本"""
        try:
            # 1. 验证热点
            hotspot_info = await self._validate_hotspot(db, user, session_id, hotspot_id)
            if "error" in hotspot_info:
                return hotspot_info

            hotspot_type = hotspot_info["type"]
            hotspot_name = hotspot_info["name"]
            reward = hotspot_info["reward"]
            scene_hotspot = hotspot_info.get("scene_hotspot")

            # 2. 获取会话信息
            result = await db.execute(
                select(GameSession).where(
                    GameSession.session_id == session_id
                )
            )
            session = result.scalar_one()

            # 3. 使用经验值缓存系统处理收集操作
            from app.services.experience_cache_service import experience_cache_service
            
            # 计算基础经验值
            base_experience = 12 if hotspot_type == "thief" else 8  # 小偷12经验，垃圾8经验（符合PRD要求）
            
            # 检查体力值影响
            stamina_stats = await experience_cache_service.get_user_cache_stats(user.id)
            current_stamina = stamina_stats.get("stamina", 120)
            
            # 体力不足时经验减少25%（PRD要求：<30时75%经验）
            if current_stamina < 30:
                base_experience = int(base_experience * 0.75)
            
            cache_result = await experience_cache_service.cache_hotspot_experience(
                user_id=user.id,
                session_id=session_id,
                hotspot_id=hotspot_id,
                hotspot_type=hotspot_type,
                experience_gained=base_experience,
                hotspot_name=hotspot_name,
                city_id=session.city_id,
                scene_id=session.scene_id
            )
            
            # 消耗体力值
            stamina_result = await experience_cache_service.update_stamina(
                user_id=user.id,
                stamina_change=-1,  # 每次收集消耗1点体力
                reason=f"收集{hotspot_type}"
            )
            
            if "error" in cache_result:
                return cache_result

            # 4. 计算奖励（使用概率系统）
            reward = {"type": "gold", "amount": 0}  # 默认无奖励
            
            try:
                from app.services.hotspot_reward_service import hotspot_reward_service
                
                calculated_reward = await hotspot_reward_service.calculate_reward(
                    db=db,
                    session_id=session_id,
                    user=user,
                    hotspot_type=hotspot_type,
                    hotspot_name=hotspot_name
                )
                
                # 使用新系统的计算结果，无论是否有奖励
                if calculated_reward:
                    reward = calculated_reward
                    if calculated_reward.get("amount", 0) > 0:
                        logger.info(f"概率奖励系统掉落: {hotspot_name} -> {reward}")
                    else:
                        logger.info(f"概率奖励系统无掉落: {hotspot_name}")
                else:
                    logger.info(f"概率奖励系统返回空结果: {hotspot_name}")
                    
            except Exception as e:
                logger.error(f"概率奖励系统异常: {e}")
                # 如果新系统出错，仍然使用无奖励，不再fallback到旧配置
                pass
            
            # 发放奖励
            reward_type = reward.get("type", "gold")
            
            # 处理宝箱奖励
            if reward_type == "treasure_box":
                contents = reward.get("contents", {})
                total_gold = 0
                total_diamond = 0
                
                # 累计所有资源（确保contents不为None）
                if contents:
                    for resource_type, amount in contents.items():
                        if resource_type == "gold":
                            total_gold += amount
                        elif resource_type == "diamond":
                            total_diamond += amount
                
                # 发放奖励
                if total_gold > 0:
                    user.gold = (user.gold or 0) + total_gold
                    session.gold_earned = (session.gold_earned or 0) + total_gold
                if total_diamond > 0:
                    user.diamond = (user.diamond or 0) + total_diamond
                    session.diamond_earned = (session.diamond_earned or 0) + total_diamond
                
                # 为前端返回，将宝箱内容合并到reward字段
                reward["total_gold"] = total_gold
                reward["total_diamond"] = total_diamond
                
            else:
                # 处理单一资源奖励和多重奖励
                reward_amount = reward.get("amount", 0)
                all_rewards = reward.get("all_rewards", {})
                
                # 如果有多重奖励，发放所有奖励
                if all_rewards:
                    for resource_type, amount in all_rewards.items():
                        if resource_type == "gold" and amount > 0:
                            user.gold = (user.gold or 0) + amount
                            session.gold_earned = (session.gold_earned or 0) + amount
                        elif resource_type == "diamond" and amount > 0:
                            user.diamond = (user.diamond or 0) + amount
                            session.diamond_earned = (session.diamond_earned or 0) + amount
                        elif resource_type == "artifact" and amount > 0:
                            # 处理图鉴奖励 - 这里可以添加图鉴逻辑
                            logger.info(f"获得图鉴奖励: {amount}")
                            
                    # 更新返回数据
                    reward["all_rewards"] = all_rewards
                    
                elif reward_amount > 0:
                    # 单一奖励
                    if reward_type == "gold":
                        user.gold = (user.gold or 0) + reward_amount
                        session.gold_earned = (session.gold_earned or 0) + reward_amount
                    elif reward_type == "diamond":
                        user.diamond = (user.diamond or 0) + reward_amount
                        session.diamond_earned = (session.diamond_earned or 0) + reward_amount
                    elif reward_type == "artifact":
                        # 处理图鉴奖励
                        logger.info(f"获得图鉴奖励: {reward_amount}")
                else:
                    # 无奖励情况
                    logger.info(f"热点 {hotspot_name} 无奖励掉落")

            # 5. 异步更新任务进度（不阻塞响应）
            asyncio.create_task(self._update_task_progress(db, user, "collect", 1))

            # 6. 异步追踪分析指标
            asyncio.create_task(analytics_service.track_hotspot_collected(hotspot_type))

            # 7. 构建快速响应（从缓存获取进度）
            cached_progress = cache_result.get("progress", {})
            response = {
                "success": True,
                "hotspot": {
                    "id": hotspot_id,
                    "name": hotspot_name,
                    "type": hotspot_type
                },
                "reward": reward,
                "progress": {
                    **cached_progress,
                    "gold": user.gold or 0,
                    "diamond": user.diamond or 0
                },
                "cached": True  # 标识这是缓存响应
            }

            logger.info(f"用户 {user.id} 成功收集热点 {hotspot_name} ({hotspot_type}) - 缓存模式")
            
            return response
            
        except Exception as e:
            await db.rollback()
            logger.error(f"收集热点失败: {e}")
            return {"error": f"Failed to collect hotspot: {str(e)}"}
    
    # 🚫 PRD合规性清理：移除boss_attack方法
    # PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少





    
    async def end_session(
        self,
        db: AsyncSession,
        user: User,
        session_id: str
    ) -> Dict[str, Any]:
        """结束游戏会话"""
        try:
            # 获取会话
            result = await db.execute(
                select(GameSession).where(
                    and_(
                        GameSession.session_id == session_id,
                        GameSession.user_id == user.id,
                        GameSession.status == SessionStatus.ACTIVE
                    )
                )
            )
            session = result.scalar_one_or_none()
            if not session:
                return {"error": "Invalid session"}
            
            # 计算持续时间
            duration = int((datetime.utcnow() - session.started_at).total_seconds())
            
            # 更新会话状态
            session.status = SessionStatus.COMPLETED
            session.ended_at = datetime.utcnow()
            session.duration = duration
            
            # 计算经验奖励（从用户模型获取收集数据）
            thieves_collected = user.thieves_collected or 0
            garbage_collected = user.garbage_collected or 0
            exp_earned = 10 + (thieves_collected * 2) + (garbage_collected * 1)
            user.exp += exp_earned
            
            # 检查升级
            level_up = None
            next_level_exp = user.level * 100  # 简单的升级公式
            if user.exp >= next_level_exp:
                user.level += 1
                user.exp -= next_level_exp
                level_up = {
                    "new_level": user.level,
                    "rewards": {
                        "gold": 100 * user.level,
                        "diamond": 5
                    }
                }
                user.gold += level_up["rewards"]["gold"]
                user.diamond += level_up["rewards"]["diamond"]
            
            # 更新总游戏时长
            user.total_play_time += duration
            
            # 更新任务进度 - 完成会话
            await self._update_task_progress(db, user, "session", 1)
            
            await db.commit()
            
            # 清理缓存
            await redis_manager.delete(f"game_session:{session_id}")
            
            # 清理所有热点缓存
            hotspot_keys = []
            
            # 通过会话中的热点记录清理缓存
            hotspot_results = await db.execute(
                select(HotspotRecord.id).where(HotspotRecord.session_id == session.id)
            )
            hotspot_ids = [row[0] for row in hotspot_results.fetchall()]
            
            for hotspot_id in hotspot_ids:
                hotspot_keys.append(f"hotspot:{session_id}:{hotspot_id}")
            
            if hotspot_keys:
                await redis_manager.delete(*hotspot_keys)
            
            # 追踪指标
            await analytics_service.track_session_end(session_id, duration)
            
            # 获取会话中发现的文物
            artifacts_found = []
            if session.boss_battles:
                for battle in session.boss_battles:
                    if battle.artifact_reward:
                        artifacts_found.append(battle.artifact_reward)
            
            return {
                "summary": {
                    "duration": duration,
                    "thieves_collected": thieves_collected,
                    "garbage_collected": garbage_collected,
                    "gold_earned": session.gold_earned,
                    "diamond_earned": session.diamond_earned,
                    "exp_earned": exp_earned,
                    "artifacts_found": artifacts_found
                },
                "level_up": level_up
            }
            
        except Exception as e:
            logger.error(f"Failed to end session: {e}")
            raise
    
    # ===== 私有方法 =====
    
    async def _get_all_scene_hotspots(
        self, 
        db: AsyncSession, 
        city_id: str, 
        scene_id: str
    ) -> List[Dict[str, Any]]:
        """获取场景的所有热点数据（不过滤已收集的）- 每次游戏会话都是全新的"""
        try:
            # 获取场景所有固定热点
            result = await db.execute(
                select(SceneHotspot).where(
                    and_(
                        SceneHotspot.city_id == city_id,
                        SceneHotspot.scene_id == scene_id
                    )
                ).order_by(SceneHotspot.id)
            )
            all_scene_hotspots = result.scalars().all()
            
            if not all_scene_hotspots:
                logger.warning(f"场景 {scene_id} 没有找到固定热点数据")
                return []
            
            # 返回所有热点，不过滤已收集的
            available_hotspots = []
            for hotspot in all_scene_hotspots:
                # 使用统一的热点ID格式：scene_hotspot_{scene_hotspot_id}
                hotspot_data = {
                    "id": f"scene_hotspot_{hotspot.id}",
                    "name": hotspot.hotspot_name,
                    "type": hotspot.hotspot_type,
                    "position": {
                        "x": float(hotspot.position_x),
                        "y": float(hotspot.position_y)
                    },
                    "reward_preview": {
                        "type": hotspot.reward_type,
                        "amount": hotspot.reward_amount
                    }
                }
                available_hotspots.append(hotspot_data)
            
            logger.info(f"场景 {scene_id} 总共返回 {len(available_hotspots)} 个热点（全新游戏会话）")
            
            return available_hotspots

        except Exception as e:
            logger.error(f"获取场景热点失败: {e}")
            return []

    async def _get_managed_hotspots(self, db: AsyncSession, city_id: str, scene_id: str) -> List[Dict[str, Any]]:
        """
        获取管理后台配置的热点数据

        Args:
            db: 数据库会话
            city_id: 城市ID
            scene_id: 场景ID

        Returns:
            热点数据列表，格式与游戏兼容
        """
        try:
            # 导入管理后台的热点模型
            from app.models.game import SceneHotspot

            # 查询管理后台配置的热点（只返回启用的热点）
            result = await db.execute(
                select(SceneHotspot).where(
                    and_(
                        SceneHotspot.city_id == city_id,
                        SceneHotspot.scene_id == scene_id,
                        SceneHotspot.enabled == True
                    )
                ).order_by(SceneHotspot.created_at)
            )
            managed_hotspots = result.scalars().all()

            if not managed_hotspots:
                logger.info(f"管理后台未配置热点: {city_id}/{scene_id}")
                return []

            # 转换为游戏兼容的格式
            hotspots_data = []
            for hotspot in managed_hotspots:
                # 处理枚举类型，确保兼容性
                hotspot_type = hotspot.hotspot_type if isinstance(hotspot.hotspot_type, str) else str(hotspot.hotspot_type)
                reward_type = hotspot.reward_type if isinstance(hotspot.reward_type, str) else str(hotspot.reward_type)

                hotspot_data = {
                    "id": f"managed_hotspot_{hotspot.id}",  # 使用managed前缀区分
                    "name": hotspot.hotspot_name,
                    "type": hotspot_type,
                    "position": {
                        "x": float(hotspot.position_x) if hotspot.position_x else 0.0,
                        "y": float(hotspot.position_y) if hotspot.position_y else 0.0,
                        "ath": float(hotspot.position_x) if hotspot.position_x else 0.0,  # krpano兼容
                        "atv": float(hotspot.position_y) if hotspot.position_y else 0.0   # krpano兼容
                    },
                    "reward_preview": {
                        "type": reward_type,
                        "amount": hotspot.reward_amount or 0
                    },
                    # 管理后台特有的配置
                    "scale": float(hotspot.scale) if hotspot.scale else 1.0,
                    "image_url": hotspot.image_url or "",
                    "visible": hotspot.visible if hotspot.visible is not None else True,
                    "onclick_action": hotspot.onclick_action or "",
                    "description": hotspot.description or "",
                    "source": "managed"  # 标识来源为管理后台
                }
                hotspots_data.append(hotspot_data)

            logger.info(f"从管理后台获取到 {len(hotspots_data)} 个热点配置: {city_id}/{scene_id}")
            return hotspots_data

        except Exception as e:
            logger.error(f"获取管理后台热点配置失败: {e}")
            return []

    async def _get_available_hotspots_for_user(
        self, 
        db: AsyncSession, 
        user_id: int, 
        city_id: str, 
        scene_id: str
    ) -> List[Dict[str, Any]]:
        """获取用户可用的热点数据（过滤已收集的）- 仅用于特殊场景"""
        try:
            # 获取场景所有固定热点
            result = await db.execute(
                select(SceneHotspot).where(
                    and_(
                        SceneHotspot.city_id == city_id,
                        SceneHotspot.scene_id == scene_id
                    )
                ).order_by(SceneHotspot.id)
            )
            all_scene_hotspots = result.scalars().all()
            
            if not all_scene_hotspots:
                logger.warning(f"场景 {scene_id} 没有找到固定热点数据")
                return []
            
            # 获取用户已收集的热点名称
            collected_result = await db.execute(
                select(UserHotspotCollection.hotspot_name).where(
                    and_(
                        UserHotspotCollection.user_id == user_id,
                        UserHotspotCollection.city_id == city_id,
                        UserHotspotCollection.scene_id == scene_id
                    )
                )
            )
            collected_hotspot_names = {row[0] for row in collected_result.fetchall()}
            
            # 过滤掉已收集的热点
            available_hotspots = []
            for hotspot in all_scene_hotspots:
                if hotspot.hotspot_name not in collected_hotspot_names:
                    # 使用统一的热点ID格式：scene_hotspot_{scene_hotspot_id}
                    hotspot_data = {
                        "id": f"scene_hotspot_{hotspot.id}",
                        "name": hotspot.hotspot_name,
                        "type": hotspot.hotspot_type,
                        "position": {
                            "x": float(hotspot.position_x),
                            "y": float(hotspot.position_y)
                        },
                        "reward_preview": {
                            "type": hotspot.reward_type,
                            "amount": hotspot.reward_amount
                        }
                    }
                    available_hotspots.append(hotspot_data)
            
            logger.info(f"场景 {scene_id} 总热点: {len(all_scene_hotspots)}, 已收集: {len(collected_hotspot_names)}, 可用: {len(available_hotspots)}")
            
            return available_hotspots
            
        except Exception as e:
            logger.error(f"获取用户可用热点失败: {e}")
            return []
    
    async def _generate_hotspots(self, db: AsyncSession, session: GameSession) -> List[HotspotRecord]:
        """生成热点 - 支持模板导入和多种生成模式"""
        
        # 获取热点模板配置
        template = self._get_hotspot_template(session.city_id, session.scene_id)
        generation_mode = template.get("generation_mode", "random")
        
        hotspots = []
        
        if generation_mode == "preset":
            # 完全使用预设热点
            hotspots = await self._generate_preset_hotspots(db, session, template)
        elif generation_mode == "mixed":
            # 混合模式：部分预设 + 部分随机
            hotspots = await self._generate_mixed_hotspots(db, session, template)
        else:
            # 默认随机生成模式
            hotspots = await self._generate_random_hotspots(db, session)
        
        await db.commit()
        return hotspots
    
    def _get_hotspot_template(self, city_id: str, scene_id: str) -> Dict[str, Any]:
        """获取热点模板配置"""
        # 优先查找场景专用模板
        scene_template = hotspot_templates_config.get(f"scene_templates.{scene_id}.{city_id}")
        if scene_template:
            return scene_template
        
        # 查找城市通用模板
        city_template = hotspot_templates_config.get(f"city_templates.{city_id}.default_scene")
        if city_template:
            return city_template
        
        # 返回默认配置
        return hotspot_templates_config.get("default", {"generation_mode": "random"})
    
    async def _generate_preset_hotspots(self, db: AsyncSession, session: GameSession, template: Dict[str, Any]) -> List[HotspotRecord]:
        """生成预设热点"""
        hotspots = []
        preset_hotspots = template.get("hotspots", [])
        
        # 获取场景热点配置用于验证
        scene_config = scene_hotspots_config.get(f"scenes.{session.scene_id}")
        available_hotspots = scene_config.get("available_hotspots", {}) if scene_config else {}
        
        for hotspot_data in preset_hotspots:
            hotspot_type = HotspotType(hotspot_data["type"])
            hotspot_name = hotspot_data["name"]
            
            # 验证热点名称是否在场景配置中
            if available_hotspots:
                type_str = hotspot_data["type"]
                valid_names = available_hotspots.get(type_str, [])
                if valid_names and hotspot_name not in valid_names:
                    logger.warning(f"Hotspot name '{hotspot_name}' not found in scene config for type '{type_str}', using anyway")
            
            hotspot = HotspotRecord(
                session_id=session.id,
                name=hotspot_name,
                hotspot_type=hotspot_type,
                position_x=hotspot_data["position"]["x"],
                position_y=hotspot_data["position"]["y"],
                reward_type=RewardType(hotspot_data["reward"]["type"]),
                reward_amount=hotspot_data["reward"]["amount"]
            )
            db.add(hotspot)
            hotspots.append(hotspot)
        
        return hotspots
    
    async def _generate_mixed_hotspots(self, db: AsyncSession, session: GameSession, template: Dict[str, Any]) -> List[HotspotRecord]:
        """生成混合热点（部分预设 + 部分随机）"""
        hotspots = []
        
        # 先生成预设热点
        preset_hotspots = template.get("hotspots", [])
        preset_count = template.get("preset_count", len(preset_hotspots))
        
        for i, hotspot_data in enumerate(preset_hotspots[:preset_count]):
            hotspot_type = HotspotType(hotspot_data["type"])
            
            hotspot = HotspotRecord(
                session_id=session.id,
                name=hotspot_data["name"],
                hotspot_type=hotspot_type,
                position_x=hotspot_data["position"]["x"],
                position_y=hotspot_data["position"]["y"],
                reward_type=RewardType(hotspot_data["reward"]["type"]),
                reward_amount=hotspot_data["reward"]["amount"]
            )
            db.add(hotspot)
            hotspots.append(hotspot)
        
        # 然后生成随机热点
        random_count = template.get("random_count", 10)
        random_hotspots = await self._generate_random_hotspots_count(db, session, random_count, len(hotspots) + 1)
        hotspots.extend(random_hotspots)
        
        return hotspots
    
    async def _generate_random_hotspots(self, db: AsyncSession, session: GameSession) -> List[HotspotRecord]:
        """生成随机热点（原有逻辑）"""
        hotspot_config = game_config.get("hotspots", {})
        
        # 热点数量
        min_hotspots = hotspot_config.get("min_per_session", 15)
        max_hotspots = hotspot_config.get("max_per_session", 25)
        hotspot_count = random.randint(min_hotspots, max_hotspots)
        
        return await self._generate_random_hotspots_count(db, session, hotspot_count)
    
    async def _generate_random_hotspots_count(self, db: AsyncSession, session: GameSession, count: int, start_index: int = 1) -> List[HotspotRecord]:
        """生成指定数量的随机热点 - 优先使用场景配置中的热点名称"""
        hotspots = []
        
        # 获取场景热点配置
        scene_config = scene_hotspots_config.get(f"scenes.{session.scene_id}")
        
        if scene_config and scene_config.get("available_hotspots"):
            # 使用场景预定义的热点名称
            hotspots = await self._generate_hotspots_from_scene_config(db, session, scene_config)
        else:
            # 回退到原有的随机生成逻辑
            hotspots = await self._generate_hotspots_legacy(db, session, count, start_index)
        
        return hotspots
    
    async def _generate_hotspots_from_scene_config(self, db: AsyncSession, session: GameSession, scene_config: Dict[str, Any]) -> List[HotspotRecord]:
        """根据场景精细配置生成热点 - 支持精确数量控制和位置模式配置"""
        hotspots = []
        
        # 获取热点配置
        hotspot_config = scene_config.get("hotspot_config", {})
        if not hotspot_config:
            logger.warning(f"场景 {session.scene_id} 没有热点配置")
            return []
        
        # 获取用户已收集的热点名称
        collected_result = await db.execute(
            select(UserHotspotCollection.hotspot_name).where(
                and_(
                    UserHotspotCollection.user_id == session.user_id,
                    UserHotspotCollection.city_id == session.city_id,
                    UserHotspotCollection.scene_id == session.scene_id
                )
            )
        )
        collected_hotspot_names = {row[0] for row in collected_result.fetchall()}
        
        logger.info(f"用户 {session.user_id} 在场景 {session.scene_id} 已收集热点: {collected_hotspot_names}")
        
        # 记录已使用的位置（用于避免重叠）
        used_positions = []
        
        # 遍历每种热点类型配置
        hotspot_types_config = hotspot_config.get("hotspot_types", {})
        for hotspot_type_str, type_config in hotspot_types_config.items():
            # 检查是否启用
            if not type_config.get("enabled", False):
                continue
                
            # 获取精确数量
            count = type_config.get("count", 0)
            if count <= 0:
                continue
                
            try:
                hotspot_type = HotspotType(hotspot_type_str)
            except ValueError:
                logger.warning(f"无效的热点类型: {hotspot_type_str}")
                continue
            
            # 获取可用名称
            available_names = type_config.get("available_names", [])
            if not available_names:
                logger.warning(f"{hotspot_type_str} 类型没有配置可用名称")
                continue
            
            # 过滤已收集的热点
            uncollected_names = [name for name in available_names if name not in collected_hotspot_names]
            if not uncollected_names:
                logger.info(f"用户已收集完所有 {hotspot_type_str} 类型的热点")
                continue
            
            # 选择热点名称（精确数量）
            actual_count = min(count, len(uncollected_names))
            selected_names = uncollected_names[:actual_count]  # 使用前N个而不是随机选择
            
            logger.info(f"为 {hotspot_type_str} 类型生成 {len(selected_names)} 个热点: {selected_names}")
            
            # 为每个名称生成热点
            for name in selected_names:
                # 生成位置
                position = await self._generate_hotspot_position(
                    name, type_config, used_positions, session.city_id, session.scene_id
                )
                
                if position is None:
                    logger.warning(f"无法为热点 {name} 生成合适的位置")
                    continue
                
                # 生成奖励
                reward_type, reward_amount = self._generate_hotspot_reward(hotspot_type, type_config)
                
                # 创建热点记录
                hotspot = HotspotRecord(
                    session_id=session.id,
                    name=name,
                    hotspot_type=hotspot_type,
                    position_x=position["x"],
                    position_y=position["y"],
                    reward_type=reward_type,
                    reward_amount=reward_amount
                )
                db.add(hotspot)
                hotspots.append(hotspot)
                
                # 记录已使用位置
                used_positions.append(position)
        
        logger.info(f"场景 {session.scene_id} 总共生成 {len(hotspots)} 个热点")
        return hotspots

    async def _generate_hotspot_position(self, name: str, type_config: Dict[str, Any], used_positions: List[Dict[str, float]], city_id: str, scene_id: str) -> Optional[Dict[str, float]]:
        """根据配置生成热点位置"""
        position_mode = type_config.get("position_mode", "random")
        position_config = type_config.get("position_config", {})
        
        if position_mode == "fixed":
            # 固定位置模式
            fixed_pos = position_config.get("fixed_position")
            if fixed_pos and "x" in fixed_pos and "y" in fixed_pos:
                return {"x": fixed_pos["x"], "y": fixed_pos["y"]}
            else:
                logger.warning(f"固定位置配置错误: {position_config}")
                return {"x": 0.5, "y": 0.5}  # 默认中心位置
                
        elif position_mode == "predefined":
            # 预定义位置模式
            predefined_positions = position_config.get("predefined_positions", [])
            if predefined_positions:
                # 基于名称选择位置（确保一致性）
                import hashlib
                name_hash = int(hashlib.md5(name.encode()).hexdigest(), 16)
                position_index = name_hash % len(predefined_positions)
                return predefined_positions[position_index]
            else:
                logger.warning(f"预定义位置列表为空: {position_config}")
                return {"x": 0.5, "y": 0.5}
                
        elif position_mode == "random":
            # 随机位置模式
            return await self._generate_random_position(position_config, used_positions)
            
        else:
            logger.warning(f"未知的位置模式: {position_mode}")
            return {"x": random.random(), "y": random.random()}

    async def _generate_random_position(self, position_config: Dict[str, Any], used_positions: List[Dict[str, float]]) -> Dict[str, float]:
        """生成随机位置，支持避免重叠"""
        # 获取随机区域配置
        random_area = position_config.get("random_area", {})
        x_min = random_area.get("x_min", 0.1)
        x_max = random_area.get("x_max", 0.9)
        y_min = random_area.get("y_min", 0.1)
        y_max = random_area.get("y_max", 0.9)
        
        # 避免重叠配置
        avoid_overlap = position_config.get("avoid_overlap", {})
        avoid_enabled = avoid_overlap.get("enabled", True)
        min_distance = avoid_overlap.get("min_distance", 0.15)
        max_attempts = avoid_overlap.get("max_attempts", 50)
        
        for attempt in range(max_attempts):
            # 生成随机位置
            x = random.uniform(x_min, x_max)
            y = random.uniform(y_min, y_max)
            new_position = {"x": x, "y": y}
            
            # 检查是否需要避免重叠
            if not avoid_enabled or not used_positions:
                return new_position
            
            # 检查与已使用位置的距离
            too_close = False
            for used_pos in used_positions:
                distance = ((x - used_pos["x"]) ** 2 + (y - used_pos["y"]) ** 2) ** 0.5
                if distance < min_distance:
                    too_close = True
                    break
            
            if not too_close:
                return new_position
        
        # 如果所有尝试都失败，返回一个基本随机位置
        logger.warning(f"在 {max_attempts} 次尝试后仍无法找到不重叠的位置，返回随机位置")
        return {"x": random.uniform(x_min, x_max), "y": random.uniform(y_min, y_max)}

    def _generate_hotspot_reward(self, hotspot_type: HotspotType, type_config: Dict[str, Any]) -> Tuple[str, int]:
        """根据配置生成热点奖励"""
        reward_config = type_config.get("reward_config", {})
        
        # 获取奖励类型
        reward_type = reward_config.get("type", "gold")
        
        # 获取奖励数量范围
        amount_range = reward_config.get("amount_range", {})
        min_amount = amount_range.get("min", 10)
        max_amount = amount_range.get("max", 20)
        
        # 生成随机奖励数量
        reward_amount = random.randint(min_amount, max_amount)
        
        return reward_type, reward_amount
    
    async def _generate_hotspots_legacy(self, db: AsyncSession, session: GameSession, count: int, start_index: int = 1) -> List[HotspotRecord]:
        """原有的随机热点生成逻辑（作为备用方案）"""
        hotspots = []
        hotspot_config = game_config.get("hotspots", {})
        types_config = hotspot_config.get("types", {})
        
        # 用于生成唯一名称的计数器
        type_counters = {}
        
        for i in range(count):
            # 根据概率选择类型
            hotspot_type = self._choose_hotspot_type(types_config)
            
            # 生成位置（简化为0-1的坐标）
            position_x = random.random()
            position_y = random.random()
            
            # 确定奖励
            reward_type, reward_amount = self._determine_reward(hotspot_type, types_config)
            
            # 生成热点名称
            type_name = hotspot_type
            type_counters[type_name] = type_counters.get(type_name, 0) + 1
            hotspot_name = f"{type_name}_{type_counters[type_name] + start_index - 1}"
            
            hotspot = HotspotRecord(
                session_id=session.id,
                name=hotspot_name,
                hotspot_type=hotspot_type,
                position_x=position_x,
                position_y=position_y,
                reward_type=reward_type,
                reward_amount=reward_amount
            )
            db.add(hotspot)
            hotspots.append(hotspot)
        
        return hotspots
    
    def _choose_hotspot_type(self, types_config: Dict) -> HotspotType:
        """根据概率选择热点类型"""
        probabilities = {
            HotspotType.THIEF: types_config.get("thief", {}).get("probability", 0.6),
            HotspotType.GARBAGE: types_config.get("garbage", {}).get("probability", 0.3),
            HotspotType.TREASURE: types_config.get("treasure", {}).get("probability", 0.08),
            HotspotType.BOSS_THIEF: types_config.get("boss_thief", {}).get("probability", 0.02)
        }
        
        rand = random.random()
        cumulative = 0
        for hotspot_type, prob in probabilities.items():
            cumulative += prob
            if rand < cumulative:
                return hotspot_type
        
        return HotspotType.THIEF  # 默认
    
    def _determine_reward(self, hotspot_type: HotspotType, types_config: Dict) -> tuple:
        """确定奖励类型和数量"""
        type_config = types_config.get(hotspot_type, {})
        rewards_config = type_config.get("rewards", {})
        
        if "gold" in rewards_config:
            amount = random.randint(
                rewards_config["gold"].get("min", 10),
                rewards_config["gold"].get("max", 50)
            )
            return RewardType.GOLD, amount
        elif "diamond" in rewards_config:
            amount = random.randint(
                rewards_config["diamond"].get("min", 1),
                rewards_config["diamond"].get("max", 5)
            )
            return RewardType.DIAMOND, amount
        
        return RewardType.GOLD, 10  # 默认
    
    def _hotspot_to_dict(self, hotspot: HotspotRecord) -> Dict[str, Any]:
        """热点转换为字典"""
        return {
            "id": str(hotspot.id),
            "name": hotspot.name,
            "type": hotspot.hotspot_type,
            "position": {
                "x": float(hotspot.position_x),
                "y": float(hotspot.position_y)
            },
            "reward_preview": {
                "type": hotspot.reward_type,
                "amount": hotspot.reward_amount
            }
        }

    async def reset_user_scene_hotspots(
        self,
        db: AsyncSession,
        user_id: int,
        city_id: str = "beijing",
        scene_id: str = "scene_level_1"
    ) -> Dict[str, Any]:
        """重置用户场景热点收集记录并重新初始化场景热点数据"""
        try:
            from sqlalchemy import delete

            # 1. 删除用户在指定场景的热点收集记录
            delete_user_stmt = delete(UserHotspotCollection).where(
                UserHotspotCollection.user_id == user_id,
                UserHotspotCollection.city_id == city_id,
                UserHotspotCollection.scene_id == scene_id
            )

            result = await db.execute(delete_user_stmt)
            deleted_user_records = result.rowcount

            # 2. 删除场景的固定热点数据，强制重新生成
            delete_scene_stmt = delete(SceneHotspot).where(
                SceneHotspot.city_id == city_id,
                SceneHotspot.scene_id == scene_id
            )

            result = await db.execute(delete_scene_stmt)
            deleted_scene_hotspots = result.rowcount

            # 3. 重新初始化场景热点数据（使用最新配置）
            await self.initialize_scene_hotspots(db, city_id, scene_id)

            await db.commit()

            # 4. 清理相关缓存
            try:
                # 清理用户相关的游戏会话缓存
                patterns_to_clean = [
                    f"game_session:*",  # 所有游戏会话
                    f"hotspot:*:*",     # 所有热点缓存
                    f"user_hotspots:{user_id}:*"  # 用户热点缓存
                ]

                cleaned_cache_count = 0
                for pattern in patterns_to_clean:
                    keys = await redis_manager.redis_client.keys(pattern)
                    if keys:
                        await redis_manager.delete(*keys)
                        cleaned_cache_count += len(keys)

                logger.info(f"用户 {user_id} 重置场景 {scene_id} 热点，删除 {deleted_user_records} 条用户记录，删除 {deleted_scene_hotspots} 个旧热点，清理 {cleaned_cache_count} 个缓存")

            except Exception as cache_error:
                logger.warning(f"清理缓存时出错: {cache_error}")

            # 5. 获取新生成的热点数据统计
            count_result = await db.execute(
                select(func.count(SceneHotspot.id)).where(
                    SceneHotspot.city_id == city_id,
                    SceneHotspot.scene_id == scene_id
                )
            )
            new_hotspots_count = count_result.scalar() or 0

            return {
                "success": True,
                "message": f"成功重置场景 {scene_id} 的热点收集记录并重新生成热点数据",
                "deleted_user_records": deleted_user_records,
                "deleted_old_hotspots": deleted_scene_hotspots,
                "new_hotspots_count": new_hotspots_count,
                "city_id": city_id,
                "scene_id": scene_id
            }

        except Exception as e:
            logger.error(f"重置用户场景热点失败: {e}")
            return {"error": f"重置失败: {str(e)}"}

    # 🚫 PRD合规性清理：移除大炮属性获取方法 - 复杂大炮系统不符合PRD要求
    
    # 🚫 PRD合规性清理：移除大炮升级方法 - 复杂大炮系统不符合PRD要求



    
    # 🚫 PRD合规性清理：移除大炮切换方法 - 复杂大炮系统不符合PRD要求
    
    # 🚫 PRD合规性清理：移除获取下一个大炮类型方法 - 复杂大炮系统不符合PRD要求
    
    async def _drop_artifact(self, db: AsyncSession, user_id: int, city_id: str) -> Optional[Dict[str, Any]]:
        """文物掉落（击杀BOSS必掉）"""
        try:
            # 获取城市文物配置
            artifacts = await config_service.get_city_artifacts(city_id)
            if not artifacts:
                logger.warning(f"No artifacts configured for city: {city_id}")
                return None
            
            # 权重随机选择文物
            weights = [artifact.get("weight", 1) for artifact in artifacts]
            selected_artifact = random.choices(artifacts, weights=weights)[0]
            
            # 检查是否已拥有
            result = await db.execute(
                select(UserArtifact).where(
                    and_(
                        UserArtifact.user_id == user_id,
                        UserArtifact.artifact_id == selected_artifact["artifact_id"]
                    )
                )
            )
            user_artifact = result.scalar_one_or_none()
            
            if user_artifact:
                # 增加数量
                user_artifact.count += 1
                user_artifact.last_obtained_at = datetime.utcnow()
            else:
                # 新文物
                user_artifact = UserArtifact(
                    user_id=user_id,
                    artifact_id=selected_artifact["artifact_id"],
                    city_id=city_id,
                    count=1
                )
                db.add(user_artifact)
                
                # 更新城市进度
                await self._update_city_progress(db, user_id, city_id)
                
                # 更新排行榜
                await self._update_artifact_leaderboard(user_id)
            
            await db.commit()
            
            # 追踪指标
            await analytics_service.track_artifact_found(
                selected_artifact["artifact_id"],
                selected_artifact.get("rarity", "common")
            )
            
            return {
                "artifact_id": selected_artifact["artifact_id"],
                "name": selected_artifact.get("name", "Unknown"),
                "rarity": selected_artifact.get("rarity", "common"),
                "is_new": user_artifact.count == 1
            }
            
        except Exception as e:
            logger.error(f"Failed to drop artifact: {e}")
            return None
    
    async def _get_or_create_city_progress(
        self, 
        db: AsyncSession, 
        user_id: int, 
        city_id: str
    ) -> UserCityProgress:
        """获取或创建城市进度"""
        result = await db.execute(
            select(UserCityProgress).where(
                and_(
                    UserCityProgress.user_id == user_id,
                    UserCityProgress.city_id == city_id
                )
            )
        )
        progress = result.scalar_one_or_none()
        
        if not progress:
            # 获取城市配置
            city_config = await config_service.get_city_config(city_id)
            total_artifacts = city_config.get("total_artifacts", 16) if city_config else 16
            
            progress = UserCityProgress(
                user_id=user_id,
                city_id=city_id,
                is_unlocked=(city_id == "beijing"),  # 北京默认解锁
                total_artifacts=total_artifacts,
                unlocked_at=datetime.utcnow() if city_id == "beijing" else None
            )
            db.add(progress)
            await db.commit()
        
        return progress
    
    async def _update_city_progress(self, db: AsyncSession, user_id: int, city_id: str):
        """更新城市收集进度"""
        # 获取城市进度
        progress = await self._get_or_create_city_progress(db, user_id, city_id)
        
        # 统计已收集的文物数
        result = await db.execute(
            select(func.count(UserArtifact.id)).where(
                and_(
                    UserArtifact.user_id == user_id,
                    UserArtifact.city_id == city_id
                )
            )
        )
        artifacts_collected = result.scalar() or 0
        
        # 更新进度
        progress.artifacts_collected = artifacts_collected
        progress.completion_rate = (artifacts_collected / progress.total_artifacts) * 100
        
        # 检查是否完成
        if progress.completion_rate >= 100 and not progress.completed_at:
            progress.completed_at = datetime.utcnow()
        
        await db.commit()
    
    async def _update_artifact_leaderboard(self, user_id: int):
        """更新文物图鉴排行榜"""
        # 获取用户文物总数
        async with get_db().session() as session:
            result = await session.execute(
                select(func.count(UserArtifact.id)).where(
                    UserArtifact.user_id == user_id
                )
            )
            artifact_count = result.scalar() or 0
        
        # 更新Redis排行榜（使用文物数量作为分数）
        await redis_manager.update_leaderboard("artifact_collector", str(user_id), float(artifact_count))

    async def import_hotspots_from_json(self, json_data: Dict[str, Any], city_id: str, scene_id: str) -> bool:
        """从JSON数据导入热点模板"""
        try:
            # 验证JSON数据格式
            if not self._validate_hotspot_json(json_data):
                logger.error("Invalid hotspot JSON format")
                return False
            
            # 更新热点模板配置
            template_key = f"scene_templates.{scene_id}.{city_id}"
            current_templates = hotspot_templates_config.get("scene_templates", {})
            
            # 确保路径存在
            if scene_id not in current_templates:
                current_templates[scene_id] = {}
            if city_id not in current_templates[scene_id]:
                current_templates[scene_id][city_id] = {}
            
            # 更新模板数据
            current_templates[scene_id][city_id] = json_data
            hotspot_templates_config.set("scene_templates", current_templates)
            
            # 保存到文件
            hotspot_templates_config.save()
            
            logger.info(f"Successfully imported hotspots template for {city_id}/{scene_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to import hotspots from JSON: {e}")
            return False
    
    def _validate_hotspot_json(self, json_data: Dict[str, Any]) -> bool:
        """验证热点JSON数据格式"""
        required_fields = ["generation_mode"]
        
        # 检查必需字段
        for field in required_fields:
            if field not in json_data:
                return False
        
        # 验证生成模式
        valid_modes = ["preset", "mixed", "random"]
        if json_data["generation_mode"] not in valid_modes:
            return False
        
        # 如果是preset或mixed模式，需要验证hotspots数据
        if json_data["generation_mode"] in ["preset", "mixed"]:
            hotspots = json_data.get("hotspots", [])
            if not isinstance(hotspots, list):
                return False
            
            for hotspot in hotspots:
                if not self._validate_hotspot_data(hotspot):
                    return False
        
        return True
    
    def _validate_hotspot_data(self, hotspot: Dict[str, Any]) -> bool:
        """验证单个热点数据格式"""
        required_fields = ["name", "type", "position", "reward"]
        
        # 检查必需字段
        for field in required_fields:
            if field not in hotspot:
                return False
        
        # 验证热点类型
        valid_types = ["thief", "garbage", "treasure", "boss_thief"]
        if hotspot["type"] not in valid_types:
            return False
        
        # 验证位置数据
        position = hotspot["position"]
        if not isinstance(position, dict) or "x" not in position or "y" not in position:
            return False
        
        try:
            x, y = float(position["x"]), float(position["y"])
            if not (0 <= x <= 1) or not (0 <= y <= 1):
                return False
        except (ValueError, TypeError):
            return False
        
        # 验证奖励数据
        reward = hotspot["reward"]
        if not isinstance(reward, dict) or "type" not in reward or "amount" not in reward:
            return False
        
        valid_reward_types = ["gold", "diamond", "artifact"]
        if reward["type"] not in valid_reward_types:
            return False
        
        try:
            amount = int(reward["amount"])
            if amount < 0:
                return False
        except (ValueError, TypeError):
            return False
        
        return True
    
    async def export_hotspots_template(self, city_id: str, scene_id: str) -> Optional[Dict[str, Any]]:
        """导出热点模板为JSON格式"""
        try:
            template = self._get_hotspot_template(city_id, scene_id)
            return template if template else None
        except Exception as e:
            logger.error(f"Failed to export hotspots template: {e}")
            return None
    
    async def list_available_templates(self) -> Dict[str, Any]:
        """列出所有可用的热点模板"""
        try:
            templates = {
                "scene_templates": hotspot_templates_config.get("scene_templates", {}),
                "city_templates": hotspot_templates_config.get("city_templates", {}),
                "event_templates": hotspot_templates_config.get("event_templates", {}),
                "difficulty_templates": hotspot_templates_config.get("difficulty_templates", {})
            }
            return templates
        except Exception as e:
            logger.error(f"Failed to list templates: {e}")
            return {}

    async def get_scene_available_hotspots(self, scene_id: str) -> Dict[str, Any]:
        """获取场景可用的热点名称"""
        try:
            scene_config = scene_hotspots_config.get(f"scenes.{scene_id}")
            if not scene_config:
                # 返回默认配置
                default_config = scene_hotspots_config.get("default", {})
                return {
                    "scene_id": scene_id,
                    "available_hotspots": {},
                    "recommended_config": default_config.get("default_counts", {}),
                    "is_default": True
                }
            
            return {
                "scene_id": scene_id,
                "available_hotspots": scene_config.get("available_hotspots", {}),
                "recommended_config": scene_config.get("recommended_config", {}),
                "is_default": False
            }
        except Exception as e:
            logger.error(f"Failed to get scene hotspots config: {e}")
            return {
                "scene_id": scene_id,
                "available_hotspots": {},
                "recommended_config": {},
                "error": str(e)
            }
    
    async def validate_hotspot_names(self, scene_id: str, hotspots: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证热点名称是否与场景配置匹配"""
        try:
            scene_config = scene_hotspots_config.get(f"scenes.{scene_id}")
            if not scene_config:
                return {"valid": True, "warnings": [], "message": "No scene config found, validation skipped"}
            
            available_hotspots = scene_config.get("available_hotspots", {})
            warnings = []
            valid_count = 0
            
            for hotspot in hotspots:
                hotspot_type = hotspot.get("type")
                hotspot_name = hotspot.get("name")
                
                if not hotspot_type or not hotspot_name:
                    warnings.append(f"Hotspot missing type or name: {hotspot}")
                    continue
                
                valid_names = available_hotspots.get(hotspot_type, [])
                if valid_names and hotspot_name not in valid_names:
                    warnings.append(f"Hotspot '{hotspot_name}' not found in scene config for type '{hotspot_type}'")
                else:
                    valid_count += 1
            
            return {
                "valid": len(warnings) == 0,
                "valid_count": valid_count,
                "total_count": len(hotspots),
                "warnings": warnings
            }
        except Exception as e:
            logger.error(f"Failed to validate hotspot names: {e}")
            return {"valid": False, "error": str(e)}

    # --- 私有辅助方法 ---

    async def _validate_hotspot(self, db, user, session_id, hotspot_id):
        """验证热点并返回热点信息"""
        try:
            logger.info(f"验证热点: hotspot_id={hotspot_id}, session_id={session_id}, user_id={user.id}")
            # 1. 验证会话
            result = await db.execute(
                select(GameSession).where(
                    and_(
                        GameSession.session_id == session_id,
                        GameSession.user_id == user.id,
                        GameSession.status == SessionStatus.ACTIVE
                    )
                )
            )
            session = result.scalar_one_or_none()

            if not session:
                return {"error": "Invalid or expired session"}

            # 2. 解析热点ID格式
            if hotspot_id.startswith("managed_hotspot_"):
                # 管理后台配置的热点
                logger.info(f"处理管理后台热点ID: {hotspot_id}")
                managed_hotspot_id = int(hotspot_id.replace("managed_hotspot_", ""))

                result = await db.execute(
                    select(SceneHotspot).where(
                        and_(
                            SceneHotspot.id == managed_hotspot_id,
                            SceneHotspot.city_id == session.city_id,
                            SceneHotspot.scene_id == session.scene_id,
                            SceneHotspot.enabled == True  # 只允许收集启用的热点
                        )
                    )
                )
                scene_hotspot = result.scalar_one_or_none()

                if not scene_hotspot:
                    return {"error": "Hotspot not found or disabled"}

                # 检查Redis缓存状态（会话内的收集状态）
                hotspot_key = f"hotspot:{session_id}:{hotspot_id}"
                cached_hotspot = await redis_manager.get_json(hotspot_key)
                if cached_hotspot and cached_hotspot.get("collected", False):
                    return {"error": "Hotspot already collected in this session"}

                # 不再使用管理后台的旧奖励配置，改为使用占位符
                # 实际奖励由新的概率系统计算
                reward = {
                    "type": "placeholder",
                    "amount": 0
                }

                # 处理热点类型
                hotspot_type = scene_hotspot.hotspot_type if isinstance(scene_hotspot.hotspot_type, str) else str(scene_hotspot.hotspot_type)

                # 返回热点信息
                return {
                    "id": hotspot_id,
                    "name": scene_hotspot.hotspot_name,
                    "type": hotspot_type,
                    "reward": reward,
                    "position": {
                        "x": float(scene_hotspot.position_x) if scene_hotspot.position_x else 0.0,
                        "y": float(scene_hotspot.position_y) if scene_hotspot.position_y else 0.0
                    },
                    "scene_hotspot": scene_hotspot,  # 传递原始对象供后续使用
                    "source": "managed"  # 标识来源
                }
            elif hotspot_id.startswith("scene_hotspot_"):
                # 新格式：从场景固定热点查找
                logger.info(f"处理新格式热点ID: {hotspot_id}")
                scene_hotspot_id = int(hotspot_id.replace("scene_hotspot_", ""))

                result = await db.execute(
                    select(SceneHotspot).where(
                        and_(
                            SceneHotspot.id == scene_hotspot_id,
                            SceneHotspot.city_id == session.city_id,
                            SceneHotspot.scene_id == session.scene_id
                        )
                    )
                )
                scene_hotspot = result.scalar_one_or_none()

                if not scene_hotspot:
                    return {"error": "Hotspot not found"}

                # 检查Redis缓存状态（会话内的收集状态）
                hotspot_key = f"hotspot:{session_id}:{hotspot_id}"
                cached_hotspot = await redis_manager.get_json(hotspot_key)
                if cached_hotspot and cached_hotspot.get("collected", False):
                    return {"error": "Hotspot already collected in this session"}

                # 不再使用旧的统一热点服务和数据库奖励配置
                # 所有奖励现在由新的概率系统计算
                reward = {
                    "type": "placeholder",
                    "amount": 0
                }
                
                # 返回热点信息
                return {
                    "id": hotspot_id,
                    "name": scene_hotspot.hotspot_name,
                    "type": scene_hotspot.hotspot_type,
                    "reward": reward,
                    "position": {
                        "x": float(scene_hotspot.position_x),
                        "y": float(scene_hotspot.position_y)
                    },
                    "scene_hotspot": scene_hotspot  # 传递原始对象供后续使用
                }
            else:
                # 处理热点名称格式（如 "thief_5"）或旧格式ID
                logger.info(f"处理热点名称或旧格式ID: {hotspot_id}")

                # 首先尝试按热点名称在SceneHotspot表中查找
                result = await db.execute(
                    select(SceneHotspot).where(
                        and_(
                            SceneHotspot.hotspot_name == hotspot_id,
                            SceneHotspot.city_id == session.city_id,
                            SceneHotspot.scene_id == session.scene_id
                        )
                    )
                )
                scene_hotspot = result.scalar_one_or_none()

                if scene_hotspot:
                    # 找到了对应的场景热点
                    # 使用标准格式的热点ID来检查Redis缓存状态
                    standard_hotspot_id = f"managed_hotspot_{scene_hotspot.id}"
                    hotspot_key = f"hotspot:{session_id}:{standard_hotspot_id}"
                    cached_hotspot = await redis_manager.get_json(hotspot_key)
                    if cached_hotspot and cached_hotspot.get("collected", False):
                        return {"error": "Hotspot already collected in this session"}

                    # 不再使用旧的奖励配置，所有奖励由新的概率系统计算
                    reward = {
                        "type": "placeholder",
                        "amount": 0
                    }
                    
                    # 返回热点信息
                    return {
                        "id": standard_hotspot_id,  # 使用与缓存键一致的标准格式ID
                        "name": scene_hotspot.hotspot_name,
                        "type": scene_hotspot.hotspot_type,
                        "reward": reward,
                        "position": {
                            "x": float(scene_hotspot.position_x),
                            "y": float(scene_hotspot.position_y)
                        },
                        "scene_hotspot": scene_hotspot  # 传递原始对象供后续使用
                    }

                # 如果不是热点名称，尝试作为旧格式处理
                # 首先检查是否是纯数字ID
                try:
                    hotspot_record_id = int(hotspot_id)
                    result = await db.execute(
                        select(HotspotRecord).where(
                            and_(
                                HotspotRecord.id == hotspot_record_id,
                                HotspotRecord.session_id == session.id,
                                HotspotRecord.collected == False
                            )
                        )
                    )
                    hotspot_record = result.scalar_one_or_none()

                    if hotspot_record:
                        # 返回旧格式热点信息
                        return {
                            "name": hotspot_record.name,
                            "type": hotspot_record.hotspot_type.value,
                            "reward": {
                                "type": hotspot_record.reward_type.value,
                                "amount": hotspot_record.reward_amount
                            },
                            "position": {
                                "x": float(hotspot_record.position_x),
                                "y": float(hotspot_record.position_y)
                            }
                        }
                except ValueError:
                    # hotspot_id 不是数字，尝试按名称在HotspotRecord中查找
                    logger.info(f"在HotspotRecord中按名称查找: {hotspot_id}")
                    result = await db.execute(
                        select(HotspotRecord).where(
                            and_(
                                HotspotRecord.name == hotspot_id,
                                HotspotRecord.session_id == session.id,
                                HotspotRecord.collected == False
                            )
                        )
                    )
                    hotspot_record = result.scalar_one_or_none()

                    if hotspot_record:
                        # 返回旧格式热点信息
                        return {
                            "name": hotspot_record.name,
                            "type": hotspot_record.hotspot_type.value,
                            "reward": {
                                "type": hotspot_record.reward_type.value,
                                "amount": hotspot_record.reward_amount
                            },
                            "position": {
                                "x": float(hotspot_record.position_x),
                                "y": float(hotspot_record.position_y)
                            }
                        }

                # 如果在数据库中找不到，但看起来是合法的热点名称，创建临时热点数据
                # 支持格式：thief_X, garbage_X, treasure_X, boss_X 等
                import re
                hotspot_pattern = r'^(thief|garbage|treasure|boss)_\d+$'
                if re.match(hotspot_pattern, hotspot_id):
                    # 从热点名称推断类型
                    hotspot_type = hotspot_id.split('_')[0]
                    
                    # 检查Redis缓存状态（会话内的收集状态）
                    hotspot_key = f"hotspot:{session_id}:{hotspot_id}"
                    cached_hotspot = await redis_manager.get_json(hotspot_key)
                    if cached_hotspot and cached_hotspot.get("collected", False):
                        return {"error": "Hotspot already collected in this session"}
                    
                    # 创建临时热点数据（不存储到数据库）
                    logger.info(f"创建临时热点: {hotspot_id} (类型: {hotspot_type})")
                    
                    # 为新的热点创建缓存记录
                    if not cached_hotspot:
                        temp_hotspot_data = {
                            "id": hotspot_id,
                            "name": hotspot_id,
                            "type": hotspot_type,
                            "collected": False,
                            "source": "xml_dynamic"
                        }
                        await redis_manager.set_json(hotspot_key, temp_hotspot_data, expire=7200)
                    
                    return {
                        "id": hotspot_id,
                        "name": hotspot_id,
                        "type": hotspot_type,
                        "reward": {"type": "placeholder", "amount": 0},
                        "position": {"x": 0.0, "y": 0.0},  # 位置信息由前端XML提供
                        "scene_hotspot": None,  # 没有数据库记录
                        "source": "xml_dynamic"  # 标识为动态XML热点
                    }
                
                # 真正找不到的情况
                return {"error": f"Hotspot not found or already collected: {hotspot_id}"}

        except Exception as e:
            logger.error(f"验证热点失败: {e}")
            return {"error": f"Failed to validate hotspot: {str(e)}"}



    # 🚫 PRD合规性清理：移除弹药相关响应构建 - PRD中没有弹药概念
    def _build_progress_response(self, user: User, reward: Dict):
        return { "success": True, "reward": reward }

    # 🚫 PRD合规性清理：完全删除弹药信息构建方法 - PRD中没有弹药概念

    async def _update_task_progress(self, db: AsyncSession, user: User, task_type: str, increment: int = 1):
        """更新任务进度"""
        try:
            # 延迟导入避免循环依赖
            from app.services.task_service import task_service
            
            # 更新任务进度
            result = await task_service.update_task_progress_enhanced(db, user, task_type, increment)
            
            if "error" in result:
                logger.warning(f"更新任务进度失败: {result['error']}")
            else:
                logger.info(f"任务进度更新成功: user={user.id}, type={task_type}, increment={increment}")
                
        except Exception as e:
            logger.error(f"更新任务进度异常: {e}")


# 创建全局游戏服务实例
game_service = GameService() 