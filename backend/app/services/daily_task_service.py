"""
每日任务系统服务
根据PRD需求实现完整的每日任务管理、进度跟踪和奖励系统
"""
import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, date, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, insert, delete, and_, or_
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.core.redis_client import redis_manager
from app.models.daily_task import (
    DailyTaskTemplate, UserDailyTask, TaskProgressLog, TaskRewardHistory,
    TaskType, TaskStatus
)
from app.models.user import User
from app.services.guardian_experience_service import guardian_experience_service

logger = logging.getLogger(__name__)


class DailyTaskService:
    """每日任务系统服务"""
    
    def __init__(self):
        # 任务配置（根据PRD）
        self.task_configs = {
            TaskType.CATCH_THIEF: {
                "targets": [15, 30, 60],          # 目标数量
                "experiences": [50, 120, 300],    # 经验奖励
                "estimated_times": [15, 30, 60], # 预估时间(分钟)
                "efficiency": [3.3, 4, 5]        # 效率(点/分钟)
            },
            TaskType.CLEAN_RUBBISH: {
                "targets": [20, 40, 80],
                "experiences": [40, 100, 250],
                "estimated_times": [20, 40, 80],
                "efficiency": [2, 2.5, 3.1]
            },
            TaskType.CULTURAL_QUIZ: {
                "targets": [2, 5, 10],
                "experiences": [80, 200, 500],
                "estimated_times": [10, 25, 50],
                "efficiency": [8, 8, 10]
            },
            TaskType.PERFECT_CLEAR: {
                "targets": [1, 3, 5],
                "experiences": [100, 300, 800],
                "estimated_times": [30, 90, 150],
                "efficiency": [3.3, 3.3, 5.3]
            }
        }
    
    # ==================== 每日任务生成 ====================
    
    async def generate_daily_tasks(self, user_id: int, task_date: date = None) -> Dict[str, Any]:
        """生成每日任务"""
        if not task_date:
            task_date = date.today()
        
        try:
            async for db in get_db():
                # 检查是否已生成今日任务
                existing_tasks = await self._get_user_daily_tasks(db, user_id, task_date)
                if existing_tasks:
                    return {
                        "success": True,
                        "message": "今日任务已存在",
                        "tasks": [await self._format_task_info(task) for task in existing_tasks]
                    }
                
                # 获取用户信息（用于任务难度判断）
                user = await self._get_user(db, user_id)
                if not user:
                    return {"error": "用户不存在", "code": "USER_NOT_FOUND"}
                
                # 根据用户等级生成任务
                generated_tasks = []
                for task_type in TaskType:
                    task = await self._create_user_task(
                        db, user_id, task_type, task_date, user.guardian_level
                    )
                    if task:
                        generated_tasks.append(task)
                
                await db.commit()
                
                # 缓存任务信息
                await self._cache_user_tasks(user_id, generated_tasks, task_date)
                
                logger.info(f"📋 生成每日任务: user_id={user_id}, count={len(generated_tasks)}")
                
                return {
                    "success": True,
                    "message": f"生成{len(generated_tasks)}个每日任务",
                    "tasks": [await self._format_task_info(task) for task in generated_tasks],
                    "task_date": task_date.isoformat()
                }
                
        except Exception as e:
            logger.error(f"生成每日任务失败 user_id={user_id}: {e}")
            return {"error": "生成任务失败", "code": "TASK_GENERATION_ERROR"}
    
    async def _create_user_task(
        self, 
        db: AsyncSession, 
        user_id: int, 
        task_type: TaskType, 
        task_date: date,
        user_level: int
    ) -> Optional[UserDailyTask]:
        """创建用户任务"""
        try:
            config = self.task_configs.get(task_type)
            if not config:
                return None
            
            # 根据用户等级选择难度
            difficulty_index = self._calculate_task_difficulty(user_level, task_type)
            difficulty_index = min(difficulty_index, len(config["targets"]) - 1)
            
            task = UserDailyTask(
                user_id=user_id,
                template_id=f"template_{task_type.value}_{difficulty_index}",
                task_date=datetime.combine(task_date, datetime.min.time()),
                task_type=task_type,
                task_name=self._get_task_name(task_type, difficulty_index),
                task_description=self._get_task_description(task_type, difficulty_index),
                target_amount=config["targets"][difficulty_index],
                base_experience=config["experiences"][difficulty_index],
                status=TaskStatus.ACTIVE
            )
            
            db.add(task)
            await db.flush()  # 获取ID但不提交
            
            return task
            
        except Exception as e:
            logger.error(f"创建用户任务失败 task_type={task_type}: {e}")
            return None
    
    def _calculate_task_difficulty(self, user_level: int, task_type: TaskType) -> int:
        """根据用户等级计算任务难度"""
        # 基础难度映射
        if user_level <= 3:
            return 0  # 简单
        elif user_level <= 6:
            return 1  # 中等
        else:
            return 2  # 困难
    
    def _get_task_name(self, task_type: TaskType, difficulty: int) -> str:
        """获取任务名称"""
        names = {
            TaskType.CATCH_THIEF: ["新手守卫", "熟练守卫", "精英守卫"],
            TaskType.CLEAN_RUBBISH: ["环保新人", "清洁能手", "环保专家"],
            TaskType.CULTURAL_QUIZ: ["文化学徒", "文化学者", "文化大师"],
            TaskType.PERFECT_CLEAR: ["通关新手", "通关达人", "完美守护者"]
        }
        return names.get(task_type, ["任务"])[difficulty]
    
    def _get_task_description(self, task_type: TaskType, difficulty: int) -> str:
        """获取任务描述"""
        config = self.task_configs.get(task_type)
        if not config:
            return ""
        
        descriptions = {
            TaskType.CATCH_THIEF: f"抓捕{config['targets'][difficulty]}个小偷，保护城市安全",
            TaskType.CLEAN_RUBBISH: f"清理{config['targets'][difficulty]}个垃圾，净化城市环境",
            TaskType.CULTURAL_QUIZ: f"完成{config['targets'][difficulty]}次文化学习，传承文化知识",
            TaskType.PERFECT_CLEAR: f"完美通关{config['targets'][difficulty]}个关卡，展现守护者实力"
        }
        return descriptions.get(task_type, "完成指定任务目标")
    
    # ==================== 任务进度管理 ====================
    
    async def update_task_progress(
        self,
        user_id: int,
        task_type: TaskType,
        progress_amount: int = 1,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """更新任务进度"""
        try:
            async for db in get_db():
                # 获取今日活跃任务
                today = date.today()
                tasks = await self._get_user_daily_tasks_by_type(db, user_id, task_type, today)
                
                if not tasks:
                    return {
                        "success": True,
                        "message": "没有相关的活跃任务",
                        "updated_tasks": []
                    }
                
                updated_tasks = []
                for task in tasks:
                    if task.status != TaskStatus.ACTIVE:
                        continue
                    
                    # 记录进度变化
                    old_progress = task.current_progress
                    task.update_progress(progress_amount)
                    
                    # 记录进度日志
                    await self._log_task_progress(
                        db, user_id, task.id, task_type.value,
                        old_progress, task.current_progress, progress_amount,
                        context
                    )
                    
                    updated_tasks.append({
                        "task_id": task.id,
                        "task_name": task.task_name,
                        "progress_before": old_progress,
                        "progress_after": task.current_progress,
                        "target_amount": task.target_amount,
                        "completion_rate": task.completion_rate,
                        "completed": task.status == TaskStatus.COMPLETED
                    })
                
                await db.commit()
                
                # 更新缓存
                if updated_tasks:
                    await self._update_task_cache(user_id, today)
                
                return {
                    "success": True,
                    "updated_count": len(updated_tasks),
                    "updated_tasks": updated_tasks
                }
                
        except Exception as e:
            logger.error(f"更新任务进度失败 user_id={user_id}, type={task_type}: {e}")
            return {"error": "更新进度失败", "code": "PROGRESS_UPDATE_ERROR"}
    
    async def _log_task_progress(
        self,
        db: AsyncSession,
        user_id: int,
        task_id: str,
        action_type: str,
        progress_before: int,
        progress_after: int,
        progress_delta: int,
        context: Dict[str, Any] = None
    ):
        """记录任务进度日志"""
        try:
            log = TaskProgressLog(
                user_id=user_id,
                task_id=task_id,
                action_type=action_type,
                progress_before=progress_before,
                progress_after=progress_after,
                progress_delta=progress_delta,
                session_id=context.get("session_id") if context else None,
                city_id=context.get("city_id") if context else None,
                level_type=context.get("level_type") if context else None
            )
            
            db.add(log)
            
        except Exception as e:
            logger.error(f"记录任务进度日志失败: {e}")
    
    # ==================== 任务奖励系统 ====================
    
    async def claim_task_reward(
        self,
        user_id: int,
        task_id: str,
        use_ad_double: bool = False
    ) -> Dict[str, Any]:
        """领取任务奖励"""
        try:
            async for db in get_db():
                # 获取任务
                task = await self._get_user_task_by_id(db, task_id)
                if not task or task.user_id != user_id:
                    return {"error": "任务不存在", "code": "TASK_NOT_FOUND"}
                
                # 检查是否可以领取奖励
                if not task.can_claim_reward():
                    return {
                        "error": "无法领取奖励",
                        "code": "CANNOT_CLAIM_REWARD",
                        "task_status": task.status.value,
                        "already_claimed": task.claimed_at is not None
                    }
                
                # 领取奖励
                experience_gained = task.claim_reward(use_ad_double)
                
                # 通过经验系统给予奖励
                exp_result = await guardian_experience_service.gain_task_experience(
                    user_id, 
                    task_id, 
                    task.base_experience,
                    use_ad_double,
                    {
                        "task_type": task.task_type.value,
                        "task_name": task.task_name,
                        "completion_rate": task.completion_rate
                    }
                )
                
                # 记录奖励历史
                await self._record_reward_history(
                    db, user_id, task_id, experience_gained, use_ad_double, task
                )
                
                await db.commit()
                
                # 更新缓存
                await self._update_task_cache(user_id, task.task_date.date())
                
                logger.info(f"🎁 领取任务奖励: user_id={user_id}, task={task.task_name}, exp={experience_gained}")
                
                return {
                    "success": True,
                    "task_id": task_id,
                    "task_name": task.task_name,
                    "experience_gained": experience_gained,
                    "was_doubled": use_ad_double,
                    "experience_result": exp_result,
                    "claimed_at": task.claimed_at.isoformat()
                }
                
        except Exception as e:
            logger.error(f"领取任务奖励失败 user_id={user_id}, task_id={task_id}: {e}")
            return {"error": "领取奖励失败", "code": "REWARD_CLAIM_ERROR"}
    
    async def _record_reward_history(
        self,
        db: AsyncSession,
        user_id: int,
        task_id: str,
        experience_gained: int,
        was_doubled: bool,
        task: UserDailyTask
    ):
        """记录奖励历史"""
        try:
            history = TaskRewardHistory(
                user_id=user_id,
                task_id=task_id,
                experience_gained=experience_gained,
                was_doubled=was_doubled,
                ad_watched=was_doubled,
                task_type=task.task_type,
                task_difficulty=self._get_task_difficulty_from_target(task.task_type, task.target_amount),
                completion_time=int((task.completed_at - task.started_at).total_seconds()) if task.completed_at and task.started_at else 0
            )
            
            db.add(history)
            
        except Exception as e:
            logger.error(f"记录奖励历史失败: {e}")
    
    def _get_task_difficulty_from_target(self, task_type: TaskType, target_amount: int) -> int:
        """根据目标数量获取任务难度"""
        config = self.task_configs.get(task_type)
        if not config:
            return 1
        
        for i, target in enumerate(config["targets"]):
            if target_amount == target:
                return i + 1
        return 1
    
    # ==================== 任务查询 ====================
    
    async def get_user_daily_tasks(self, user_id: int, task_date: date = None) -> Dict[str, Any]:
        """获取用户每日任务"""
        if not task_date:
            task_date = date.today()
        
        try:
            # 先尝试从缓存获取
            cached_tasks = await self._get_cached_tasks(user_id, task_date)
            if cached_tasks:
                return cached_tasks
            
            async for db in get_db():
                tasks = await self._get_user_daily_tasks(db, user_id, task_date)
                
                # 如果没有任务，尝试生成
                if not tasks:
                    generation_result = await self.generate_daily_tasks(user_id, task_date)
                    if generation_result.get("success"):
                        return generation_result
                    else:
                        return {"error": "无法获取或生成任务", "code": "NO_TASKS_AVAILABLE"}
                
                # 检查过期任务
                await self._check_and_expire_tasks(db, tasks)
                await db.commit()
                
                # 格式化任务信息
                formatted_tasks = []
                for task in tasks:
                    formatted_task = await self._format_task_info(task)
                    formatted_tasks.append(formatted_task)
                
                # 缓存结果
                result = {
                    "success": True,
                    "task_date": task_date.isoformat(),
                    "tasks": formatted_tasks,
                    "total_tasks": len(formatted_tasks),
                    "completed_tasks": len([t for t in formatted_tasks if t["status"] == "completed"]),
                    "active_tasks": len([t for t in formatted_tasks if t["status"] == "active"])
                }
                
                await self._cache_task_result(user_id, task_date, result)
                
                return result
                
        except Exception as e:
            logger.error(f"获取用户每日任务失败 user_id={user_id}: {e}")
            return {"error": "获取任务失败", "code": "TASK_FETCH_ERROR"}
    
    async def get_task_statistics(self, user_id: int, days: int = 7) -> Dict[str, Any]:
        """获取任务统计信息"""
        try:
            async for db in get_db():
                end_date = date.today()
                start_date = end_date - timedelta(days=days-1)
                
                # 获取历史任务数据
                result = await db.execute(
                    select(UserDailyTask)
                    .where(
                        UserDailyTask.user_id == user_id,
                        UserDailyTask.task_date >= datetime.combine(start_date, datetime.min.time()),
                        UserDailyTask.task_date <= datetime.combine(end_date, datetime.max.time())
                    )
                    .order_by(UserDailyTask.task_date.desc())
                )
                
                tasks = result.scalars().all()
                
                # 统计数据
                stats = {
                    "total_tasks": len(tasks),
                    "completed_tasks": len([t for t in tasks if t.status == TaskStatus.COMPLETED]),
                    "active_tasks": len([t for t in tasks if t.status == TaskStatus.ACTIVE]),
                    "expired_tasks": len([t for t in tasks if t.status == TaskStatus.EXPIRED]),
                    "completion_rate": 0,
                    "total_experience_earned": sum([t.actual_experience for t in tasks if t.actual_experience]),
                    "ad_doubled_count": len([t for t in tasks if t.is_ad_doubled]),
                    "by_type": {},
                    "by_date": {},
                    "efficiency_analysis": {}
                }
                
                if stats["total_tasks"] > 0:
                    stats["completion_rate"] = round((stats["completed_tasks"] / stats["total_tasks"]) * 100, 2)
                
                # 按类型统计
                for task_type in TaskType:
                    type_tasks = [t for t in tasks if t.task_type == task_type]
                    stats["by_type"][task_type.value] = {
                        "total": len(type_tasks),
                        "completed": len([t for t in type_tasks if t.status == TaskStatus.COMPLETED]),
                        "total_experience": sum([t.actual_experience for t in type_tasks if t.actual_experience])
                    }
                
                # 按日期统计
                for i in range(days):
                    check_date = start_date + timedelta(days=i)
                    date_tasks = [t for t in tasks if t.task_date.date() == check_date]
                    stats["by_date"][check_date.isoformat()] = {
                        "total": len(date_tasks),
                        "completed": len([t for t in date_tasks if t.status == TaskStatus.COMPLETED])
                    }
                
                return {
                    "success": True,
                    "user_id": user_id,
                    "period": f"{start_date.isoformat()} to {end_date.isoformat()}",
                    "statistics": stats
                }
                
        except Exception as e:
            logger.error(f"获取任务统计失败 user_id={user_id}: {e}")
            return {"error": "获取统计失败", "code": "STATS_ERROR"}
    
    # ==================== 辅助方法 ====================
    
    async def _get_user(self, db: AsyncSession, user_id: int) -> Optional[User]:
        """获取用户信息"""
        result = await db.execute(select(User).where(User.id == user_id))
        return result.scalar_one_or_none()
    
    async def _get_user_daily_tasks(self, db: AsyncSession, user_id: int, task_date: date) -> List[UserDailyTask]:
        """获取用户每日任务"""
        result = await db.execute(
            select(UserDailyTask)
            .where(
                UserDailyTask.user_id == user_id,
                UserDailyTask.task_date >= datetime.combine(task_date, datetime.min.time()),
                UserDailyTask.task_date <= datetime.combine(task_date, datetime.max.time())
            )
            .order_by(UserDailyTask.task_type)
        )
        return result.scalars().all()
    
    async def _get_user_daily_tasks_by_type(
        self, 
        db: AsyncSession, 
        user_id: int, 
        task_type: TaskType, 
        task_date: date
    ) -> List[UserDailyTask]:
        """获取指定类型的用户每日任务"""
        result = await db.execute(
            select(UserDailyTask)
            .where(
                UserDailyTask.user_id == user_id,
                UserDailyTask.task_type == task_type,
                UserDailyTask.task_date >= datetime.combine(task_date, datetime.min.time()),
                UserDailyTask.task_date <= datetime.combine(task_date, datetime.max.time())
            )
        )
        return result.scalars().all()
    
    async def _get_user_task_by_id(self, db: AsyncSession, task_id: str) -> Optional[UserDailyTask]:
        """根据ID获取用户任务"""
        result = await db.execute(
            select(UserDailyTask).where(UserDailyTask.id == task_id)
        )
        return result.scalar_one_or_none()
    
    async def _check_and_expire_tasks(self, db: AsyncSession, tasks: List[UserDailyTask]):
        """检查并过期任务"""
        for task in tasks:
            if task.is_expired():
                logger.info(f"⏰ 任务过期: task_id={task.id}, user_id={task.user_id}")
    
    async def _format_task_info(self, task: UserDailyTask) -> Dict[str, Any]:
        """格式化任务信息"""
        config = self.task_configs.get(task.task_type, {})
        
        return {
            "task_id": task.id,
            "task_type": task.task_type.value,
            "task_name": task.task_name,
            "task_description": task.task_description,
            "target_amount": task.target_amount,
            "current_progress": task.current_progress,
            "completion_rate": task.completion_rate,
            "base_experience": task.base_experience,
            "actual_experience": task.actual_experience,
            "status": task.status.value,
            "can_claim_reward": task.can_claim_reward(),
            "is_ad_doubled": task.is_ad_doubled,
            "efficiency_score": task.get_efficiency_score(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "claimed_at": task.claimed_at.isoformat() if task.claimed_at else None
        }
    
    # ==================== 缓存管理 ====================
    
    async def _cache_user_tasks(self, user_id: int, tasks: List[UserDailyTask], task_date: date):
        """缓存用户任务"""
        try:
            cache_key = f"user_daily_tasks:{user_id}:{task_date.isoformat()}"
            task_data = [await self._format_task_info(task) for task in tasks]
            
            await redis_manager.setex(
                cache_key,
                3600,  # 1小时过期
                str(task_data)
            )
            
        except Exception as e:
            logger.error(f"缓存用户任务失败: {e}")
    
    async def _get_cached_tasks(self, user_id: int, task_date: date) -> Optional[Dict[str, Any]]:
        """获取缓存的任务"""
        try:
            cache_key = f"user_daily_tasks:{user_id}:{task_date.isoformat()}"
            cached_data = await redis_manager.get(cache_key)
            
            if cached_data:
                import ast
                task_data = ast.literal_eval(cached_data)
                
                return {
                    "success": True,
                    "task_date": task_date.isoformat(),
                    "tasks": task_data,
                    "cached": True
                }
            
        except Exception as e:
            logger.error(f"获取缓存任务失败: {e}")
        
        return None
    
    async def _update_task_cache(self, user_id: int, task_date: date):
        """更新任务缓存"""
        try:
            cache_key = f"user_daily_tasks:{user_id}:{task_date.isoformat()}"
            await redis_manager.delete(cache_key)  # 删除旧缓存，下次访问时重新生成
            
        except Exception as e:
            logger.error(f"更新任务缓存失败: {e}")
    
    async def _cache_task_result(self, user_id: int, task_date: date, result: Dict[str, Any]):
        """缓存任务查询结果"""
        try:
            cache_key = f"user_daily_tasks:{user_id}:{task_date.isoformat()}"
            await redis_manager.setex(cache_key, 3600, str(result))
            
        except Exception as e:
            logger.error(f"缓存任务结果失败: {e}")


# 全局实例
daily_task_service = DailyTaskService()