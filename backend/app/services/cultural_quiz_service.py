"""
文化教育问答系统服务
根据PRD需求实现完整的文化问答、图鉴收集和古迹修复系统
"""
import asyncio
import logging
import random
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta  
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, insert, delete, and_, or_, func
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.core.redis_client import redis_manager
from app.models.cultural_quiz import (
    CulturalQuiz, QuizAnswer, CulturalArtifact, UserArtifactCollection,
    MonumentRestoration
)
from app.models.user import User
from app.models.treasure_box import TreasureBoxType, DropSource
from app.services.guardian_experience_service import guardian_experience_service
from app.services.treasure_box_service import treasure_box_service

logger = logging.getLogger(__name__)


class CulturalQuizService:
    """文化教育问答系统服务"""
    
    def __init__(self):
        # 经验值配置（根据PRD）
        self.experience_config = {
            "monument_quiz": {
                "base_experience": 35,  # 每题正确35点经验
                "can_double": True,     # 可以广告翻倍
                "stamina_cost": 5       # 消耗5点体力
            }
        }
        
        # 宝箱掉落配置
        self.treasure_box_config = {
            "silver_box_rate": 150,  # 15%掉落银宝箱（150‰）
            "artifact_bonus_rate": 100  # 获得图鉴时100%掉落
        }
        
        # 难度系数
        self.difficulty_multipliers = {
            "easy": 1.0,
            "medium": 1.2,
            "hard": 1.5
        }
    
    # ==================== 问答题库管理 ====================
    
    async def get_quiz_questions(
        self,
        city_id: str,
        count: int = 5,
        difficulty: str = "medium",
        heritage_type: str = None
    ) -> Dict[str, Any]:
        """获取问答题目"""
        try:
            async for db in get_db():
                # 构建查询条件
                query = select(CulturalQuiz).where(
                    CulturalQuiz.city_id == city_id,
                    CulturalQuiz.is_active == True
                )
                
                if difficulty:
                    query = query.where(CulturalQuiz.difficulty == difficulty)
                
                if heritage_type:
                    query = query.where(CulturalQuiz.heritage_type == heritage_type)
                
                result = await db.execute(query)
                available_questions = result.scalars().all()
                
                if len(available_questions) < count:
                    return {
                        "error": "可用题目数量不足",
                        "code": "INSUFFICIENT_QUESTIONS",
                        "available": len(available_questions),
                        "requested": count
                    }
                
                # 随机选择题目
                selected_questions = random.sample(available_questions, count)
                
                # 格式化题目信息（不包含正确答案）
                formatted_questions = []
                for question in selected_questions:
                    formatted_question = {
                        "quiz_id": question.id,
                        "quiz_code": question.quiz_code,
                        "question": question.question,
                        "options": question.options,
                        "difficulty": question.difficulty,
                        "category": question.category,
                        "heritage_type": question.heritage_type,
                        "cultural_value": question.cultural_value,
                        "knowledge_points": question.knowledge_points
                    }
                    formatted_questions.append(formatted_question)
                
                # 缓存题目答案（用于后续验证）
                await self._cache_quiz_answers(selected_questions)
                
                return {
                    "success": True,
                    "city_id": city_id,
                    "questions": formatted_questions,
                    "total_count": len(formatted_questions),
                    "difficulty": difficulty,
                    "heritage_type": heritage_type
                }
                
        except Exception as e:
            logger.error(f"获取问答题目失败 city_id={city_id}: {e}")
            return {"error": "获取题目失败", "code": "QUIZ_FETCH_ERROR"}
    
    async def _cache_quiz_answers(self, questions: List[CulturalQuiz]):
        """缓存问答答案"""
        try:
            for question in questions:
                cache_key = f"quiz_answer:{question.id}"
                answer_data = {
                    "correct_answer": question.correct_answer,
                    "explanation": question.explanation,
                    "quiz_code": question.quiz_code,
                    "cached_at": datetime.utcnow().isoformat()
                }
                
                await redis_manager.setex(cache_key, 3600, str(answer_data))  # 1小时过期
                
        except Exception as e:
            logger.error(f"缓存问答答案失败: {e}")
    
    # ==================== 问答回答系统 ====================
    
    async def submit_quiz_answers(
        self,
        user_id: int,
        session_id: str,
        answers: List[Dict[str, Any]],
        city_id: str = None,
        monument_id: str = None
    ) -> Dict[str, Any]:
        """提交问答答案"""
        try:
            # 验证答案
            verification_results = []
            for answer_data in answers:
                quiz_id = answer_data["quiz_id"]
                user_answer = answer_data["user_answer"]
                answer_time = answer_data.get("answer_time", 0)
                
                verification = await self._verify_quiz_answer(quiz_id, user_answer)
                verification["answer_time"] = answer_time
                verification_results.append(verification)
            
            # 计算统计
            total_questions = len(verification_results)
            correct_answers = len([r for r in verification_results if r["is_correct"]])
            accuracy = (correct_answers / total_questions) * 100 if total_questions > 0 else 0
            
            # 计算经验值奖励
            base_experience_per_correct = self.experience_config["monument_quiz"]["base_experience"]
            total_experience = correct_answers * base_experience_per_correct
            
            # 难度加成
            if verification_results:
                avg_difficulty = self._calculate_average_difficulty(verification_results)
                difficulty_multiplier = self.difficulty_multipliers.get(avg_difficulty, 1.0)
                total_experience = int(total_experience * difficulty_multiplier)
            
            # 记录答题历史
            async for db in get_db():
                quiz_records = []
                for i, result in enumerate(verification_results):
                    answer_record = QuizAnswer(
                        user_id=user_id,
                        quiz_id=answers[i]["quiz_id"],
                        session_id=session_id,
                        user_answer=answers[i]["user_answer"],
                        is_correct=result["is_correct"],
                        answer_time=result["answer_time"],
                        experience_gained=base_experience_per_correct if result["is_correct"] else 0
                    )
                    
                    # 检查是否获得图鉴
                    if result["is_correct"] and random.randint(1, 100) <= 30:  # 30%概率获得图鉴
                        artifact = await self._try_award_artifact(db, user_id, city_id, result["quiz_code"])
                        if artifact:
                            answer_record.artifact_gained = artifact["artifact_code"]
                    
                    # 检查宝箱掉落
                    if result["is_correct"]:
                        box_drop = await treasure_box_service.try_drop_treasure_box(
                            user_id,
                            DropSource.CULTURAL_QUIZ,
                            1,
                            {
                                "quiz_id": answers[i]["quiz_id"],
                                "session_id": session_id,
                                "city_id": city_id,
                                "correct_answer": True
                            }
                        )
                        if box_drop.get("dropped"):
                            answer_record.treasure_box_dropped = True
                    
                    quiz_records.append(answer_record)
                    db.add(answer_record)
                
                await db.commit()
            
            # 给予经验奖励
            if total_experience > 0:
                exp_result = await guardian_experience_service.gain_monument_experience(
                    user_id,
                    correct_answers,
                    can_double=True,
                    context={
                        "session_id": session_id,
                        "city_id": city_id,
                        "monument_id": monument_id,
                        "total_questions": total_questions,
                        "accuracy": accuracy
                    }
                )
            else:
                exp_result = {"success": True, "experience_gained": 0}
            
            # 更新古迹修复进度（如果有）
            restoration_update = None
            if monument_id:
                restoration_update = await self._update_monument_restoration(
                    user_id, monument_id, city_id, total_questions, correct_answers, total_experience
                )
            
            logger.info(f"📚 问答提交: user_id={user_id}, 正确={correct_answers}/{total_questions}, 经验={total_experience}")
            
            return {
                "success": True,
                "quiz_results": {
                    "total_questions": total_questions,
                    "correct_answers": correct_answers,
                    "accuracy_percentage": round(accuracy, 2),
                    "total_experience": total_experience,
                    "base_experience_per_correct": base_experience_per_correct,
                    "difficulty_multiplier": difficulty_multiplier if 'difficulty_multiplier' in locals() else 1.0
                },
                "detailed_results": verification_results,
                "experience_result": exp_result,
                "restoration_update": restoration_update,
                "artifacts_gained": [r.artifact_gained for r in quiz_records if r.artifact_gained],
                "treasure_boxes_dropped": len([r for r in quiz_records if r.treasure_box_dropped])
            }
            
        except Exception as e:
            logger.error(f"提交问答答案失败 user_id={user_id}: {e}")
            return {"error": "提交答案失败", "code": "QUIZ_SUBMIT_ERROR"}
    
    async def _verify_quiz_answer(self, quiz_id: str, user_answer: str) -> Dict[str, Any]:
        """验证问答答案"""
        try:
            # 从缓存获取正确答案
            cache_key = f"quiz_answer:{quiz_id}"
            cached_answer = await redis_manager.get(cache_key)
            
            if not cached_answer:
                # 从数据库获取
                async for db in get_db():
                    result = await db.execute(
                        select(CulturalQuiz).where(CulturalQuiz.id == quiz_id)
                    )
                    quiz = result.scalar_one_or_none()
                    
                    if not quiz:
                        return {
                            "quiz_id": quiz_id,
                            "is_correct": False,
                            "error": "题目不存在"
                        }
                    
                    correct_answer = quiz.correct_answer
                    explanation = quiz.explanation
                    quiz_code = quiz.quiz_code
                    difficulty = quiz.difficulty
                    break
            else:
                import ast
                answer_data = ast.literal_eval(cached_answer)
                correct_answer = answer_data["correct_answer"]
                explanation = answer_data["explanation"]
                quiz_code = answer_data["quiz_code"]
                difficulty = "medium"  # 默认难度
            
            is_correct = user_answer.upper() == correct_answer.upper()
            
            return {
                "quiz_id": quiz_id,
                "quiz_code": quiz_code,
                "user_answer": user_answer,
                "correct_answer": correct_answer,
                "is_correct": is_correct,
                "explanation": explanation,
                "difficulty": difficulty
            }
            
        except Exception as e:
            logger.error(f"验证问答答案失败 quiz_id={quiz_id}: {e}")
            return {
                "quiz_id": quiz_id,
                "is_correct": False,
                "error": "验证失败"
            }
    
    def _calculate_average_difficulty(self, results: List[Dict]) -> str:
        """计算平均难度"""
        difficulties = [r.get("difficulty", "medium") for r in results if r.get("difficulty")]
        if not difficulties:
            return "medium"
        
        difficulty_scores = {"easy": 1, "medium": 2, "hard": 3}
        avg_score = sum(difficulty_scores.get(d, 2) for d in difficulties) / len(difficulties)
        
        if avg_score <= 1.5:
            return "easy"
        elif avg_score <= 2.5:
            return "medium"
        else:
            return "hard"
    
    # ==================== 文化图鉴系统 ====================
    
    async def _try_award_artifact(
        self,
        db: AsyncSession,
        user_id: int,
        city_id: str,
        quiz_code: str
    ) -> Optional[Dict[str, Any]]:
        """尝试奖励文化图鉴"""
        try:
            # 查找该城市的可用图鉴
            result = await db.execute(
                select(CulturalArtifact)
                .where(
                    CulturalArtifact.city_id == city_id,
                    CulturalArtifact.is_active == True
                )
            )
            artifacts = result.scalars().all()
            
            if not artifacts:
                return None
            
            # 检查用户已收集的图鉴
            collected_result = await db.execute(
                select(UserArtifactCollection.artifact_id)
                .where(UserArtifactCollection.user_id == user_id)
            )
            collected_ids = [row[0] for row in collected_result.fetchall()]
            
            # 筛选未收集的图鉴
            uncollected_artifacts = [a for a in artifacts if a.id not in collected_ids]
            
            if not uncollected_artifacts:
                return None
            
            # 根据稀有度权重选择
            weighted_artifacts = []
            for artifact in uncollected_artifacts:
                weight = {"common": 70, "rare": 25, "epic": 5}.get(artifact.rarity, 50)
                weighted_artifacts.extend([artifact] * weight)
            
            if not weighted_artifacts:
                return None
            
            # 随机选择图鉴
            selected_artifact = random.choice(weighted_artifacts)
            
            # 创建收集记录
            collection = UserArtifactCollection(
                user_id=user_id,
                artifact_id=selected_artifact.id,
                obtained_from="quiz",
                quantity=1
            )
            
            db.add(collection)
            await db.flush()
            
            logger.info(f"🏛️ 获得图鉴: user_id={user_id}, artifact={selected_artifact.name}")
            
            return {
                "artifact_id": selected_artifact.id,
                "artifact_code": selected_artifact.artifact_code,
                "artifact_name": selected_artifact.name,
                "rarity": selected_artifact.rarity,
                "description": selected_artifact.description
            }
            
        except Exception as e:
            logger.error(f"奖励文化图鉴失败: {e}")
            return None
    
    async def get_user_artifacts(self, user_id: int, city_id: str = None) -> Dict[str, Any]:
        """获取用户图鉴收集"""
        try:
            async for db in get_db():
                # 构建查询
                query = select(UserArtifactCollection, CulturalArtifact).join(
                    CulturalArtifact,
                    UserArtifactCollection.artifact_id == CulturalArtifact.id
                ).where(UserArtifactCollection.user_id == user_id)
                
                if city_id:
                    query = query.where(CulturalArtifact.city_id == city_id)
                
                result = await db.execute(query)
                collections = result.fetchall()
                
                # 格式化收集信息
                collected_artifacts = []
                for collection, artifact in collections:
                    artifact_info = {
                        "collection_id": collection.id,
                        "artifact_id": artifact.id,
                        "artifact_code": artifact.artifact_code,
                        "name": artifact.name,
                        "name_en": artifact.name_en,
                        "category": artifact.category,
                        "rarity": artifact.rarity,
                        "description": artifact.description,
                        "cultural_background": artifact.cultural_background,
                        "historical_significance": artifact.historical_significance,
                        "image_url": artifact.image_url,
                        "quantity": collection.quantity,
                        "obtained_at": collection.obtained_at.isoformat(),
                        "obtained_from": collection.obtained_from,
                        "is_new": collection.is_new,
                        "last_viewed_at": collection.last_viewed_at.isoformat() if collection.last_viewed_at else None
                    }
                    collected_artifacts.append(artifact_info)
                
                # 获取收集统计
                if city_id:
                    total_artifacts_result = await db.execute(
                        select(func.count(CulturalArtifact.id))
                        .where(
                            CulturalArtifact.city_id == city_id,
                            CulturalArtifact.is_active == True
                        )
                    )
                    total_artifacts = total_artifacts_result.scalar()
                else:
                    total_artifacts_result = await db.execute(
                        select(func.count(CulturalArtifact.id))
                        .where(CulturalArtifact.is_active == True)
                    )
                    total_artifacts = total_artifacts_result.scalar()
                
                collected_count = len(collected_artifacts)
                collection_rate = (collected_count / total_artifacts * 100) if total_artifacts > 0 else 0
                
                # 按类别统计
                by_category = {}
                by_rarity = {}
                
                for artifact in collected_artifacts:
                    # 按类别
                    category = artifact["category"]
                    if category not in by_category:
                        by_category[category] = []
                    by_category[category].append(artifact)
                    
                    # 按稀有度
                    rarity = artifact["rarity"]
                    if rarity not in by_rarity:
                        by_rarity[rarity] = []
                    by_rarity[rarity].append(artifact)
                
                return {
                    "success": True,
                    "user_id": user_id,
                    "city_id": city_id,
                    "collected_artifacts": collected_artifacts,
                    "collection_statistics": {
                        "collected_count": collected_count,
                        "total_artifacts": total_artifacts,
                        "collection_rate": round(collection_rate, 2),
                        "by_category": {k: len(v) for k, v in by_category.items()},
                        "by_rarity": {k: len(v) for k, v in by_rarity.items()}
                    },
                    "artifacts_by_category": by_category,
                    "artifacts_by_rarity": by_rarity
                }
                
        except Exception as e:
            logger.error(f"获取用户图鉴失败 user_id={user_id}: {e}")
            return {"error": "获取图鉴失败", "code": "ARTIFACT_FETCH_ERROR"}
    
    async def view_artifact(self, user_id: int, collection_id: str) -> Dict[str, Any]:
        """查看图鉴详情"""
        try:
            async for db in get_db():
                # 获取收集记录
                result = await db.execute(
                    select(UserArtifactCollection, CulturalArtifact)
                    .join(CulturalArtifact, UserArtifactCollection.artifact_id == CulturalArtifact.id)
                    .where(
                        UserArtifactCollection.id == collection_id,
                        UserArtifactCollection.user_id == user_id
                    )
                )
                
                collection_data = result.first()
                if not collection_data:
                    return {"error": "图鉴不存在", "code": "ARTIFACT_NOT_FOUND"}
                
                collection, artifact = collection_data
                
                # 标记为已查看
                collection.is_new = False
                collection.last_viewed_at = datetime.utcnow()
                
                await db.commit()
                
                return {
                    "success": True,
                    "artifact": {
                        "collection_id": collection.id,
                        "artifact_id": artifact.id,
                        "artifact_code": artifact.artifact_code,
                        "name": artifact.name,
                        "name_en": artifact.name_en,
                        "category": artifact.category,
                        "rarity": artifact.rarity,
                        "description": artifact.description,
                        "cultural_background": artifact.cultural_background,
                        "historical_significance": artifact.historical_significance,
                        "image_url": artifact.image_url,
                        "audio_url": artifact.audio_url,
                        "video_url": artifact.video_url,
                        "knowledge_points": artifact.knowledge_points if hasattr(artifact, 'knowledge_points') else [],
                        "obtained_at": collection.obtained_at.isoformat(),
                        "obtained_from": collection.obtained_from,
                        "quantity": collection.quantity
                    }
                }
                
        except Exception as e:
            logger.error(f"查看图鉴详情失败 user_id={user_id}, collection_id={collection_id}: {e}")
            return {"error": "查看图鉴失败", "code": "ARTIFACT_VIEW_ERROR"}
    
    # ==================== 古迹修复系统 ====================
    
    async def _update_monument_restoration(
        self,
        user_id: int,
        monument_id: str,
        city_id: str,
        total_questions: int,
        correct_answers: int,
        experience_gained: int
    ) -> Dict[str, Any]:
        """更新古迹修复进度"""
        try:
            async for db in get_db():
                # 获取或创建修复记录
                result = await db.execute(
                    select(MonumentRestoration)
                    .where(
                        MonumentRestoration.user_id == user_id,
                        MonumentRestoration.monument_id == monument_id,
                        MonumentRestoration.is_completed == False
                    )
                )
                
                restoration = result.scalar_one_or_none()
                
                if not restoration:
                    restoration = MonumentRestoration(
                        user_id=user_id,
                        monument_id=monument_id,
                        city_id=city_id,
                        total_questions=total_questions,
                        answered_questions=0,
                        correct_answers=0
                    )
                    db.add(restoration)
                
                # 更新进度
                restoration.answered_questions += total_questions
                restoration.correct_answers += correct_answers
                restoration.total_experience += experience_gained
                
                # 计算修复进度
                restoration.calculate_progress()
                
                await db.commit()
                
                return {
                    "restoration_id": restoration.id,
                    "monument_id": monument_id,
                    "progress_percentage": restoration.restoration_progress,
                    "is_completed": restoration.is_completed,
                    "total_questions": restoration.total_questions,
                    "answered_questions": restoration.answered_questions,
                    "correct_answers": restoration.correct_answers,
                    "accuracy": round((restoration.correct_answers / restoration.answered_questions * 100), 2) if restoration.answered_questions > 0 else 0,
                    "total_experience": restoration.total_experience,
                    "completed_at": restoration.completed_at.isoformat() if restoration.completed_at else None
                }
                
        except Exception as e:
            logger.error(f"更新古迹修复进度失败 user_id={user_id}, monument_id={monument_id}: {e}")
            return {"error": "更新修复进度失败"}
    
    async def get_monument_restoration_status(self, user_id: int, monument_id: str) -> Dict[str, Any]:
        """获取古迹修复状态"""
        try:
            async for db in get_db():
                result = await db.execute(
                    select(MonumentRestoration)
                    .where(
                        MonumentRestoration.user_id == user_id,
                        MonumentRestoration.monument_id == monument_id
                    )
                    .order_by(MonumentRestoration.created_at.desc())
                )
                
                restoration = result.scalar_one_or_none()
                
                if not restoration:
                    return {
                        "success": True,
                        "monument_id": monument_id,
                        "restoration_progress": 0,
                        "is_completed": False,
                        "not_started": True
                    }
                
                return {
                    "success": True,
                    "restoration_id": restoration.id,
                    "monument_id": monument_id,
                    "city_id": restoration.city_id,
                    "restoration_progress": restoration.restoration_progress,
                    "is_completed": restoration.is_completed,
                    "total_questions": restoration.total_questions,
                    "answered_questions": restoration.answered_questions,
                    "correct_answers": restoration.correct_answers,
                    "accuracy": round((restoration.correct_answers / restoration.answered_questions * 100), 2) if restoration.answered_questions > 0 else 0,
                    "total_experience": restoration.total_experience,
                    "artifacts_gained": restoration.artifacts_gained,
                    "created_at": restoration.created_at.isoformat(),
                    "updated_at": restoration.updated_at.isoformat(),
                    "completed_at": restoration.completed_at.isoformat() if restoration.completed_at else None
                }
                
        except Exception as e:
            logger.error(f"获取古迹修复状态失败 user_id={user_id}, monument_id={monument_id}: {e}")
            return {"error": "获取修复状态失败", "code": "RESTORATION_STATUS_ERROR"}
    
    # ==================== 统计和分析 ====================
    
    async def get_quiz_statistics(self, user_id: int, days: int = 7) -> Dict[str, Any]:
        """获取问答统计信息"""
        try:
            async for db in get_db():
                end_date = datetime.utcnow()
                start_date = end_date - timedelta(days=days)
                
                # 获取问答记录
                result = await db.execute(
                    select(QuizAnswer)
                    .where(
                        QuizAnswer.user_id == user_id,
                        QuizAnswer.answered_at >= start_date
                    )
                    .order_by(QuizAnswer.answered_at.desc())
                )
                
                quiz_records = result.scalars().all()
                
                # 计算统计
                total_answered = len(quiz_records)
                correct_answered = len([r for r in quiz_records if r.is_correct])
                total_experience = sum([r.experience_gained for r in quiz_records])
                
                accuracy = (correct_answered / total_answered * 100) if total_answered > 0 else 0
                
                # 按日期统计
                daily_stats = {}
                for record in quiz_records:
                    date_key = record.answered_at.date().isoformat()
                    if date_key not in daily_stats:
                        daily_stats[date_key] = {"answered": 0, "correct": 0, "experience": 0}
                    
                    daily_stats[date_key]["answered"] += 1
                    if record.is_correct:
                        daily_stats[date_key]["correct"] += 1
                    daily_stats[date_key]["experience"] += record.experience_gained
                
                # 图鉴获取统计
                artifacts_gained = len([r for r in quiz_records if r.artifact_gained])
                treasure_boxes_gained = len([r for r in quiz_records if r.treasure_box_dropped])
                
                return {
                    "success": True,
                    "user_id": user_id,
                    "period_days": days,
                    "overall_stats": {
                        "total_answered": total_answered,
                        "correct_answered": correct_answered,
                        "accuracy_percentage": round(accuracy, 2),
                        "total_experience": total_experience,
                        "artifacts_gained": artifacts_gained,
                        "treasure_boxes_gained": treasure_boxes_gained,
                        "average_answer_time": round(sum([r.answer_time for r in quiz_records if r.answer_time]) / len([r for r in quiz_records if r.answer_time]), 2) if quiz_records else 0
                    },
                    "daily_breakdown": daily_stats,
                    "performance_trend": self._calculate_performance_trend(daily_stats)
                }
                
        except Exception as e:
            logger.error(f"获取问答统计失败 user_id={user_id}: {e}")
            return {"error": "获取统计失败", "code": "QUIZ_STATS_ERROR"}
    
    def _calculate_performance_trend(self, daily_stats: Dict[str, Dict]) -> Dict[str, Any]:
        """计算表现趋势"""
        if len(daily_stats) < 2:
            return {"trend": "insufficient_data"}
        
        dates = sorted(daily_stats.keys())
        recent_dates = dates[-3:] if len(dates) >= 3 else dates
        
        accuracies = []
        for date in recent_dates:
            stats = daily_stats[date]
            accuracy = (stats["correct"] / stats["answered"] * 100) if stats["answered"] > 0 else 0
            accuracies.append(accuracy)
        
        if len(accuracies) >= 2:
            if accuracies[-1] > accuracies[0]:
                trend = "improving"
            elif accuracies[-1] < accuracies[0]:
                trend = "declining"
            else:
                trend = "stable"
        else:
            trend = "stable"
        
        return {
            "trend": trend,
            "recent_accuracy": round(sum(accuracies) / len(accuracies), 2) if accuracies else 0,
            "trend_data": list(zip(recent_dates, accuracies))
        }


# 全局实例
cultural_quiz_service = CulturalQuizService()