"""
认证服务模块
处理用户认证、令牌生成、游客登录等功能
"""
from jose import jwt
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Union
import json
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from google.oauth2 import id_token
from google.auth.transport import requests as google_requests
from loguru import logger

from app.core.config import settings, game_config
from app.models.user import User, UserLoginLog, LoginType
# 🚫 PRD合规性清理：移除CannonUpgrade导入 - 复杂大炮系统不符合PRD要求
from app.schemas.auth import UserInfo, CrazyGamesTokenPayload
from app.services.crazygames_service import crazygames_service


class AuthService:
    """认证服务类"""
    
    def __init__(self):
        self.secret_key = settings.JWT_SECRET_KEY
        self.algorithm = settings.JWT_ALGORITHM
        self.access_token_expire_minutes = settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_days = settings.JWT_REFRESH_TOKEN_EXPIRE_DAYS
    
    def _generate_guest_user_id(self, device_id: str) -> str:
        """根据设备ID生成游客用户ID"""
        # 使用MD5哈希生成稳定的用户ID
        hash_object = hashlib.md5(f"guest_{device_id}".encode())
        return f"guest_{hash_object.hexdigest()[:12]}"
    
    def _generate_nickname(self, user_id: str) -> str:
        """生成游客昵称"""
        # 取用户ID后8位作为昵称后缀
        suffix = user_id[-8:]
        return f"游客{suffix}"
    
    def _create_token(self, user_id: str, token_type: str = "access") -> str:
        """创建JWT令牌"""
        now = datetime.utcnow()
        
        if token_type == "access":
            expire = now + timedelta(minutes=self.access_token_expire_minutes)
        else:  # refresh
            expire = now + timedelta(days=self.refresh_token_expire_days)
        
        payload = {
            "user_id": user_id,
            "type": token_type,
            "exp": expire,
            "iat": now
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def _verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证JWT令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.JWTError:
            return None
    
    async def guest_login(
        self,
        db: AsyncSession,
        device_id: str,
        device_info: Optional[Union[str, Dict[str, Any]]] = None,
        login_ip: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        游客登录

        Args:
            db: 数据库会话
            device_id: 设备唯一标识
            device_info: 设备信息，可以是字符串或对象
            login_ip: 登录IP

        Returns:
            包含用户信息和令牌的字典
        """
        try:
            user_id = self._generate_guest_user_id(device_id)
            
            # 查找现有用户
            stmt = select(User).where(User.user_id == user_id)
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                # 创建新的游客用户
                user = User(
                    user_id=user_id,
                    nickname=self._generate_nickname(user_id),
                    login_type="guest",
                    device_id=device_id,
                    guardian_level=1,
                    guardian_exp=0,
                    # 统一使用game_config配置，确保与PRD一致
                    gold=game_config.get("currency", {}).get("initial_gold", 200),
                    diamond=game_config.get("currency", {}).get("initial_diamond", 20),
                    ammo_count=game_config.get("currency", {}).get("initial_ammunition", 50),
                    # 初始化PRD要求的体力系统
                    stamina=120,  # PRD要求初始体力120点
                    max_stamina=120,
                    last_stamina_update=datetime.utcnow(),
                    total_play_time=0,
                    last_login_time=datetime.utcnow(),
                    is_active=True,
                    is_deleted=False
                )
                db.add(user)
                await db.flush()  # 获取用户ID
                
                # 🚫 PRD合规性清理：移除大炮升级记录创建 - 复杂大炮系统不符合PRD要求

                await db.commit()
                await db.refresh(user)
            else:
                # 更新最后登录时间
                user.last_login_time = datetime.utcnow()
                await db.commit()
            
            # 处理设备信息 - 如果是对象则转换为JSON字符串
            device_info_str = device_info
            if isinstance(device_info, dict):
                device_info_str = json.dumps(device_info, ensure_ascii=False)

            # 记录登录日志
            login_log = UserLoginLog(
                user_id=user.id,
                user_uuid=user.user_id,
                login_ip=login_ip,
                device_info=device_info_str,
                login_type=LoginType.GUEST
            )
            db.add(login_log)
            
            # 更新登录任务进度
            await self._update_task_progress(db, user, "login", 1)

            # 在commit前获取用户信息，避免session过期问题
            user_data = {
                "user_id": user.user_id,
                "nickname": user.nickname,
                "level": user.guardian_level,
                "exp": user.guardian_exp,
                "gold": user.gold,
                "diamond": user.diamond,
                "total_play_time": user.total_play_time,
            }

            await db.commit()

            # 生成令牌
            access_token = self._create_token(user_data["user_id"], "access")
            refresh_token = self._create_token(user_data["user_id"], "refresh")

            # 构建用户信息
            user_info = UserInfo(
                user_id=user_data["user_id"],
                nickname=user_data["nickname"],
                level=user_data["level"],
                exp=user_data["exp"],
                vip_level=0,  # 默认VIP等级
                gold=user_data["gold"],
                diamond=user_data["diamond"],
                ammo_count=user.ammo_count,  # 添加弹药数量
                stamina=user.stamina,        # 添加当前体力
                max_stamina=user.max_stamina, # 添加最大体力
                total_play_time=user_data["total_play_time"],
                created_at=user.created_at
            )
            
            return {
                "user_id": user.user_id,
                "token": access_token,
                "refresh_token": refresh_token,
                "user_info": user_info
            }
            
        except Exception as e:
            await db.rollback()
            print(f"❌ 游客登录失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"error": f"登录失败: {str(e)}"}
    
    async def google_auth_login(
        self,
        db: AsyncSession,
        google_token: str,
        device_id: Optional[str] = None,
        device_info: Optional[Union[str, Dict[str, Any]]] = None,
        login_ip: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        谷歌账号登录

        Args:
            db: 数据库会话
            google_token: Google ID Token
            device_id: 设备唯一标识
            device_info: 设备信息
            login_ip: 登录IP

        Returns:
            包含用户信息和令牌的字典
        """
        try:
            # 验证 Google ID Token，添加时钟容差
            try:
                idinfo = id_token.verify_oauth2_token(
                    google_token, 
                    google_requests.Request(), 
                    settings.GOOGLE_CLIENT_ID,
                    clock_skew_in_seconds=60  # 允许60秒的时钟偏差
                )
            except ValueError as e:
                return {"error": f"无效的 Google Token: {str(e)}"}
            
            # 从 token 中获取用户信息
            google_id = idinfo['sub']
            email = idinfo['email']
            name = idinfo['name']
            picture = idinfo.get('picture')
            
            # 生成用户ID（使用 google_ 前缀）
            user_id = f"google_{google_id}"
            
            # 查找现有用户
            stmt = select(User).where(User.user_id == user_id)
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                # 创建新的谷歌用户
                user = User(
                    user_id=user_id,
                    nickname=name,
                    email=email,
                    avatar_url=picture,
                    login_type="google",
                    device_id=device_id,
                    guardian_level=1,
                    guardian_exp=0,
                    # 统一使用game_config配置，确保与PRD一致
                    gold=game_config.get("currency", {}).get("initial_gold", 200),
                    diamond=game_config.get("currency", {}).get("initial_diamond", 20),
                    ammo_count=game_config.get("currency", {}).get("initial_ammunition", 50),
                    # 初始化PRD要求的体力系统
                    stamina=120,  # PRD要求初始体力120点
                    max_stamina=120,
                    last_stamina_update=datetime.utcnow(),
                    total_play_time=0,
                    last_login_time=datetime.utcnow(),
                    is_active=True,
                    is_deleted=False
                )
                db.add(user)
                await db.flush()
                
                # 🚫 PRD合规性清理：移除大炮升级记录创建 - 复杂大炮系统不符合PRD要求

                await db.commit()
                await db.refresh(user)
            else:
                # 更新用户信息
                user.nickname = name
                user.avatar_url = picture
                user.last_login_time = datetime.utcnow()
                await db.commit()
            
            # 处理设备信息
            device_info_str = device_info
            if isinstance(device_info, dict):
                device_info_str = json.dumps(device_info, ensure_ascii=False)

            # 记录登录日志
            login_log = UserLoginLog(
                user_id=user.id,
                user_uuid=user.user_id,
                login_ip=login_ip,
                device_info=device_info_str,
                login_type=LoginType.GOOGLE
            )
            db.add(login_log)
            
            # 更新登录任务进度
            await self._update_task_progress(db, user, "login", 1)

            # 在commit前获取用户信息，避免session过期问题
            user_data = {
                "user_id": user.user_id,
                "nickname": user.nickname,
                "avatar_url": user.avatar_url,
                "level": user.guardian_level,
                "exp": user.guardian_exp,
                "gold": user.gold,
                "diamond": user.diamond,
                "total_play_time": user.total_play_time,
                "created_at": user.created_at
            }

            await db.commit()

            # 生成令牌
            access_token = self._create_token(user_data["user_id"], "access")
            refresh_token = self._create_token(user_data["user_id"], "refresh")

            # 构建用户信息
            user_info = UserInfo(
                user_id=user_data["user_id"],
                nickname=user_data["nickname"],
                avatar_url=user_data["avatar_url"],
                level=user_data["level"],
                exp=user_data["exp"],
                vip_level=0,  # 默认VIP等级
                gold=user_data["gold"],
                diamond=user_data["diamond"],
                ammo_count=user.ammo_count,  # 添加弹药数量
                stamina=user.stamina,        # 添加当前体力
                max_stamina=user.max_stamina, # 添加最大体力
                total_play_time=user_data["total_play_time"],
                created_at=user_data["created_at"]
            )
            
            return {
                "user_id": user.user_id,
                "token": access_token,
                "refresh_token": refresh_token,
                "user_info": user_info,
                "google_info": {
                    "google_id": google_id,
                    "email": email,
                    "name": name,
                    "picture": picture
                }
            }
            
        except Exception as e:
            await db.rollback()
            return {"error": f"谷歌登录失败: {str(e)}"}

    async def crazygames_login(
        self,
        db: AsyncSession,
        crazygames_token: str,
        device_id: Optional[str] = None,
        device_info: Optional[Union[str, Dict[str, Any]]] = None,
        login_ip: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        CrazyGames账号登录

        Args:
            db: 数据库会话
            crazygames_token: CrazyGames Token
            device_id: 设备唯一标识
            device_info: 设备信息
            login_ip: 登录IP

        Returns:
            包含用户信息和令牌的字典
        """
        try:
            # 验证CrazyGames Token
            crazy_payload = await crazygames_service.verify_token(crazygames_token)

            # 生成用户ID（使用 crazygames_ 前缀）
            user_id = crazygames_service.generate_user_id(crazy_payload)

            # 查找现有用户
            stmt = select(User).where(User.user_id == user_id)
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()

            if user:
                # 更新现有用户信息
                user.nickname = crazygames_service.generate_nickname(crazy_payload)
                user.avatar_url = crazy_payload.profilePictureUrl
                user.last_login_time = datetime.utcnow()

                print(f"🔄 更新CrazyGames用户: {user.user_id}")
            else:
                # 创建新用户
                user = User(
                    user_id=user_id,
                    nickname=crazygames_service.generate_nickname(crazy_payload),
                    avatar_url=crazy_payload.profilePictureUrl,
                    login_type="crazygames",
                    device_id=device_id,
                    guardian_level=1,
                    guardian_exp=0,
                    # 统一使用game_config配置，确保与PRD一致
                    gold=game_config.get("currency", {}).get("initial_gold", 200),
                    diamond=game_config.get("currency", {}).get("initial_diamond", 20),
                    ammo_count=game_config.get("currency", {}).get("initial_ammunition", 50),
                    # 初始化PRD要求的体力系统
                    stamina=120,  # PRD要求初始体力120点
                    max_stamina=120,
                    last_stamina_update=datetime.utcnow(),
                    total_play_time=0,
                    last_login_time=datetime.utcnow(),
                    is_active=True,
                    is_deleted=False
                )
                db.add(user)
                await db.flush()  # 获取用户ID

                # 🚫 PRD合规性清理：移除大炮升级记录创建 - 复杂大炮系统不符合PRD要求

                print(f"🆕 创建新CrazyGames用户: {user.user_id}")

            # 处理设备信息
            device_info_str = device_info
            if isinstance(device_info, dict):
                device_info_str = json.dumps(device_info, ensure_ascii=False)

            # 记录登录日志
            login_log = UserLoginLog(
                user_id=user.id,
                user_uuid=user.user_id,
                login_type=LoginType.CRAZYGAMES,  # 使用正确的CrazyGames登录类型
                device_info=device_info_str,
                login_ip=login_ip
            )
            db.add(login_log)
            
            # 更新登录任务进度
            await self._update_task_progress(db, user, "login", 1)

            # 在commit前获取用户信息，避免session过期问题
            user_data = {
                "user_id": user.user_id,
                "nickname": user.nickname,
                "avatar_url": user.avatar_url,
                "level": user.guardian_level,
                "exp": user.guardian_exp,
                "gold": user.gold,
                "diamond": user.diamond,
                "total_play_time": user.total_play_time,
                "created_at": user.created_at
            }

            await db.commit()

            # 生成令牌
            access_token = self._create_token(user_data["user_id"], "access")
            refresh_token = self._create_token(user_data["user_id"], "refresh")

            # 构建用户信息
            user_info = UserInfo(
                user_id=user_data["user_id"],
                nickname=user_data["nickname"],
                avatar_url=user_data["avatar_url"],
                level=user_data["level"],
                exp=user_data["exp"],
                vip_level=0,  # 默认VIP等级
                gold=user_data["gold"],
                diamond=user_data["diamond"],
                ammo_count=user.ammo_count,  # 添加弹药数量
                stamina=user.stamina,        # 添加当前体力
                max_stamina=user.max_stamina, # 添加最大体力
                total_play_time=user_data["total_play_time"],
                created_at=user_data["created_at"]
            )

            return {
                "user_id": user.user_id,
                "token": access_token,
                "refresh_token": refresh_token,
                "user_info": user_info,
                "crazygames_info": {
                    "crazygames_id": crazy_payload.userId,
                    "game_id": crazy_payload.gameId,
                    "username": crazy_payload.username,
                    "profile_picture_url": crazy_payload.profilePictureUrl
                }
            }

        except Exception as e:
            await db.rollback()
            print(f"❌ CrazyGames登录失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"error": f"CrazyGames登录失败: {str(e)}"}

    async def refresh_access_token(self, refresh_token: str) -> Optional[Dict[str, str]]:
        """
        刷新访问令牌
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            包含新令牌的字典，失败时返回None
        """
        payload = self._verify_token(refresh_token)
        
        if not payload or payload.get("type") != "refresh":
            return None
        
        user_id = payload.get("user_id")
        if not user_id:
            return None
        
        # 生成新的令牌
        new_access_token = self._create_token(user_id, "access")
        new_refresh_token = self._create_token(user_id, "refresh")
        
        return {
            "token": new_access_token,
            "refresh_token": new_refresh_token
        }
    
    async def verify_access_token(self, token: str) -> Optional[str]:
        """
        验证访问令牌
        
        Args:
            token: 访问令牌
            
        Returns:
            用户ID，失败时返回None
        """
        payload = self._verify_token(token)
        
        if not payload or payload.get("type") != "access":
            return None
        
        return payload.get("user_id")
    
    async def get_current_user(self, db: AsyncSession, token: str) -> Optional["User"]:
        """
        根据访问令牌获取当前用户
        
        Args:
            db: 数据库会话
            token: 访问令牌
            
        Returns:
            用户对象，失败时返回None
        """
        user_id = await self.verify_access_token(token)
        if not user_id:
            return None
        
        # 从数据库查询用户
        stmt = select(User).where(User.user_id == user_id)
        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    async def _update_task_progress(self, db: AsyncSession, user: User, task_type: str, increment: int = 1):
        """更新任务进度"""
        try:
            # 延迟导入避免循环依赖
            from app.services.task_service import task_service
            
            # 更新任务进度
            result = await task_service.update_task_progress_enhanced(db, user, task_type, increment)
            
            if "error" in result:
                logger.warning(f"更新任务进度失败: {result['error']}")
            else:
                logger.info(f"任务进度更新成功: user={user.id}, type={task_type}, increment={increment}")
                
        except Exception as e:
            logger.error(f"更新任务进度异常: {e}")


# 创建全局实例
auth_service = AuthService() 