"""
经验值缓存服务
统一处理游戏中所有经验值相关的高频操作缓存
"""
import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from app.core.redis_client import redis_manager
from app.core.database import get_db
from sqlalchemy import text, insert
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)


class ExperienceCacheService:
    """经验值缓存服务 - 统一管理所有经验值操作"""
    
    def __init__(self):
        self.experience_queue = asyncio.Queue(maxsize=10000)
        self.batch_size = 100
        self.batch_timeout = 3  # 3秒批量处理
        self.is_processing = False
    
    async def start_batch_processor(self):
        """启动批量处理器"""
        if self.is_processing:
            return
        
        self.is_processing = True
        asyncio.create_task(self._batch_processor())
        logger.info("✅ 经验值缓存批量处理器已启动")
    
    async def stop_batch_processor(self):
        """停止批量处理器"""
        self.is_processing = False
        logger.info("🛑 经验值缓存批量处理器已停止")
    
    # ==================== 热点收集经验缓存 ====================
    
    async def cache_hotspot_experience(
        self, 
        user_id: int, 
        session_id: str, 
        hotspot_id: str, 
        hotspot_type: str, 
        experience_gained: int,
        hotspot_name: str = "",
        city_id: str = "",
        scene_id: str = ""
    ) -> Dict[str, Any]:
        """缓存热点收集经验"""
        try:
            # 检查重复收集
            duplicate_key = f"hotspot:{session_id}:{hotspot_id}"
            is_duplicate = await redis_manager.exists(duplicate_key)
            
            if is_duplicate:
                return {"error": "热点已收集", "code": "ALREADY_COLLECTED"}
            
            # 标记已收集
            await redis_manager.setex(duplicate_key, 7200, "1")  # 2小时过期
            
            # 更新缓存中的经验值
            experience_key = f"user:{user_id}:experience"
            current_exp = await redis_manager.get(experience_key)
            new_exp = (int(current_exp) if current_exp else 0) + experience_gained
            await redis_manager.setex(experience_key, 7200, str(new_exp))
            
            # 更新会话统计
            session_key = f"session:{session_id}:stats"
            await redis_manager.hincrby(session_key, f"{hotspot_type}_collected", 1)
            await redis_manager.hincrby(session_key, "total_experience", experience_gained)
            await redis_manager.expire(session_key, 7200)
            
            # 加入批量处理队列
            experience_record = {
                "type": "hotspot_collection",
                "user_id": user_id,
                "session_id": session_id,
                "hotspot_id": hotspot_id,
                "hotspot_type": hotspot_type,
                "hotspot_name": hotspot_name,
                "city_id": city_id,
                "scene_id": scene_id,
                "experience_gained": experience_gained,
                "timestamp": datetime.now().isoformat()
            }
            
            await self.experience_queue.put(experience_record)
            
            return {
                "success": True,
                "experience_gained": experience_gained,
                "total_experience": new_exp,
                "cached": True
            }
            
        except Exception as e:
            logger.error(f"缓存热点经验失败: {e}")
            return {"error": "缓存失败", "code": "CACHE_ERROR"}
    
    # ==================== 问答经验缓存 ====================
    
    async def cache_quiz_experience(
        self,
        user_id: int,
        session_id: str,
        quiz_id: str,
        quiz_type: str,  # 'monument' 或 'cultural_collection'
        question_count: int,
        correct_count: int,
        experience_gained: int,
        is_double_reward: bool = False
    ) -> Dict[str, Any]:
        """缓存问答经验值"""
        try:
            # 更新缓存中的经验值
            experience_key = f"user:{user_id}:experience"
            current_exp = await redis_manager.get(experience_key)
            new_exp = (int(current_exp) if current_exp else 0) + experience_gained
            await redis_manager.setex(experience_key, 7200, str(new_exp))
            
            # 更新问答统计
            quiz_stats_key = f"user:{user_id}:quiz_stats"
            await redis_manager.hincrby(quiz_stats_key, f"{quiz_type}_answered", 1)
            await redis_manager.hincrby(quiz_stats_key, f"{quiz_type}_correct", correct_count)
            await redis_manager.hincrby(quiz_stats_key, "total_quiz_experience", experience_gained)
            await redis_manager.expire(quiz_stats_key, 7200)
            
            # 加入批量处理队列
            quiz_record = {
                "type": "quiz_completion",
                "user_id": user_id,
                "session_id": session_id,
                "quiz_id": quiz_id,
                "quiz_type": quiz_type,
                "question_count": question_count,
                "correct_count": correct_count,
                "experience_gained": experience_gained,
                "is_double_reward": is_double_reward,
                "timestamp": datetime.now().isoformat()
            }
            
            await self.experience_queue.put(quiz_record)
            
            return {
                "success": True,
                "experience_gained": experience_gained,
                "total_experience": new_exp,
                "accuracy": correct_count / question_count if question_count > 0 else 0,
                "cached": True
            }
            
        except Exception as e:
            logger.error(f"缓存问答经验失败: {e}")
            return {"error": "缓存失败", "code": "CACHE_ERROR"}
    
    # ==================== 任务完成经验缓存 ====================
    
    async def cache_task_experience(
        self,
        user_id: int,
        task_id: str,
        task_type: str,
        experience_gained: int,
        is_double_reward: bool = False
    ) -> Dict[str, Any]:
        """缓存任务完成经验值"""
        try:
            # 检查任务是否已完成
            task_key = f"task:{user_id}:{task_id}:completed"
            is_completed = await redis_manager.exists(task_key)
            
            if is_completed:
                return {"error": "任务已完成", "code": "TASK_COMPLETED"}
            
            # 标记任务完成
            await redis_manager.setex(task_key, 86400, "1")  # 24小时过期
            
            # 更新经验值
            experience_key = f"user:{user_id}:experience"
            current_exp = await redis_manager.get(experience_key)
            new_exp = (int(current_exp) if current_exp else 0) + experience_gained
            await redis_manager.setex(experience_key, 7200, str(new_exp))
            
            # 更新任务统计
            task_stats_key = f"user:{user_id}:task_stats"
            await redis_manager.hincrby(task_stats_key, "tasks_completed", 1)
            await redis_manager.hincrby(task_stats_key, "task_experience", experience_gained)
            await redis_manager.expire(task_stats_key, 86400)
            
            # 加入批量处理队列
            task_record = {
                "type": "task_completion",
                "user_id": user_id,
                "task_id": task_id,
                "task_type": task_type,
                "experience_gained": experience_gained,
                "is_double_reward": is_double_reward,
                "timestamp": datetime.now().isoformat()
            }
            
            await self.experience_queue.put(task_record)
            
            return {
                "success": True,
                "experience_gained": experience_gained,
                "total_experience": new_exp,
                "cached": True
            }
            
        except Exception as e:
            logger.error(f"缓存任务经验失败: {e}")
            return {"error": "缓存失败", "code": "CACHE_ERROR"}
    
    # ==================== 守望者头像被动收益 ====================
    
    async def cache_passive_income(
        self,
        user_id: int,
        guardian_level: int,
        income_rate: int,
        duration_minutes: int
    ) -> Dict[str, Any]:
        """缓存守望者头像被动收益"""
        try:
            # 计算被动收益
            passive_experience = income_rate * duration_minutes
            
            # 更新经验值
            experience_key = f"user:{user_id}:experience"
            current_exp = await redis_manager.get(experience_key)
            new_exp = (int(current_exp) if current_exp else 0) + passive_experience
            await redis_manager.setex(experience_key, 7200, str(new_exp))
            
            # 更新被动收益记录
            passive_key = f"user:{user_id}:passive_income"
            await redis_manager.hincrby(passive_key, "total_passive_exp", passive_experience)
            await redis_manager.hset(passive_key, "last_collected", datetime.now().isoformat())
            await redis_manager.expire(passive_key, 86400)
            
            # 加入批量处理队列
            passive_record = {
                "type": "passive_income",
                "user_id": user_id,
                "guardian_level": guardian_level,
                "income_rate": income_rate,
                "duration_minutes": duration_minutes,
                "experience_gained": passive_experience,
                "timestamp": datetime.now().isoformat()
            }
            
            await self.experience_queue.put(passive_record)
            
            return {
                "success": True,
                "experience_gained": passive_experience,
                "total_experience": new_exp,
                "income_rate": income_rate,
                "cached": True
            }
            
        except Exception as e:
            logger.error(f"缓存被动收益失败: {e}")
            return {"error": "缓存失败", "code": "CACHE_ERROR"}
    
    # ==================== 体力值管理 ====================
    
    async def update_stamina(
        self,
        user_id: int,
        stamina_change: int,
        reason: str = "游戏操作"
    ) -> Dict[str, Any]:
        """更新用户体力值"""
        try:
            stamina_key = f"user:{user_id}:stamina"
            current_stamina = await redis_manager.get(stamina_key)
            current_stamina = int(current_stamina) if current_stamina else 120
            
            new_stamina = max(0, min(120, current_stamina + stamina_change))
            await redis_manager.setex(stamina_key, 7200, str(new_stamina))
            
            # 记录体力变化
            stamina_log = {
                "type": "stamina_change",
                "user_id": user_id,
                "stamina_change": stamina_change,
                "current_stamina": new_stamina,
                "reason": reason,
                "timestamp": datetime.now().isoformat()
            }
            
            await self.experience_queue.put(stamina_log)
            
            return {
                "success": True,
                "current_stamina": new_stamina,
                "stamina_change": stamina_change,
                "is_low_stamina": new_stamina < 20
            }
            
        except Exception as e:
            logger.error(f"更新体力值失败: {e}")
            return {"error": "更新失败", "code": "STAMINA_ERROR"}
    
    # ==================== 批量处理器 ====================
    
    async def _batch_processor(self):
        """批量处理经验记录"""
        batch = []
        
        while self.is_processing:
            try:
                # 收集批量记录
                timeout_task = asyncio.create_task(asyncio.sleep(self.batch_timeout))
                
                while len(batch) < self.batch_size:
                    try:
                        record = await asyncio.wait_for(
                            self.experience_queue.get(), 
                            timeout=0.1
                        )
                        batch.append(record)
                    except asyncio.TimeoutError:
                        break
                
                timeout_task.cancel()
                
                # 处理批量记录
                if batch:
                    await self._batch_process_records(batch)
                    logger.info(f"📊 批量处理经验记录: {len(batch)} 条")
                    batch.clear()
                
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"批量处理器错误: {e}")
                await asyncio.sleep(1)
    
    async def _batch_process_records(self, records: List[Dict[str, Any]]):
        """批量处理记录到数据库"""
        if not records:
            return
        
        try:
            async for db in get_db():
                # 按类型分组处理
                hotspot_records = [r for r in records if r["type"] == "hotspot_collection"]
                quiz_records = [r for r in records if r["type"] == "quiz_completion"]
                task_records = [r for r in records if r["type"] == "task_completion"]
                passive_records = [r for r in records if r["type"] == "passive_income"]
                stamina_records = [r for r in records if r["type"] == "stamina_change"]
                
                # 批量插入热点收集记录
                if hotspot_records:
                    await self._insert_hotspot_records(db, hotspot_records)
                
                # 批量插入问答记录
                if quiz_records:
                    await self._insert_quiz_records(db, quiz_records)
                
                # 批量插入任务记录
                if task_records:
                    await self._insert_task_records(db, task_records)
                
                # 批量插入被动收益记录
                if passive_records:
                    await self._insert_passive_records(db, passive_records)
                
                # 批量插入体力记录
                if stamina_records:
                    await self._insert_stamina_records(db, stamina_records)
                
                await db.commit()
                break
                
        except Exception as e:
            logger.error(f"批量处理记录失败: {e}")
    
    async def _insert_hotspot_records(self, db: AsyncSession, records: List[Dict]):
        """插入热点收集记录"""
        if not records:
            return
        
        try:
            values = []
            for record in records:
                values.append({
                    "user_id": record["user_id"],
                    "city_id": record.get("city_id", ""),
                    "scene_id": record.get("scene_id", ""),
                    "hotspot_name": record.get("hotspot_name", ""),
                    "hotspot_type": record["hotspot_type"],
                    "collected_at": datetime.fromisoformat(record["timestamp"])
                })
            
            # 使用批量插入
            stmt = insert(text("user_hotspot_collections")).values(values)
            await db.execute(stmt)
            
        except Exception as e:
            logger.error(f"插入热点记录失败: {e}")
    
    async def _insert_quiz_records(self, db: AsyncSession, records: List[Dict]):
        """插入问答记录"""
        # 创建问答记录表的插入逻辑
        pass
    
    async def _insert_task_records(self, db: AsyncSession, records: List[Dict]):
        """插入任务记录"""
        # 创建任务记录表的插入逻辑
        pass
    
    async def _insert_passive_records(self, db: AsyncSession, records: List[Dict]):
        """插入被动收益记录"""
        # 创建被动收益记录表的插入逻辑
        pass
    
    async def _insert_stamina_records(self, db: AsyncSession, records: List[Dict]):
        """插入体力记录"""
        # 创建体力记录表的插入逻辑
        pass
    
    # ==================== 同步方法 ====================
    
    async def sync_user_experience_to_database(self, user_id: int):
        """同步用户经验值到数据库"""
        try:
            experience_key = f"user:{user_id}:experience"
            cached_exp = await redis_manager.get(experience_key)
            
            if not cached_exp:
                return
            
            async for db in get_db():
                # 更新用户经验值
                await db.execute(text("""
                    UPDATE users 
                    SET civilization_exp = :exp, updated_at = NOW() 
                    WHERE id = :user_id
                """), {"exp": int(cached_exp), "user_id": user_id})
                
                await db.commit()
                logger.info(f"✅ 同步用户 {user_id} 经验值: {cached_exp}")
                break
                
        except Exception as e:
            logger.error(f"同步用户经验值失败: {e}")
    
    async def get_user_cache_stats(self, user_id: int) -> Dict[str, Any]:
        """获取用户缓存统计"""
        try:
            stats = {}
            
            # 获取经验值
            experience_key = f"user:{user_id}:experience"
            stats["experience"] = int(await redis_manager.get(experience_key) or 0)
            
            # 获取体力值
            stamina_key = f"user:{user_id}:stamina"
            stats["stamina"] = int(await redis_manager.get(stamina_key) or 120)
            
            # 获取问答统计
            quiz_stats = await redis_manager.hgetall(f"user:{user_id}:quiz_stats")
            stats["quiz_stats"] = {k.decode(): int(v) for k, v in quiz_stats.items()} if quiz_stats else {}
            
            # 获取任务统计
            task_stats = await redis_manager.hgetall(f"user:{user_id}:task_stats")
            stats["task_stats"] = {k.decode(): int(v) for k, v in task_stats.items()} if task_stats else {}
            
            return stats
            
        except Exception as e:
            logger.error(f"获取用户缓存统计失败: {e}")
            return {}


# 全局实例
experience_cache_service = ExperienceCacheService() 