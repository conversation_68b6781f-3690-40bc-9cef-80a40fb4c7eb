"""
收藏系统服务
"""
import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from datetime import datetime, timedelta

from app.models.user import User
from app.models.artifact import UserArtifact, UserCityProgress, CityCollection, UserCollection
from app.models.game import GameSession, SessionStatus
# 🚫 PRD合规性清理：移除AmmoUsageType导入 - PRD中没有弹药概念
from app.core.config import cities_config, game_config
# ammo_service不再需要，新系统基于材料数量直接计算弹药

logger = logging.getLogger(__name__)


class CollectionService:
    """收藏系统服务"""
    
    def __init__(self):
        self.max_ammo = 100
        self.ammo_refill_interval = 300  # 5分钟
        self.radar_cooldown = 60  # 1分钟
    
    async def get_city_collections(
        self, 
        db: AsyncSession, 
        user: User, 
        city_id: str
    ) -> Dict[str, Any]:
        """
        获取城市收藏品列表
        
        Args:
            db: 数据库会话
            user: 当前用户
            city_id: 城市ID
            
        Returns:
            城市收藏品信息
        """
        try:
            # 查询城市收藏品定义
            city_items_query = await db.execute(
                select(CityCollection)
                .where(CityCollection.city_id == city_id)
                .order_by(CityCollection.order_index)
            )
            city_items = city_items_query.scalars().all()
            
            # 查询用户已收集的收藏品
            user_collections_query = await db.execute(
                select(UserCollection)
                .where(and_(
                    UserCollection.user_id == user.id,
                    UserCollection.city_id == city_id
                ))
            )
            user_collections = {
                uc.item_id: uc for uc in user_collections_query.scalars().all()
            }
            
            # 构建响应数据
            items = []
            for item in city_items:
                user_collection = user_collections.get(item.item_id)
                items.append({
                    "item_id": item.item_id,
                    "name": item.name,
                    "image_url": item.image_url,
                    "rarity": item.rarity,
                    "description": item.description,
                    "owned": user_collection is not None,
                    "collected_at": user_collection.collected_at.isoformat() if user_collection else None
                })
            
            # 获取城市名称
            city_name = self._get_city_name(city_id)
            
            return {
                "city_id": city_id,
                "city_name": city_name,
                "total_items": len(city_items),
                "collected_count": len(user_collections),
                "completion_rate": len(user_collections) / len(city_items) * 100 if city_items else 0,
                "items": items
            }
            
        except Exception as e:
            logger.error(f"获取城市收藏品失败: {e}")
            return {"error": f"获取城市收藏品失败: {str(e)}"}
    
    async def get_user_artifacts(
        self, 
        db: AsyncSession, 
        user: User, 
        city_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取用户文物列表
        
        Args:
            db: 数据库会话
            user: 当前用户
            city_id: 城市ID（可选）
            
        Returns:
            用户文物信息
        """
        try:
            # 构建查询条件
            query = select(UserArtifact).where(UserArtifact.user_id == user.id)
            if city_id:
                query = query.where(UserArtifact.city_id == city_id)
            
            # 查询用户文物
            user_artifacts_query = await db.execute(query)
            user_artifacts = user_artifacts_query.scalars().all()
            
            # 获取文物配置
            artifacts_config = self._get_artifacts_config(city_id)
            
            # 构建响应数据
            artifacts = []
            user_artifact_dict = {ua.artifact_id: ua for ua in user_artifacts}
            
            for artifact_config in artifacts_config:
                artifact_id = artifact_config.get("artifact_id")
                user_artifact = user_artifact_dict.get(artifact_id)
                
                artifacts.append({
                    "artifact_id": artifact_id,
                    "name": artifact_config.get("name", ""),
                    "rarity": artifact_config.get("rarity", "common"),
                    "owned": user_artifact is not None,
                    "count": user_artifact.count if user_artifact else 0,
                    "first_obtained_at": user_artifact.first_obtained_at.isoformat() if user_artifact else None
                })
            
            # 获取城市名称
            city_name = self._get_city_name(city_id) if city_id else "全部"
            
            return {
                "city_id": city_id or "all",
                "city_name": city_name,
                "total_artifacts": len(artifacts_config),
                "collected_count": len(user_artifacts),
                "completion_rate": len(user_artifacts) / len(artifacts_config) * 100 if artifacts_config else 0,
                "artifacts": artifacts
            }
            
        except Exception as e:
            logger.error(f"获取用户文物失败: {e}")
            return {"error": f"获取用户文物失败: {str(e)}"}
    
    async def get_artifact_detail(
        self, 
        db: AsyncSession, 
        user: User, 
        artifact_id: str
    ) -> Dict[str, Any]:
        """
        获取文物详情
        
        Args:
            db: 数据库会话
            user: 当前用户
            artifact_id: 文物ID
            
        Returns:
            文物详情信息
        """
        try:
            # 查询用户文物
            user_artifact_query = await db.execute(
                select(UserArtifact)
                .where(and_(
                    UserArtifact.user_id == user.id,
                    UserArtifact.artifact_id == artifact_id
                ))
            )
            user_artifact = user_artifact_query.scalar_one_or_none()
            
            # 获取文物配置
            artifact_config = self._get_artifact_config(artifact_id)
            if not artifact_config:
                return {"error": "文物不存在"}
            
            return {
                "artifact_id": artifact_id,
                "name": artifact_config.get("name", ""),
                "description": artifact_config.get("description", ""),
                "rarity": artifact_config.get("rarity", "common"),
                "image_url": artifact_config.get("image_url", ""),
                "story": artifact_config.get("story", ""),
                "owned": user_artifact is not None,
                "count": user_artifact.count if user_artifact else 0,
                "first_obtained_at": user_artifact.first_obtained_at.isoformat() if user_artifact else None
            }
            
        except Exception as e:
            logger.error(f"获取文物详情失败: {e}")
            return {"error": f"获取文物详情失败: {str(e)}"}
    
    async def get_user_resources(self, db: AsyncSession, user: User) -> Dict[str, Any]:
        """
        获取用户资源信息 - 新弹药系统：材料数量即弹药数量
        """
        # 材料数量
        thieves_collected = user.thieves_collected or 0
        garbage_collected = user.garbage_collected or 0
        total_materials = thieves_collected + garbage_collected
        
        # 弹药数量等于材料总数
        current_ammo = total_materials
        
        # 计算消耗的弹药（用于统计）
        total_ammo_used = user.total_ammo_used or 0
        boss_usage = user.ammo_used_on_boss or 0
        radar_usage = user.ammo_used_on_radar or 0
        
        # 构建符合UserResourcesResponse模型的响应
        return {
            "user_id": user.user_id,
            "gold": user.gold or 0,
            "diamond": user.diamond or 0,
            "last_ammo_refill": user.last_ammo_refill.isoformat() if user.last_ammo_refill else None,
            "current_status": {
                "current_ammo": current_ammo,
                "max_ammo": 999,  # 新系统无上限，设一个大值
                "ammo_percentage": min(100.0, current_ammo / 999 * 100),
                "synthesis_progress": 0,  # 新系统无合成进度概念
                "materials_needed": 0  # 新系统无需等待合成
            },
            "lifetime_statistics": {
                "total_synthesized": total_materials,  # 总收集材料数
                "total_used": total_ammo_used,
                "net_ammo": total_materials - total_ammo_used,
                "efficiency_percentage": (total_ammo_used / total_materials * 100) if total_materials > 0 else 0
            },
            "usage_breakdown": {
                "boss_battles": boss_usage,
                "radar_scans": radar_usage,
                "other_usage": total_ammo_used - boss_usage - radar_usage
            },
            "materials": {
                "thieves_collected": thieves_collected,
                "garbage_collected": garbage_collected,
                "total_materials": total_materials,
                "synthesis_ratio": 1  # 新系统1:1比例
            },
            "recent_activity": {
                "recent_usage": [],  # 简化版本，暂不实现详细记录
                "recent_synthesis": []  # 新系统无合成记录
            }
        }
    
    def _get_city_name(self, city_id: str) -> str:
        """获取城市名称"""
        cities = cities_config.get("cities", [])
        for city in cities:
            if city.get("city_id") == city_id:
                return city.get("name", city_id)
        return city_id
    
    def _get_artifacts_config(self, city_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取文物配置"""
        if not city_id:
            # 返回所有城市的文物
            all_artifacts = []
            cities = cities_config.get("cities", [])
            for city in cities:
                artifacts = city.get("artifacts", [])
                all_artifacts.extend(artifacts)
            return all_artifacts
        
        # 返回指定城市的文物
        cities = cities_config.get("cities", [])
        for city in cities:
            if city.get("city_id") == city_id:
                return city.get("artifacts", [])
        return []
    
    def _get_artifact_config(self, artifact_id: str) -> Optional[Dict[str, Any]]:
        """获取单个文物配置"""
        cities = cities_config.get("cities", [])
        for city in cities:
            artifacts = city.get("artifacts", [])
            for artifact in artifacts:
                if artifact.get("artifact_id") == artifact_id:
                    return artifact
        return None


    async def reload_ammo(
        self,
        db: AsyncSession,
        user: User,
        reload_type: str = "time"
    ) -> Dict[str, Any]:
        """
        重装弹药

        Args:
            db: 数据库会话
            user: 当前用户
            reload_type: 重装类型 (time/diamond)

        Returns:
            重装结果
        """
        try:
            current_time = datetime.utcnow()
            cost = {}

            if reload_type == "time":
                # 检查时间冷却
                if user.last_ammo_refill:
                    time_diff = current_time - user.last_ammo_refill
                    if time_diff.total_seconds() < self.ammo_refill_interval:
                        remaining = self.ammo_refill_interval - time_diff.total_seconds()
                        return {"error": f"弹药补充冷却中，剩余 {int(remaining)} 秒"}

            elif reload_type == "diamond":
                # 使用钻石立即补充
                diamond_cost = 1
                if user.diamond < diamond_cost:
                    return {"error": "钻石不足"}

                user.diamond -= diamond_cost
                cost["diamond"] = diamond_cost

            else:
                return {"error": "无效的重装类型"}

            # 补充弹药
            user.ammo_count = self.max_ammo
            user.last_ammo_refill = current_time

            await db.commit()

            # 计算下次免费重装时间
            next_free_reload = current_time + timedelta(seconds=self.ammo_refill_interval)

            return {
                "success": True,
                "new_ammo_count": user.ammo_count,
                "cost": cost,
                "next_free_reload": next_free_reload.isoformat()
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"弹药重装失败: {e}")
            return {"error": f"弹药重装失败: {str(e)}"}

    async def radar_scan(
        self,
        db: AsyncSession,
        user: User,
        city_id: str,
        scene_id: str,
        scan_range: float = 50.0
    ) -> Dict[str, Any]:
        """
        雷达扫描

        Args:
            db: 数据库会话
            user: 当前用户
            city_id: 城市ID
            scene_id: 场景ID
            scan_range: 扫描范围

        Returns:
            扫描结果
        """
        try:
            # 使用弹药服务消耗弹药
            # 🚫 PRD合规性清理：移除弹药消耗逻辑 - PRD中没有弹药概念
            # PRD要求：通过收集行为直接对BOSS造成伤害，无需弹药消耗

            # 模拟雷达扫描结果
            # 这里应该根据实际的热点数据生成
            hotspots_found = [
                {
                    "hotspot_id": f"hotspot_{i}",
                    "name": f"热点 {i}",
                    "type": "thief" if i % 2 == 0 else "garbage",
                    "distance": 20.0 + i * 5,
                    "direction": i * 45,
                    "reward_preview": {
                        "type": "gold",
                        "amount": 50 + i * 10
                    }
                }
                for i in range(1, 4)  # 返回3个热点
            ]

            return {
                "success": True,
                "scan_range": scan_range,
                "hotspots_found": hotspots_found,
                "scan_cost": {"ammo": ammo_cost},
                "remaining_ammo": user.ammo_count,
                "cooldown_remaining": self.radar_cooldown
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"雷达扫描失败: {e}")
            return {"error": f"雷达扫描失败: {str(e)}"}

    async def _get_current_ammo_count(self, db: AsyncSession, user_id: int) -> int:
        """获取用户当前实时合成弹药数量"""
        # 直接从用户表获取弹药数量（已迁移到用户表）
        result = await db.execute(
            select(User).where(User.id == user_id)
        )

        user = result.scalars().first()
        if user:
            return user.ammo_count or 0
        else:
            return 0


# 创建全局服务实例
collection_service = CollectionService()
