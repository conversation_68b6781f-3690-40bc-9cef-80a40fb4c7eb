"""
定时任务服务
"""
import logging
import asyncio
from datetime import datetime, timedelta
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
# 🚫 PRD合规性清理：移除lottery_service导入 - 转盘抽奖系统不在PRD要求中
from app.services.daily_task_service import daily_task_service

logger = logging.getLogger(__name__)


class SchedulerService:
    """定时任务服务"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        
    async def start(self):
        """启动定时任务服务"""
        if self.is_running:
            logger.warning("定时任务服务已经在运行")
            return
            
        # 🚫 PRD合规性清理：移除转盘抽奖定时任务 - 转盘抽奖系统不在PRD要求中
        
        # 添加每日0点重置每日任务的任务
        self.scheduler.add_job(
            func=self.reset_daily_tasks,
            trigger=CronTrigger(hour=0, minute=1, second=0),  # 每天0点1分执行（避免与转盘重置冲突）
            id='reset_daily_tasks',
            name='重置每日任务',
            replace_existing=True,
            max_instances=1
        )
        
        # 🚫 PRD合规性清理：移除转盘数据清理任务 - 转盘抽奖系统不在PRD要求中
        
        # 启动调度器
        self.scheduler.start()
        self.is_running = True
        logger.info("定时任务服务启动成功")
        
    async def stop(self):
        """停止定时任务服务"""
        if not self.is_running:
            return
            
        self.scheduler.shutdown(wait=True)
        self.is_running = False
        logger.info("定时任务服务已停止")
        
    # 🚫 PRD合规性清理：移除转盘抽奖重置方法 - 转盘抽奖系统不在PRD要求中

    async def reset_daily_tasks(self):
        """重置每日任务（每天0点1分执行）"""
        try:
            logger.info("开始重置每日任务...")
            
            async for db in get_db():
                result = await daily_task_service.reset_all_daily_tasks(db)
                if result.get("success"):
                    logger.info(f"每日任务重置成功: 处理了 {result.get('processed_users', 0)} 个用户")
                else:
                    logger.error(f"重置每日任务失败: {result.get('error', '未知错误')}")
                break
                
        except Exception as e:
            logger.error(f"执行每日任务重置时发生错误: {e}")
            
    # 🚫 PRD合规性清理：移除转盘数据清理方法 - 转盘抽奖系统不在PRD要求中

    def get_job_status(self):
        """获取定时任务状态"""
        if not self.is_running:
            return {"status": "stopped", "jobs": []}
            
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
            
        return {
            "status": "running",
            "jobs": jobs
        }


# 创建全局实例
scheduler_service = SchedulerService() 