"""
广告系统服务
"""
import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from datetime import datetime, timedelta

from app.models.user import User
from app.models.advertisement import AdWatchRecord, UserAdLimit
from app.models.daily_task import DailyTaskProgress  # 正确的导入位置
# 🚫 PRD合规性清理：LoginReward在schemas中，不在models中
from app.core.config import game_config

logger = logging.getLogger(__name__)


class AdvertisementService:
    """广告系统服务"""
    
    def __init__(self):
        # 广告配置
        self.ad_limits = {
            "task_reward": 10,      # 任务奖励广告每日限制
            "ammo_refill": 5,       # 弹药补充广告每日限制
            "daily_bonus": 3,       # 每日奖励广告每日限制
            "login_reward": 1       # 登录奖励广告每日限制
        }
        
        # 奖励倍数配置
        self.reward_multipliers = {
            "task_reward": 2,       # 任务奖励双倍
            "login_reward": 2,      # 登录奖励双倍
            "daily_bonus": 2        # 每日奖励双倍
        }
    
    async def watch_ad(
        self, 
        db: AsyncSession, 
        user: User, 
        ad_type: str,
        task_id: Optional[str] = None,
        watch_duration: int = 30,
        ad_provider: str = "default"
    ) -> Dict[str, Any]:
        """
        观看广告
        
        Args:
            db: 数据库会话
            user: 当前用户
            ad_type: 广告类型
            task_id: 任务ID（可选）
            watch_duration: 观看时长
            ad_provider: 广告提供商
            
        Returns:
            观看结果
        """
        try:
            current_date = datetime.utcnow().strftime("%Y-%m-%d")
            
            # 检查每日观看限制
            limit_check = await self._check_daily_limit(db, user.id, ad_type, current_date)
            if not limit_check["can_watch"]:
                return {"error": f"今日{ad_type}广告观看次数已达上限"}
            
            # 创建广告观看记录
            ad_record = AdWatchRecord(
                user_id=user.id,
                ad_type=ad_type,
                ad_provider=ad_provider,
                watch_duration=watch_duration,
                is_rewarded=False
            )
            
            db.add(ad_record)
            await db.flush()  # 获取ID
            
            # 更新每日限制记录
            await self._update_daily_limit(db, user.id, ad_type, current_date)
            
            # 更新广告观看任务进度
            await self._update_task_progress(db, user, "ad", 1)
            
            await db.commit()
            
            return {
                "success": True,
                "ad_id": ad_record.id,
                "message": "广告观看记录已保存",
                "remaining_today": limit_check["remaining"] - 1
            }
            
        except Exception as e:
            await db.rollback()
            logger.error(f"观看广告失败: {e}")
            return {"error": f"观看广告失败: {str(e)}"}
    
    async def apply_double_reward(
        self, 
        db: AsyncSession, 
        user: User, 
        reward_type: str,
        task_id: Optional[str] = None,
        original_amount: int = 0
    ) -> Dict[str, Any]:
        """
        应用双倍奖励
        
        Args:
            db: 数据库会话
            user: 当前用户
            reward_type: 奖励类型
            task_id: 任务ID
            original_amount: 原始奖励数量
            
        Returns:
            双倍奖励结果
        """
        try:
            current_date = datetime.utcnow().strftime("%Y-%m-%d")
            
            # 验证是否观看了相应的广告
            ad_type = f"{reward_type}_reward"
            if not await self._verify_ad_watched(db, user.id, ad_type, current_date):
                return {"error": "请先观看广告"}
            
            # 计算双倍奖励
            multiplier = self.reward_multipliers.get(reward_type, 2)
            bonus_amount = original_amount * (multiplier - 1)
            total_amount = original_amount + bonus_amount
            
            # 发放奖励
            reward_result = await self._grant_reward(db, user, "gold", bonus_amount)
            if not reward_result["success"]:
                return reward_result
            
            # 记录奖励发放
            await self._record_reward_granted(db, user.id, ad_type, "gold", bonus_amount, multiplier)
            
            await db.commit()
            
            return {
                "success": True,
                "original_amount": original_amount,
                "bonus_amount": bonus_amount,
                "total_amount": total_amount,
                "reward_type": "gold"
            }
            
        except Exception as e:
            await db.rollback()
            logger.error(f"应用双倍奖励失败: {e}")
            return {"error": f"应用双倍奖励失败: {str(e)}"}
    
    async def get_ad_status(
        self, 
        db: AsyncSession, 
        user: User
    ) -> Dict[str, Any]:
        """
        获取用户广告状态
        
        Args:
            db: 数据库会话
            user: 当前用户
            
        Returns:
            广告状态信息
        """
        try:
            current_date = datetime.utcnow().strftime("%Y-%m-%d")
            
            # 查询各类型广告的观看状态
            ad_limits_query = await db.execute(
                select(UserAdLimit)
                .where(and_(
                    UserAdLimit.user_id == user.id,
                    UserAdLimit.date == current_date
                ))
            )
            user_limits = {limit.ad_type: limit for limit in ad_limits_query.scalars().all()}
            
            # 构建状态响应
            ad_status = []
            total_watched = 0
            
            for ad_type, max_count in self.ad_limits.items():
                user_limit = user_limits.get(ad_type)
                watched_count = user_limit.watch_count if user_limit else 0
                remaining = max(0, max_count - watched_count)
                
                ad_status.append({
                    "ad_type": ad_type,
                    "daily_limit": max_count,
                    "watched_today": watched_count,
                    "remaining_today": remaining,
                    "last_watch_time": user_limit.last_watch_time.isoformat() if user_limit and user_limit.last_watch_time else None,
                    "can_watch": remaining > 0
                })
                
                total_watched += watched_count
            
            return {
                "user_id": user.user_id,
                "date": current_date,
                "ad_limits": ad_status,
                "total_watched": total_watched
            }
            
        except Exception as e:
            logger.error(f"获取广告状态失败: {e}")
            return {"error": f"获取广告状态失败: {str(e)}"}
    
    async def _check_daily_limit(
        self, 
        db: AsyncSession, 
        user_id: int, 
        ad_type: str, 
        date: str
    ) -> Dict[str, Any]:
        """检查每日观看限制"""
        max_count = self.ad_limits.get(ad_type, 5)
        
        # 查询今日观看记录
        limit_query = await db.execute(
            select(UserAdLimit)
            .where(and_(
                UserAdLimit.user_id == user_id,
                UserAdLimit.ad_type == ad_type,
                UserAdLimit.date == date
            ))
        )
        user_limit = limit_query.scalar_one_or_none()
        
        if not user_limit:
            return {"can_watch": True, "remaining": max_count}
        
        remaining = max(0, max_count - user_limit.watch_count)
        return {"can_watch": remaining > 0, "remaining": remaining}
    
    async def _update_daily_limit(
        self, 
        db: AsyncSession, 
        user_id: int, 
        ad_type: str, 
        date: str
    ):
        """更新每日观看限制"""
        # 查询或创建限制记录
        limit_query = await db.execute(
            select(UserAdLimit)
            .where(and_(
                UserAdLimit.user_id == user_id,
                UserAdLimit.ad_type == ad_type,
                UserAdLimit.date == date
            ))
        )
        user_limit = limit_query.scalar_one_or_none()
        
        if user_limit:
            user_limit.watch_count += 1
            user_limit.last_watch_time = datetime.utcnow()
        else:
            user_limit = UserAdLimit(
                user_id=user_id,
                ad_type=ad_type,
                date=date,
                watch_count=1,
                max_count=self.ad_limits.get(ad_type, 5),
                last_watch_time=datetime.utcnow()
            )
            db.add(user_limit)
    
    async def _verify_ad_watched(
        self, 
        db: AsyncSession, 
        user_id: int, 
        ad_type: str, 
        date: str
    ) -> bool:
        """验证是否观看了广告"""
        today_start = datetime.strptime(date, "%Y-%m-%d")
        today_end = today_start + timedelta(days=1)
        
        ad_query = await db.execute(
            select(AdWatchRecord)
            .where(and_(
                AdWatchRecord.user_id == user_id,
                AdWatchRecord.ad_type == ad_type,
                AdWatchRecord.created_at >= today_start,
                AdWatchRecord.created_at < today_end,
                AdWatchRecord.is_rewarded == False
            ))
            .limit(1)
        )
        
        return ad_query.scalar_one_or_none() is not None
    
    async def _grant_reward(
        self, 
        db: AsyncSession, 
        user: User, 
        reward_type: str, 
        amount: int
    ) -> Dict[str, Any]:
        """发放奖励"""
        try:
            if reward_type == "gold":
                user.gold += amount
            elif reward_type == "diamond":
                user.diamond += amount
            else:
                return {"success": False, "error": f"不支持的奖励类型: {reward_type}"}
            
            return {"success": True}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _record_reward_granted(
        self, 
        db: AsyncSession, 
        user_id: int, 
        ad_type: str, 
        reward_type: str, 
        amount: int, 
        multiplier: int
    ):
        """记录奖励发放"""
        # 更新广告记录为已奖励
        current_date = datetime.utcnow().strftime("%Y-%m-%d")
        today_start = datetime.strptime(current_date, "%Y-%m-%d")
        today_end = today_start + timedelta(days=1)
        
        # 找到最近的未奖励广告记录并标记为已奖励
        ad_query = await db.execute(
            select(AdWatchRecord)
            .where(and_(
                AdWatchRecord.user_id == user_id,
                AdWatchRecord.ad_type == ad_type,
                AdWatchRecord.created_at >= today_start,
                AdWatchRecord.created_at < today_end,
                AdWatchRecord.is_rewarded == False
            ))
            .order_by(AdWatchRecord.created_at.desc())
            .limit(1)
        )
        
        ad_record = ad_query.scalar_one_or_none()
        if ad_record:
            ad_record.reward_type = reward_type
            ad_record.reward_amount = amount
            ad_record.reward_multiplier = multiplier
            ad_record.is_rewarded = True
            ad_record.rewarded_at = datetime.utcnow()

    async def _update_task_progress(self, db: AsyncSession, user: User, task_type: str, increment: int = 1):
        """更新任务进度"""
        try:
            # 延迟导入避免循环依赖
            from app.services.task_service import task_service
            
            # 更新任务进度
            result = await task_service.update_task_progress_enhanced(db, user, task_type, increment)
            
            if "error" in result:
                logger.warning(f"更新任务进度失败: {result['error']}")
            else:
                logger.info(f"任务进度更新成功: user={user.id}, type={task_type}, increment={increment}")
                
        except Exception as e:
            logger.error(f"更新任务进度异常: {e}")


# 创建全局服务实例
advertisement_service = AdvertisementService()
