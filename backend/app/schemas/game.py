"""
游戏相关的请求和响应模型
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from enum import Enum


class SessionStartRequest(BaseModel):
    """开始游戏会话请求"""
    city_id: str = Field(..., description="城市ID")
    scene_id: str = Field(..., description="场景ID")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "city_id": "beijing",
                "scene_id": "scene_beijing"
            }
        }
    }


class HotspotPosition(BaseModel):
    """热点位置"""
    x: float = Field(..., ge=-180, le=180, description="X坐标(经度: -180到180)")
    y: float = Field(..., ge=-90, le=90, description="Y坐标(纬度: -90到90)")


class HotspotReward(BaseModel):
    """热点奖励"""
    type: str = Field(..., description="奖励类型: gold, diamond, artifact")
    amount: int = Field(..., ge=0, description="奖励数量")


class HotspotTemplate(BaseModel):
    """热点模板"""
    name: str = Field(..., description="热点名称")
    type: str = Field(..., description="热点类型: thief, garbage, treasure, boss_thief")
    position: HotspotPosition
    reward: HotspotReward


class HotspotImportRequest(BaseModel):
    """热点导入请求"""
    city_id: str = Field(..., description="城市ID")
    scene_id: str = Field(..., description="场景ID")
    generation_mode: str = Field(..., description="生成模式: preset, mixed, random")
    hotspots: List[HotspotTemplate] = Field(default=[], description="热点列表")
    preset_count: Optional[int] = Field(None, description="预设热点数量（混合模式）")
    random_count: Optional[int] = Field(None, description="随机热点数量（混合模式）")


class HotspotExportResponse(BaseModel):
    """热点导出响应"""
    city_id: str
    scene_id: str
    template: Dict[str, Any]


class HotspotTemplateListResponse(BaseModel):
    """热点模板列表响应"""
    templates: List[Dict[str, Any]] = Field(..., description="模板列表")


class RewardPreview(BaseModel):
    """奖励预览"""
    type: str = Field(..., description="奖励类型: gold, diamond, artifact")
    amount: int = Field(..., ge=0, description="奖励数量")


class HotspotInfo(BaseModel):
    """热点信息"""
    id: str
    name: str
    type: str = Field(..., description="热点类型: thief, garbage, treasure, boss_thief")
    position: HotspotPosition
    reward_preview: RewardPreview
    # 管理后台配置的额外字段
    scale: Optional[float] = Field(default=1.0, description="缩放比例")
    image_url: Optional[str] = Field(default="", description="热点图片URL")
    visible: Optional[bool] = Field(default=True, description="是否可见")
    onclick_action: Optional[str] = Field(default="", description="点击事件")
    description: Optional[str] = Field(default="", description="热点描述")
    source: Optional[str] = Field(default="generated", description="热点来源: managed, generated")


class SessionStartResponse(BaseModel):
    """开始游戏会话响应 - PRD合规版本"""
    session_id: str
    thieves_collected: int = Field(default=0, description="收集的小偷数量")
    garbage_collected: int = Field(default=0, description="收集的垃圾数量")
    # 🚫 PRD合规性清理：移除弹药相关字段，PRD中没有弹药概念


class CollectHotspotRequest(BaseModel):
    """收集热点请求"""
    hotspot_id: str = Field(..., description="热点ID")


class CollectProgress(BaseModel):
    """收集进度 - PRD合规版本"""
    thieves_collected: int
    garbage_collected: int
    # 🚫 PRD合规性清理：移除弹药相关字段，PRD中没有弹药概念


class CollectHotspotResponse(BaseModel):
    """收集热点响应"""
    success: bool
    reward: Dict[str, Any]
    progress: CollectProgress


# 🚫 PRD合规性清理：移除BOSS攻击相关schemas
# PRD要求使用BOSS血量系统，不需要主动攻击BOSS
# BOSS血量通过收集行为自动减少，相关schemas在boss_health相关文件中定义

class ArtifactReward(BaseModel):
    """文物奖励"""
    artifact_id: str
    name: str
    rarity: str
    is_new: bool
    error: Optional[str] = Field(None, description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    required: Optional[int] = Field(None, description="需要的数量")
    available: Optional[int] = Field(None, description="可用的数量")
    message: Optional[str] = Field(None, description="消息")
    rewards: Optional[Dict[str, Any]] = Field(None, description="奖励信息")
    # 🚫 PRD合规性清理：移除弹药和BOSS攻击相关字段，PRD中没有这些概念


class SessionSummary(BaseModel):
    """会话总结"""
    duration: int = Field(..., description="持续时间(秒)")
    thieves_collected: int
    garbage_collected: int
    gold_earned: int
    diamond_earned: int
    exp_earned: int
    artifacts_found: List[str]


class LevelUpInfo(BaseModel):
    """升级信息"""
    new_level: int
    rewards: Dict[str, int]


class EndSessionResponse(BaseModel):
    """结束会话响应"""
    summary: SessionSummary
    level_up: Optional[LevelUpInfo]


# 🚫 PRD合规性清理：移除所有弹药相关schemas
# PRD中没有弹药概念，只有简单的收集机制（抓捕小偷、清理垃圾、保护遗迹）
# 游戏机制基于收集行为自动影响BOSS血量，不需要弹药系统


class HotspotSyncRequest(BaseModel):
    """热点同步请求"""
    city_id: str = Field(..., description="城市ID")
    scene_id: str = Field(..., description="场景ID")
    sync_mode: str = Field(default="xml", description="同步模式: xml, database")
    force_update: bool = Field(default=False, description="是否强制更新")
    
    
class HotspotSyncResponse(BaseModel):
    """热点同步响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="同步结果消息")
    hotspots_count: int = Field(default=0, description="同步的热点数量")
    updated_count: int = Field(default=0, description="更新的热点数量")
    errors: List[str] = Field(default=[], description="错误列表") 