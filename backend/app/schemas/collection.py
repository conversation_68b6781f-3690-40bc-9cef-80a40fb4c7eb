"""
收藏系统相关的请求和响应模型
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class CollectionItemResponse(BaseModel):
    """收藏品项目响应"""
    item_id: str = Field(..., description="收藏品ID")
    name: str = Field(..., description="收藏品名称")
    image_url: Optional[str] = Field(None, description="收藏品图片URL")
    rarity: str = Field(default="common", description="稀有度")
    description: Optional[str] = Field(None, description="收藏品描述")
    owned: bool = Field(default=False, description="是否已拥有")
    collected_at: Optional[str] = Field(None, description="收集时间")


class CityCollectionResponse(BaseModel):
    """城市收藏品响应"""
    city_id: str = Field(..., description="城市ID")
    city_name: str = Field(..., description="城市名称")
    total_items: int = Field(..., description="总收藏品数量")
    collected_count: int = Field(..., description="已收集数量")
    completion_rate: float = Field(..., description="完成率")
    items: List[CollectionItemResponse] = Field(..., description="收藏品列表")


class ArtifactDetailResponse(BaseModel):
    """文物详情响应"""
    artifact_id: str = Field(..., description="文物ID")
    name: str = Field(..., description="文物名称")
    description: Optional[str] = Field(None, description="文物描述")
    rarity: str = Field(..., description="稀有度")
    image_url: Optional[str] = Field(None, description="文物图片URL")
    story: Optional[str] = Field(None, description="文物故事")
    owned: bool = Field(..., description="是否拥有")
    count: int = Field(default=0, description="拥有数量")
    first_obtained_at: Optional[str] = Field(None, description="首次获得时间")


class UserArtifactResponse(BaseModel):
    """用户文物响应"""
    artifact_id: str = Field(..., description="文物ID")
    name: str = Field(..., description="文物名称")
    rarity: str = Field(..., description="稀有度")
    owned: bool = Field(..., description="是否拥有")
    count: int = Field(default=0, description="拥有数量")
    first_obtained_at: Optional[str] = Field(None, description="首次获得时间")


class CityArtifactsResponse(BaseModel):
    """城市文物列表响应"""
    city_id: str = Field(..., description="城市ID")
    city_name: str = Field(..., description="城市名称")
    total_artifacts: int = Field(..., description="总文物数量")
    collected_count: int = Field(..., description="已收集数量")
    completion_rate: float = Field(..., description="完成率")
    artifacts: List[UserArtifactResponse] = Field(..., description="文物列表")


# 🚫 PRD合规性清理：移除复杂弹药系统schema
# PRD要求简单弹药系统，不需要复杂的合成、统计、分类等功能
# 简单弹药系统已在game.py中实现：SimpleAmmoStatus, AmmoUsageRequest, AmmoUsageResponse

# PRD合规的简单弹药状态（已在game.py中定义）
# class SimpleAmmoStatus(BaseModel):
#     current_ammo: int = Field(..., description="当前弹药数量")
#     max_ammo: int = Field(default=999, description="最大弹药数量")
#     can_use_bonus: bool = Field(..., description="是否可以使用10发弹药获得1.2x伤害加成")


class UserResourcesResponse(BaseModel):
    """用户资源响应 - PRD合规版本"""
    user_id: str = Field(..., description="用户ID")
    gold: int = Field(..., description="金币数量")
    diamond: int = Field(..., description="钻石数量")
    ammo_count: int = Field(..., description="当前弹药数量")
    guardian_level: int = Field(..., description="守护者等级")
    guardian_exp: int = Field(..., description="守护者经验")
    stamina: int = Field(..., description="当前体力")
    max_stamina: int = Field(default=120, description="最大体力")

# 🚫 PRD合规性清理：移除复杂弹药重装系统
# PRD要求简单弹药系统，弹药通过收集小偷和垃圾获得，不需要复杂的重装机制


class RadarScanRequest(BaseModel):
    """雷达扫描请求"""
    city_id: str = Field(..., description="城市ID")
    scene_id: str = Field(..., description="场景ID")
    scan_range: float = Field(default=50.0, description="扫描范围")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "city_id": "beijing",
                "scene_id": "scene_beijing",
                "scan_range": 50.0
            }
        }
    }


class RadarHotspot(BaseModel):
    """雷达发现的热点"""
    hotspot_id: str = Field(..., description="热点ID")
    name: str = Field(..., description="热点名称")
    type: str = Field(..., description="热点类型")
    distance: float = Field(..., description="距离")
    direction: float = Field(..., description="方向角度")
    reward_preview: Dict[str, Any] = Field(..., description="奖励预览")


class RadarScanResponse(BaseModel):
    """雷达扫描响应"""
    success: bool = Field(..., description="是否成功")
    scan_range: float = Field(..., description="扫描范围")
    hotspots_found: List[RadarHotspot] = Field(..., description="发现的热点")
    scan_cost: Dict[str, int] = Field(default={}, description="扫描消耗")
    cooldown_remaining: int = Field(default=0, description="冷却剩余时间(秒)")
