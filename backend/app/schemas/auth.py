"""
认证相关的请求和响应模型
"""
from pydantic import BaseModel, Field
from typing import Optional, Union, Dict, Any, Literal
from datetime import datetime


class DeviceInfo(BaseModel):
    """设备信息对象"""
    userAgent: Optional[str] = Field(None, description="用户代理字符串")
    platform: Optional[str] = Field(None, description="平台信息")
    language: Optional[str] = Field(None, description="语言设置")
    screen: Optional[str] = Field(None, description="屏幕分辨率")
    timestamp: Optional[str] = Field(None, description="时间戳")


class GuestLoginRequest(BaseModel):
    """游客登录请求"""
    device_id: str = Field(..., min_length=1, max_length=64, description="设备唯一标识")
    device_info: Optional[Union[str, DeviceInfo, Dict[str, Any]]] = Field(None, description="设备信息，可以是字符串或对象")

    model_config = {
        "json_schema_extra": {
            "example": {
                "device_id": "device_123456",
                "device_info": {
                    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "platform": "Win32",
                    "language": "zh-CN",
                    "screen": "1920x1080",
                    "timestamp": "2025-06-25T08:00:00.000Z"
                }
            }
        }
    }


class TokenRefreshRequest(BaseModel):
    """刷新令牌请求"""
    refresh_token: str = Field(..., description="刷新令牌")


class UserInfo(BaseModel):
    """用户信息 - PRD合规版本"""
    user_id: str
    nickname: Optional[str]
    avatar_url: Optional[str] = None
    guardian_level: int  # PRD要求：守护者等级
    guardian_exp: int    # PRD要求：守护者经验值
    gold: int           # PRD要求：金币数量
    diamond: int        # PRD要求：钻石数量
    ammo_count: Optional[int] = None  # PRD要求：弹药数量
    stamina: Optional[int] = None     # PRD要求：当前体力值
    max_stamina: Optional[int] = None # PRD要求：最大体力值
    total_play_time: int
    created_at: Optional[datetime]


class LoginResponse(BaseModel):
    """登录响应"""
    user_id: str
    token: str
    refresh_token: str
    user_info: UserInfo
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "user_id": "guest_device_123456",
                "token": "eyJ0eXAiOiJKV1QiLCJhbGci...",
                "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGci...",
                "user_info": {
                    "user_id": "guest_device_123456",
                    "nickname": "游客12345678",
                    "guardian_level": 1,      # PRD要求：守护者等级
                    "guardian_exp": 0,        # PRD要求：守护者经验
                    "gold": 200,              # PRD要求：初始金币200
                    "diamond": 20,            # PRD要求：初始钻石20
                    "ammo_count": 50,         # PRD要求：初始弹药50
                    "stamina": 120,           # PRD要求：初始体力120
                    "max_stamina": 120,       # PRD要求：最大体力120
                    "total_play_time": 0,
                    "created_at": "2024-01-01T00:00:00Z"
                }
            }
        }
    }


class TokenRefreshResponse(BaseModel):
    """令牌刷新响应"""
    token: str
    refresh_token: str


class GoogleLoginRequest(BaseModel):
    """谷歌登录请求"""
    token: str = Field(..., description="Google ID Token")
    device_id: Optional[str] = Field(None, description="设备唯一标识")
    device_info: Optional[Union[str, DeviceInfo, Dict[str, Any]]] = Field(None, description="设备信息")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6...",
                "device_id": "device_123456",
                "device_info": {
                    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "platform": "Win32",
                    "language": "zh-CN",
                    "screen": "1920x1080",
                    "timestamp": "2025-06-25T08:00:00.000Z"
                }
            }
        }
    }


class UnifiedLoginRequest(BaseModel):
    """统一登录请求"""
    login_type: Literal["guest", "google", "crazygames"] = Field(..., description="登录类型：guest(游客登录)、google(谷歌登录) 或 crazygames(CrazyGames登录)")
    device_id: str = Field(..., min_length=1, max_length=64, description="设备唯一标识")
    device_info: Optional[Union[str, DeviceInfo, Dict[str, Any]]] = Field(None, description="设备信息，可以是字符串或对象")

    # 谷歌登录专用字段
    google_token: Optional[str] = Field(None, description="Google ID Token，谷歌登录时必填")

    # CrazyGames登录专用字段
    crazygames_token: Optional[str] = Field(None, description="CrazyGames Token，CrazyGames登录时必填")

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "summary": "游客登录",
                    "value": {
                        "login_type": "guest",
                        "device_id": "device_123456",
                        "device_info": {
                            "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                            "platform": "Win32",
                            "language": "zh-CN",
                            "screen": "1920x1080",
                            "timestamp": "2025-06-25T08:00:00.000Z"
                        }
                    }
                },
                {
                    "summary": "谷歌登录",
                    "value": {
                        "login_type": "google",
                        "device_id": "device_123456",
                        "google_token": "eyJhbGciOiJSUzI1NiIsImtpZCI6...",
                        "device_info": {
                            "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                            "platform": "Win32",
                            "language": "zh-CN",
                            "screen": "1920x1080",
                            "timestamp": "2025-06-25T08:00:00.000Z"
                        }
                    }
                }
            ]
        }
    }


class GoogleUserInfo(BaseModel):
    """谷歌用户信息"""
    google_id: str = Field(..., description="谷歌用户唯一ID")
    email: str = Field(..., description="用户邮箱")
    name: str = Field(..., description="用户姓名")
    picture: Optional[str] = Field(None, description="用户头像URL")
    email_verified: bool = Field(True, description="邮箱是否已验证")


class CrazyGamesLoginRequest(BaseModel):
    """CrazyGames登录请求"""
    token: str = Field(..., description="CrazyGames Token")
    device_id: Optional[str] = Field(None, description="设备唯一标识")
    device_info: Optional[Union[str, DeviceInfo, Dict[str, Any]]] = Field(None, description="设备信息")

    model_config = {
        "json_schema_extra": {
            "example": {
                "token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
                "device_id": "device_123456",
                "device_info": {
                    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "platform": "Win32",
                    "language": "zh-CN",
                    "screen": "1920x1080",
                    "timestamp": "2025-06-25T08:00:00.000Z"
                }
            }
        }
    }


class CrazyGamesTokenPayload(BaseModel):
    """CrazyGames Token载荷"""
    userId: str = Field(..., description="CrazyGames用户ID")
    gameId: str = Field(..., description="游戏ID")
    username: str = Field(..., description="用户名")
    profilePictureUrl: str = Field(..., description="头像URL")
    iat: int = Field(..., description="签发时间")
    exp: int = Field(..., description="过期时间")