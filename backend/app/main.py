"""
游戏后端主应用
基于 FastAPI + MySQL + Redis 的高性能游戏后端
"""
import logging
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>earer
from contextlib import asynccontextmanager
import uvicorn

from app.core.config import settings, game_config, hotspot_templates_config, scene_hotspots_config
from app.core.database import get_db, init_db, db_manager
from app.core.redis_client import redis_manager
from app.analytics.metrics import analytics_service, get_prometheus_metrics
from app.middleware.analytics import AnalyticsMiddleware
from app.middleware.security import SecurityMiddleware, InputValidationMiddleware
from app.middleware.error_handler import error_handler_middleware
from app.services.scheduler_service import scheduler_service

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动阶段
    logger.info("🚀 启动 FastAPI 应用...")
    
    try:
        # 初始化数据库
        await db_manager.initialize()
        logger.info("✅ 数据库连接已建立")
        
        # 初始化Redis
        await redis_manager.initialize()
        logger.info("✅ Redis连接已建立")
        
        # 启动收集缓存批量处理器
        from app.services.collection_cache_service import collection_cache_service
        await collection_cache_service.start_batch_processor()
        logger.info("✅ 收集缓存批量处理器已启动")
        
        # 启动经验值缓存批量处理器
        from app.services.experience_cache_service import experience_cache_service
        await experience_cache_service.start_batch_processor()
        logger.info("✅ 经验值缓存批量处理器已启动")
        
        # 初始化其他服务
        # await init_other_services()
        
    except Exception as e:
        logger.error(f"❌ 应用启动失败: {e}")
        raise
    
    yield  # 应用运行
    
    # 关闭阶段
    logger.info("🛑 关闭 FastAPI 应用...")
    
    try:
        # 停止批量处理器
        from app.services.collection_cache_service import collection_cache_service
        await collection_cache_service.stop_batch_processor()
        logger.info("✅ 收集缓存批量处理器已停止")
        
        from app.services.experience_cache_service import experience_cache_service
        await experience_cache_service.stop_batch_processor()
        logger.info("✅ 经验值缓存批量处理器已停止")
        
        # 关闭Redis连接
        await redis_manager.close()
        logger.info("✅ Redis连接已关闭")
        
        # 关闭数据库连接
        await db_manager.close()
        logger.info("✅ 数据库连接已关闭")
        
    except Exception as e:
        logger.error(f"❌ 应用关闭失败: {e}")


# 创建FastAPI应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    version="1.0.0",
    description="基于 FastAPI + MySQL + Redis 的高性能游戏后端",
    docs_url="/docs",  # 强制启用文档
    redoc_url="/redoc",  # 强制启用ReDoc
    lifespan=lifespan,
    # 添加安全方案定义，让Swagger UI显示授权按钮
    openapi_tags=[
        {"name": "认证", "description": "用户认证和登录相关接口"},
        {"name": "游戏", "description": "游戏核心功能接口"},
        {"name": "任务", "description": "任务系统相关接口"},
        {"name": "用户", "description": "用户信息管理接口"},
        {"name": "排行榜", "description": "排行榜相关接口"},
        {"name": "配置管理", "description": "游戏配置管理接口"},
        {"name": "转盘抽奖", "description": "转盘抽奖相关接口"},
        {"name": "健康检查", "description": "系统监控接口"},
    ]
)

# 中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000", "*"],  # 明确允许前端域名
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 安全中间件
app.add_middleware(SecurityMiddleware)
app.add_middleware(InputValidationMiddleware)

# 分析中间件
if settings.ENABLE_ANALYTICS:
    app.add_middleware(AnalyticsMiddleware)

# 错误处理中间件
app.middleware("http")(error_handler_middleware)

# 定义安全方案 - 让Swagger UI显示Authorize按钮
security = HTTPBearer()

def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    # 使用FastAPI的get_openapi函数而不是app.openapi()来避免递归
    from fastapi.openapi.utils import get_openapi

    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    openapi_schema["components"]["securitySchemes"] = {
        "HTTPBearer": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "输入您的JWT token，格式：Bearer <token>"
        }
    }

    # 为需要认证的接口添加安全要求
    for path in openapi_schema["paths"]:
        for method in openapi_schema["paths"][path]:
            if openapi_schema["paths"][path][method].get("security"):
                openapi_schema["paths"][path][method]["security"] = [{"HTTPBearer": []}]

    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi

# 导入和注册路由
from app.api.v1.api import api_router
# TODO: admin模块暂时注释，等后续实现
# from app.api.admin.api import admin_api_router
app.include_router(api_router, prefix=settings.API_V1_STR)
# app.include_router(admin_api_router, prefix="/api/admin")

# 临时解决方案：直接添加谷歌登录路由
from fastapi import Request, HTTPException, status, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.schemas.auth import GoogleLoginRequest, LoginResponse

# 健康检查端点
@app.get("/health", tags=["monitoring"])
async def health_check():
    """健康检查端点"""
    try:
        # 检查数据库连接
        db_healthy = await db_manager.health_check()
    except:
        db_healthy = False
    
    health_status = {
        "status": "healthy",
        "database": db_healthy,
        "redis": await redis_manager.health_check(),
    }
    
    # 如果任何服务不健康，返回503
    if not all([health_status["database"], health_status["redis"]]):
        health_status["status"] = "unhealthy"
        return health_status, 503
    
    return health_status


# Prometheus监控端点
@app.get("/metrics", tags=["monitoring"])
async def metrics():
    """Prometheus监控指标"""
    return get_prometheus_metrics()


# 实时数据端点
@app.get("/analytics/realtime", tags=["analytics"])
async def get_realtime_analytics():
    """获取实时分析数据"""
    return await analytics_service.get_realtime_metrics()


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    ) 