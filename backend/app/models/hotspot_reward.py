"""
热点奖励配置模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DECIMAL, BigInteger, TIMESTAMP, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, List, Any, Optional
import random

from app.core.database import Base

class HotspotRewardConfig(Base):
    """热点奖励配置表"""
    __tablename__ = "hotspot_reward_config"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    hotspot_type = Column(String(50), nullable=False, comment="热点类型：thief, garbage, treasure_box, boss")
    base_drop_rate = Column(DECIMAL(5,4), nullable=False, default=0.5000, comment="基础掉落概率(0-1)")
    reward_type = Column(String(20), nullable=False, comment="奖励类型：experience, artifact")
    min_amount = Column(Integer, nullable=False, default=1, comment="最小奖励数量")
    max_amount = Column(Integer, nullable=False, default=10, comment="最大奖励数量")
    weight = Column(Integer, nullable=False, default=100, comment="权重，用于多奖励随机")
    is_enabled = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"))
    updated_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "hotspot_type": self.hotspot_type,
            "base_drop_rate": float(self.base_drop_rate),
            "reward_type": self.reward_type,
            "min_amount": self.min_amount,
            "max_amount": self.max_amount,
            "weight": self.weight,
            "is_enabled": self.is_enabled,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

class SessionRandomSeed(Base):
    """游戏会话随机种子表"""
    __tablename__ = "session_random_seed"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(36), nullable=False, unique=True, comment="会话ID")
    user_id = Column(Integer, nullable=False, comment="用户ID")
    random_seed = Column(BigInteger, nullable=False, comment="随机数种子")
    thief_collected_count = Column(Integer, default=0, comment="本会话已收集小偷数量")
    garbage_collected_count = Column(Integer, default=0, comment="本会话已收集垃圾数量")
    treasure_collected_count = Column(Integer, default=0, comment="本会话已收集宝箱数量")
    boss_defeated_count = Column(Integer, default=0, comment="本会话已击败BOSS数量")
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"))

    def get_seeded_random(self, increment: int = 0) -> random.Random:
        """获取基于种子的随机数生成器"""
        seed = self.random_seed + increment
        rng = random.Random(seed)
        return rng

    def get_next_random_value(self, hotspot_type: str) -> float:
        """获取下一个随机值（0-1之间）"""
        # 根据热点类型和已收集数量计算增量
        if hotspot_type == "thief":
            increment = self.thief_collected_count
        elif hotspot_type == "garbage":
            increment = self.garbage_collected_count
        elif hotspot_type == "treasure_box":
            increment = self.treasure_collected_count
        elif hotspot_type == "boss":
            increment = self.boss_defeated_count
        else:
            increment = 0
        
        rng = self.get_seeded_random(increment)
        return rng.random()

    def increment_count(self, hotspot_type: str):
        """增加对应类型的收集计数"""
        if hotspot_type == "thief":
            self.thief_collected_count += 1
        elif hotspot_type == "garbage":
            self.garbage_collected_count += 1
        elif hotspot_type == "treasure_box":
            self.treasure_collected_count += 1
        elif hotspot_type == "boss":
            self.boss_defeated_count += 1

class HotspotDropHistory(Base):
    """热点掉落历史记录表"""
    __tablename__ = "hotspot_drop_history"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(36), nullable=False, comment="会话ID")
    user_id = Column(Integer, nullable=False, comment="用户ID")
    hotspot_type = Column(String(50), nullable=False, comment="热点类型")
    hotspot_name = Column(String(100), nullable=False, comment="热点名称")
    reward_type = Column(String(20), comment="获得奖励类型，NULL表示无奖励")
    reward_amount = Column(Integer, default=0, comment="获得奖励数量")
    drop_probability = Column(DECIMAL(5,4), nullable=False, comment="实际掉落概率")
    random_value = Column(DECIMAL(10,8), nullable=False, comment="随机数值")
    dropped = Column(Boolean, nullable=False, default=False, comment="是否掉落奖励")
    collected_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"))

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "user_id": self.user_id,
            "hotspot_type": self.hotspot_type,
            "hotspot_name": self.hotspot_name,
            "reward_type": self.reward_type,
            "reward_amount": self.reward_amount,
            "drop_probability": float(self.drop_probability) if self.drop_probability else 0,
            "random_value": float(self.random_value),
            "dropped": self.dropped,
            "collected_at": self.collected_at
        }