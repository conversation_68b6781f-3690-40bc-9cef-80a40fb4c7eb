"""
游戏相关数据模型
"""
from sqlalchemy import Column, BigInteger, String, Integer, DateTime, Boolean, Enum, ForeignKey, Numeric, JSON, Text, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.core.database import Base


class SessionStatus(str, enum.Enum):
    """会话状态枚举"""
    ACTIVE = "active"
    COMPLETED = "completed"
    ABANDONED = "abandoned"


class HotspotType(str, enum.Enum):
    """热点类型枚举"""
    THIEF = "thief"
    GARBAGE = "garbage"
    TREASURE = "treasure"
    BOSS_THIEF = "boss_thief"


class RewardType(str, enum.Enum):
    """奖励类型枚举"""
    GOLD = "gold"
    DIAMOND = "diamond"
    ARTIFACT = "artifact"


class GameSession(Base):
    """游戏会话表"""
    __tablename__ = "game_sessions"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    session_id = Column(String(64), unique=True, nullable=False, comment="会话唯一标识")
    user_id = Column(BigInteger, nullable=False, comment="用户ID，软关联")
    scene_id = Column(String(50), nullable=False, comment="场景ID")
    city_id = Column(String(50), nullable=False, comment="城市ID")
    status = Column(Enum(SessionStatus), default=SessionStatus.ACTIVE)
    
    # --- 以下字段已移至 User 模型 ---
    # thieves_collected = Column(Integer, default=0, comment="收集的小偷数量")
    # garbage_collected = Column(Integer, default=0, comment="收集的垃圾军团数量")
    # ammo_count = Column(Integer, default=0, comment="合成的弹药数量")
    # --- 结束 ---
    
    gold_earned = Column(Integer, default=0, comment="获得的金币")
    diamond_earned = Column(Integer, default=0, comment="获得的钻石")
    star = Column(Integer, default=0, comment="星级")
    started_at = Column(DateTime, server_default=func.now())
    ended_at = Column(DateTime)
    duration = Column(Integer, default=0, comment="持续时间(秒)")
    
    # 关系 (使用软关联，不依赖外键)
    # user = relationship("User", back_populates="game_sessions")  # 已移除强关联
    # hotspots = relationship("HotspotRecord", back_populates="session", lazy="dynamic")  # 已移除强关联
    # boss_battles = relationship("BossBattle", back_populates="session", lazy="dynamic")  # 已移除强关联
    
    # 索引
    __table_args__ = (
        Index("idx_user_id", "user_id"),
        Index("idx_scene_id", "scene_id"),
        Index("idx_status", "status"),
        Index("idx_started_at", "started_at"),
    )
    
    def to_dict(self):
        """转换为字典"""
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "scene_id": self.scene_id,
            "city_id": self.city_id,
            "status": self.status.value if self.status else None,
            "gold_earned": self.gold_earned,
            "diamond_earned": self.diamond_earned,
            "star": self.star,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "ended_at": self.ended_at.isoformat() if self.ended_at else None,
            "duration": self.duration
        }


class HotspotRecord(Base):
    """热点记录表"""
    __tablename__ = "hotspot_records"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    session_id = Column(BigInteger, nullable=False, comment="会话ID，软关联")
    name = Column(String(50), nullable=False, comment="热点名称")
    hotspot_type = Column(Enum(HotspotType), nullable=False)
    position_x = Column(Numeric(10, 6), nullable=False, comment="X坐标")
    position_y = Column(Numeric(10, 6), nullable=False, comment="Y坐标")
    reward_type = Column(Enum(RewardType), nullable=False)
    reward_amount = Column(Integer, default=0)
    artifact_id = Column(String(50), comment="如果是文物奖励，记录文物ID")
    collected = Column(Boolean, default=False)
    collected_at = Column(DateTime)
    created_at = Column(DateTime, server_default=func.now())
    
    # 关系 (已移除强关联)
    # session = relationship("GameSession", back_populates="hotspots")
    
    # 索引
    __table_args__ = (
        Index("idx_session_id", "session_id"),
        Index("idx_hotspot_type", "hotspot_type"),
        Index("idx_collected", "collected"),
    )


class UserHotspotCollection(Base):
    """用户热点收集记录表 - 用于记录用户在特定场景中收集过的热点"""
    __tablename__ = "user_hotspot_collections"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False, comment="用户ID，软关联")
    city_id = Column(String(50), nullable=False, comment="城市ID")
    scene_id = Column(String(50), nullable=False, comment="场景ID")
    hotspot_name = Column(String(50), nullable=False, comment="热点名称")
    hotspot_type = Column(Enum(HotspotType), nullable=False)
    collected_at = Column(DateTime, server_default=func.now())
    
    # 关系 (已移除强关联)
    # user = relationship("User")
    
    # 索引
    __table_args__ = (
        Index("idx_user_scene", "user_id", "city_id", "scene_id"),
        Index("idx_user_hotspot", "user_id", "city_id", "scene_id", "hotspot_name"),
        Index("idx_hotspot_type", "hotspot_type"),
    )


# 🚫 PRD合规性清理：移除BossBattle模型
# PRD要求使用BOSS血量系统，不需要主动攻击BOSS的战斗记录
# BOSS血量通过收集行为（抓捕小偷、清理垃圾、古迹问答）自动减少
# 相关功能已在boss_health.py中的BossHealth模型实现


class ShareRecord(Base):
    """分享拉新记录表"""
    __tablename__ = "share_records"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    sharer_id = Column(BigInteger, nullable=False, comment="分享者ID，软关联")
    invitee_id = Column(BigInteger, comment="被邀请者ID，软关联")
    share_code = Column(String(32), unique=True, nullable=False, comment="分享码")
    city_unlocked = Column(String(50), comment="解锁的城市")
    status = Column(Enum("pending", "completed", "expired", name="share_status"), default="pending")
    created_at = Column(DateTime, server_default=func.now())
    completed_at = Column(DateTime)
    
    # 关系 (已移除强关联)
    # sharer = relationship("User", foreign_keys=[sharer_id])
    # invitee = relationship("User", foreign_keys=[invitee_id])
    
    # 索引
    __table_args__ = (
        Index("idx_sharer_id", "sharer_id"),
        Index("idx_status", "status"),
    )


class LeaderboardSnapshot(Base):
    """排行榜快照表"""
    __tablename__ = "leaderboard_snapshots"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False, comment="用户ID，软关联")
    leaderboard_type = Column(Enum("artifact_collector", "city_guardian", "weekly_active", name="leaderboard_type"), nullable=False)
    rank = Column(Integer, nullable=False)
    score = Column(Integer, nullable=False)
    extra_data = Column(JSON, comment="额外数据，如图鉴进度、游戏时长等")
    snapshot_date = Column(DateTime, nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    
    # 关系 (已移除强关联)
    # user = relationship("User")
    
    # 索引
    __table_args__ = (
        Index("idx_leaderboard_date", "leaderboard_type", "snapshot_date"),
        Index("idx_user_id", "user_id"),
        Index("idx_rank", "rank"),
    )


class AIInteraction(Base):
    """AI交互记录表"""
    __tablename__ = "ai_interactions"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False, comment="用户ID，软关联")
    interaction_type = Column(Enum("thief_diary", "boss_clue", "artifact_story", name="ai_interaction_type"), nullable=False)
    context_id = Column(String(100), comment="上下文ID，如文物ID、任务ID等")
    prompt = Column(Text, comment="发送给AI的提示")
    response = Column(Text, comment="AI的回复")
    created_at = Column(DateTime, server_default=func.now())
    
    # 关系 (已移除强关联)
    # user = relationship("User")
    
    # 索引
    __table_args__ = (
        Index("idx_user_id", "user_id"),
        Index("idx_interaction_type", "interaction_type"),
        Index("idx_created_at", "created_at"),
    )


class SceneHotspot(Base):
    """场景热点配置表 - 管理后台配置的热点数据"""
    __tablename__ = "scene_hotspots"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    scene_id = Column(String(50), nullable=False, comment="场景ID")
    city_id = Column(String(50), nullable=False, comment="城市ID")
    hotspot_name = Column(String(50), nullable=False, comment="热点名称")
    hotspot_type = Column(Enum("thief", "garbage", "treasure", "boss_thief", name="hotspot_type_enum"), nullable=False, comment="热点类型")

    # 位置信息
    position_x = Column(Numeric(10, 6), nullable=False, comment="X坐标(ath)")
    position_y = Column(Numeric(10, 6), nullable=False, comment="Y坐标(atv)")
    scale = Column(Numeric(5, 2), default=1.0, comment="缩放比例")

    # 显示信息
    image_url = Column(String(500), comment="热点图片URL")
    visible = Column(Boolean, default=True, comment="是否可见")
    enabled = Column(Boolean, default=True, comment="是否启用")

    # 奖励信息
    reward_type = Column(Enum("gold", "diamond", "artifact", "treasure_box", name="reward_type_enum"), nullable=False, comment="奖励类型")
    reward_amount = Column(Integer, default=0, comment="奖励数量")
    reward_data = Column(JSON, comment="完整奖励数据")

    # 交互信息
    onclick_action = Column(String(200), comment="点击事件")
    description = Column(String(500), comment="热点描述")

    # 元数据
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    created_by = Column(String(50), comment="创建者")

    # 索引
    __table_args__ = (
        Index("idx_scene_city", "scene_id", "city_id"),
        Index("idx_hotspot_type", "hotspot_type"),
        Index("idx_enabled", "enabled"),
    )