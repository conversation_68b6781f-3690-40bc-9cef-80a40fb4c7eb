"""
用户相关数据模型
"""
from sqlalchemy import Column, BigInteger, String, Integer, DateTime, Boolean, Enum, ForeignKey, UniqueConstraint, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import enum

from app.core.database import Base


class LoginType(str, enum.Enum):
    """登录类型枚举"""
    GUEST = "guest"
    FACEBOOK = "facebook"
    GOOGLE = "google"
    APPLE = "apple"
    CRAZYGAMES = "crazygames"


class User(Base):
    """用户基础信息表"""
    __tablename__ = "users"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(String(64), unique=True, nullable=False, comment="用户唯一标识")
    nickname = Column(String(50), comment="昵称")
    email = Column(String(100), nullable=True, comment="邮箱")
    avatar_url = Column(String(255), comment="头像URL")
    login_type = Column(String(20), default="guest", comment="登录类型")
    device_id = Column(String(100), comment="设备ID")
    device_info = Column(String(1000), comment="设备信息")
    last_login = Column(DateTime, comment="最后登录时间")
    is_active = Column(Boolean, default=True, comment="是否激活")
    # 守护者等级系统
    guardian_level = Column(Integer, default=1, comment="守护者等级")
    guardian_exp = Column(Integer, default=0, comment="守护者经验值")
    total_exp = Column(Integer, default=0, comment="累计经验值")
    
    # 体力值系统（PRD要求）
    stamina = Column(Integer, default=120, comment="当前体力值")
    max_stamina = Column(Integer, default=120, comment="最大体力值")
    last_stamina_update = Column(DateTime, default=datetime.utcnow, comment="最后体力更新时间")
    stamina_ad_count = Column(Integer, default=0, comment="体力广告观看次数")
    last_stamina_ad_time = Column(DateTime, comment="最后观看体力广告时间")
    
    # 货币系统
    gold = Column(Integer, default=0, comment="金币数量")
    diamond = Column(Integer, default=0, comment="钻石数量")
    
    # 道具系统（简化版 - 根据PRD要求）
    magnifier_count = Column(Integer, default=0, comment="放大镜数量")
    radar_count = Column(Integer, default=0, comment="雷达数量")
    stamina_potion_count = Column(Integer, default=0, comment="体力药水数量")

    # 🚫 PRD合规性清理：移除弹药系统，PRD中没有弹药概念
    
    # 收集统计
    thieves_collected = Column(Integer, default=0, comment="收集的小偷数量")
    garbage_collected = Column(Integer, default=0, comment="收集的垃圾数量")
    
    total_play_time = Column(Integer, default=0, comment="总游戏时长(秒)")
    last_login_time = Column(DateTime, comment="最后登录时间")
    is_deleted = Column(Boolean, default=False, comment="软删除标记")
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # 关系 (减少外键约束，使用软关联)
    # 注意：为了提高性能和减少约束，这里不使用强制外键
    # 在应用层保证数据一致性
    
    # 被动收益和守护等级关联
    passive_income_records = relationship("PassiveIncomeRecord", back_populates="user")
    guardian_level_rel = relationship("GuardianLevel", back_populates="user", uselist=False)
    guardian_upgrade_records = relationship("GuardianUpgradeRecord", back_populates="user")
    
    # 索引
    __table_args__ = (
        Index("idx_guardian_level", "guardian_level"),
        Index("idx_user_id", "user_id"),
        Index("idx_login_type", "login_type"),
    )
    
    # 游戏统计（PRD要求的三大关卡）
    total_thieves_caught = Column(Integer, default=0, comment="累积抓捕小偷数量")
    total_rubbish_cleaned = Column(Integer, default=0, comment="累积清理垃圾数量")
    total_monuments_restored = Column(Integer, default=0, comment="累积修复古迹数量")
    total_quiz_answered = Column(Integer, default=0, comment="累积回答问题数量")
    total_quiz_correct = Column(Integer, default=0, comment="累积正确回答数量")
    
    # 宝箱和奖励统计
    treasure_boxes_opened = Column(Integer, default=0, comment="开启宝箱总数")
    copper_boxes_opened = Column(Integer, default=0, comment="铜宝箱开启数")
    silver_boxes_opened = Column(Integer, default=0, comment="银宝箱开启数")
    gold_boxes_opened = Column(Integer, default=0, comment="金宝箱开启数")
    
    # 广告统计
    total_ads_watched = Column(Integer, default=0, comment="观看广告总次数")
    stamina_ads_watched = Column(Integer, default=0, comment="体力恢复广告次数")
    reward_ads_watched = Column(Integer, default=0, comment="奖励翻倍广告次数")
    
    # 被动收益系统（简化版）
    offline_income_rate = Column(Integer, default=2, comment="离线收益速率(经验/分钟)")
    last_offline_collection = Column(DateTime, comment="最后收集离线收益时间")
    
    # PRD合规性标记 - 移除VIP系统和复杂弹药系统
    # VIP系统和复杂弹药系统不在PRD中，已移除
    
    @property
    def level(self):
        """别名：守护者等级"""
        return self.guardian_level
    
    @property
    def exp(self):
        """别名：守护者经验值"""
        return self.guardian_exp
    
    def to_dict(self):
        """转换为字典"""
        return {
            "user_id": self.user_id,
            "nickname": self.nickname,
            "avatar_url": self.avatar_url,
            "guardian_level": self.guardian_level,
            "guardian_exp": self.guardian_exp,
            "total_exp": self.total_exp,
            "stamina": self.stamina,
            "max_stamina": self.max_stamina,
            "gold": self.gold,
            "diamond": self.diamond,
            "total_play_time": self.total_play_time,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

    def get_guardian_appearance(self):
        """获取守护者外观"""
        if self.guardian_level <= 3:
            return "初级守卫"
        elif self.guardian_level <= 6:
            return "中级守护者"
        elif self.guardian_level <= 9:
            return "高级执法者"
        else:
            return "传奇守护者"

    def get_offline_income_rate(self):
        """获取被动收益速率（经验/分钟）"""
        # 根据PRD要求的被动收益公式
        level_rates = {
            1: 2, 2: 3, 3: 5, 4: 8, 5: 12,
            6: 15, 7: 20, 8: 30, 9: 35
        }
        if self.guardian_level >= 10:
            return min(60, 40 + (self.guardian_level - 10) * 2)
        return level_rates.get(self.guardian_level, 2)

    def calculate_level_up_requirement(self):
        """计算升级所需经验值"""
        # 根据PRD的升级经验表
        requirements = {
            1: 100, 2: 150, 3: 250, 4: 500, 5: 1000,
            6: 2000, 7: 4000, 8: 7000, 9: 10000
        }
        if self.guardian_level >= 10:
            return 15000
        return requirements.get(self.guardian_level, 15000)

    def can_level_up(self):
        """检查是否可以升级"""
        return self.guardian_exp >= self.calculate_level_up_requirement()

    def level_up(self):
        """升级处理"""
        requirement = self.calculate_level_up_requirement()
        if self.guardian_exp >= requirement:
            self.guardian_exp -= requirement
            self.guardian_level += 1
            # 升级时完全恢复体力
            self.stamina = self.max_stamina
            self.last_stamina_update = datetime.utcnow()
            return True
        return False

    def restore_stamina_naturally(self):
        """自然恢复体力（每3分钟1点）"""
        if not self.last_stamina_update:
            self.last_stamina_update = datetime.utcnow()
            return
            
        now = datetime.utcnow()
        time_diff = (now - self.last_stamina_update).total_seconds()
        stamina_to_restore = int(time_diff // 180)  # 每180秒（3分钟）恢复1点
        
        if stamina_to_restore > 0:
            self.stamina = min(self.max_stamina, self.stamina + stamina_to_restore)
            self.last_stamina_update = now

    def get_experience_efficiency(self):
        """获取经验获取效率"""
        # 体力低于30时，经验减少25%
        if self.stamina < 30:
            return 0.75
        return 1.0

    def consume_stamina(self, amount):
        """消耗体力"""
        if self.stamina >= amount:
            self.stamina -= amount
            return True
        return False

    def can_watch_stamina_ad(self):
        """检查是否可以观看体力恢复广告"""
        if not self.last_stamina_ad_time:
            return True
        now = datetime.utcnow()
        time_diff = (now - self.last_stamina_ad_time).total_seconds()
        # 每小时最多3次，即每20分钟可以观看1次
        return time_diff >= 1200 and self.stamina_ad_count < 3

    def watch_stamina_ad(self):
        """观看体力恢复广告"""
        if self.can_watch_stamina_ad():
            self.stamina = min(self.max_stamina, self.stamina + 30)
            self.stamina_ad_count += 1
            self.stamina_ads_watched += 1
            self.total_ads_watched += 1
            self.last_stamina_ad_time = datetime.utcnow()
            return True
        return False


class UserLoginLog(Base):
    """用户登录日志表"""
    __tablename__ = "user_login_logs"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False, comment="用户ID，软关联")
    user_uuid = Column(String(64), nullable=False, comment="用户UUID")
    login_time = Column(DateTime, server_default=func.now())
    login_ip = Column(String(45))
    device_info = Column(String(1000))  # 增加字段长度以容纳详细的设备信息
    login_type = Column(Enum(LoginType), default=LoginType.GUEST)
    session_duration = Column(Integer, default=0, comment="会话时长(秒)")

    # 索引 (移除外键约束，使用普通索引)
    __table_args__ = (
        Index("idx_user_id", "user_id"),
        Index("idx_user_uuid", "user_uuid"),
        Index("idx_login_time", "login_time"),
        Index("idx_login_type", "login_type"),
    )


# CannonUpgrade类已删除 - PRD中只需要简单的弹药系统，不需要复杂的大炮升级
