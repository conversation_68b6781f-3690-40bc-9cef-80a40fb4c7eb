"""
文明守护者系统API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
import uuid

from app.core.database import get_db
from app.models.user import User
from app.services.civilization_service import CivilizationService
from app.schemas.civilization import (
    CivilizationStatusResponse,
    CivilizationExpLogResponse,
    BossStatusResponse,
    BossAttackRequest,
    BossAttackResponse,
    BossDialogueResponse
)
from app.api.deps import get_current_user

router = APIRouter()
civilization_service = CivilizationService()


@router.get("/user/civilization/status", response_model=CivilizationStatusResponse)
async def get_civilization_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户文明守护者状态"""
    try:
        status_data = await civilization_service.get_user_status(db, current_user.id)
        return CivilizationStatusResponse(success=True, data=status_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文明状态失败: {str(e)}"
        )


@router.get("/user/civilization/exp-logs", response_model=CivilizationExpLogResponse)
async def get_civilization_exp_logs(
    limit: int = 20,
    offset: int = 0,
    source: str = "all",
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取文明经验记录"""
    try:
        logs_data = await civilization_service.get_exp_logs(
            db, current_user.id, limit, offset, source
        )
        return CivilizationExpLogResponse(success=True, data=logs_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取经验记录失败: {str(e)}"
        )


@router.get("/boss/status/{city_id}", response_model=BossStatusResponse)
async def get_boss_status(
    city_id: str,
    db: Session = Depends(get_db)
):
    """获取城市BOSS状态"""
    try:
        boss_data = await civilization_service.get_boss_status(db, city_id)
        return BossStatusResponse(success=True, data=boss_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取BOSS状态失败: {str(e)}"
        )


# 🚫 PRD合规性清理：移除BOSS攻击端点
# PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少


@router.get("/boss/dialogue/{city_id}/{stage}", response_model=BossDialogueResponse)
async def get_boss_dialogue(
    city_id: str,
    stage: str,
    db: Session = Depends(get_db)
):
    """获取BOSS对话"""
    try:
        dialogue_data = await civilization_service.get_boss_dialogue(db, city_id, stage)
        return BossDialogueResponse(success=True, data=dialogue_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到对话内容: {str(e)}"
        )


@router.post("/boss/reset/{city_id}")
async def reset_boss(
    city_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """重置城市BOSS（管理员功能或新城市解锁）"""
    try:
        # 注意：这里可能需要添加权限检查
        result = await civilization_service.reset_boss(db, city_id)
        return {"success": True, "message": f"城市 {city_id} 的BOSS已重置"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重置BOSS失败: {str(e)}"
        )


@router.post("/user/guardian/level-up")
async def level_up_guardian(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """守望者升级"""
    try:
        result = await civilization_service.level_up_guardian(db, current_user.id)
        return {"success": True, "data": result}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"升级失败: {str(e)}"
        )