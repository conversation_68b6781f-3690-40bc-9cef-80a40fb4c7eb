"""
用户相关API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

from app.core.database import get_db
from app.api.deps import get_current_user
from app.models.user import User
from app.models.game import GameSession, SessionStatus
from app.analytics.metrics import track_api_performance

router = APIRouter()


class UserUpdateRequest(BaseModel):
    """用户信息更新请求"""
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None


class UserInfoResponse(BaseModel):
    """用户信息响应 - PRD合规版本"""
    user_id: str
    nickname: Optional[str]
    avatar_url: Optional[str]
    guardian_level: int  # PRD要求：守护者等级
    guardian_exp: int    # PRD要求：守护者经验
    gold: int           # PRD要求：金币
    diamond: int        # PRD要求：钻石
    ammo_count: int     # PRD要求：弹药数量
    stamina: int        # PRD要求：当前体力
    max_stamina: int    # PRD要求：最大体力
    total_play_time: int
    created_at: str

    # PRD合规的统计信息
    total_sessions: int = 0
    total_artifacts: int = 0
    cities_unlocked: int = 1  # 默认解锁北京
    thieves_collected: int = 0    # PRD要求：收集的小偷数量
    garbage_collected: int = 0    # PRD要求：收集的垃圾数量


async def get_current_ammo_count(db: AsyncSession, user_id: int) -> int:
    """获取用户当前实时合成弹药数量"""
    # 直接从用户表获取弹药数量（已迁移到用户表）
    result = await db.execute(
        select(User).where(User.id == user_id)
    )

    user = result.scalars().first()
    if user:
        return user.ammo_count or 0
    else:
        return 0


@router.get("/me", response_model=UserInfoResponse)
@track_api_performance("/user/me")
async def get_current_user_info(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取当前用户信息

    返回用户的详细信息，包括：
    - 基础信息（昵称、等级、经验等）
    - 资产信息（金币、钻石）
    - 游戏统计（总游戏时长、会话数等）
    - 实时弹药数量（基于当前活跃会话）
    """
    # 获取实时弹药数量
    current_ammo = await get_current_ammo_count(db, current_user.id)
    
    # TODO: 获取额外的统计信息
    # - 总会话数
    # - 收集的文物数
    # - 解锁的城市数

    return UserInfoResponse(
        user_id=current_user.user_id,
        nickname=current_user.nickname,
        avatar_url=current_user.avatar_url,
        guardian_level=current_user.guardian_level or 1,  # PRD要求：守护者等级
        guardian_exp=current_user.guardian_exp or 0,      # PRD要求：守护者经验
        gold=current_user.gold or 200,                    # PRD要求：初始金币200
        diamond=current_user.diamond or 20,               # PRD要求：初始钻石20
        ammo_count=current_ammo,                          # PRD要求：弹药数量
        stamina=current_user.stamina or 120,              # PRD要求：当前体力
        max_stamina=current_user.max_stamina or 120,      # PRD要求：最大体力120
        total_play_time=current_user.total_play_time,
        created_at=current_user.created_at.isoformat(),
        thieves_collected=current_user.thieves_collected or 0,  # PRD要求
        garbage_collected=current_user.garbage_collected or 0   # PRD要求
    )


@router.get("/profile", response_model=UserInfoResponse)
@track_api_performance("/user/profile")
async def get_user_profile(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户信息（别名接口）

    与 /me 接口功能相同，为了兼容前端调用
    """
    # 获取实时弹药数量
    current_ammo = await get_current_ammo_count(db, current_user.id)
    
    return UserInfoResponse(
        user_id=current_user.user_id,
        nickname=current_user.nickname,
        avatar_url=current_user.avatar_url,
        guardian_level=current_user.guardian_level or 1,  # PRD要求：守护者等级
        guardian_exp=current_user.guardian_exp or 0,      # PRD要求：守护者经验
        gold=current_user.gold or 200,                    # PRD要求：初始金币200
        diamond=current_user.diamond or 20,               # PRD要求：初始钻石20
        ammo_count=current_ammo,                          # PRD要求：弹药数量
        stamina=current_user.stamina or 120,              # PRD要求：当前体力
        max_stamina=current_user.max_stamina or 120,      # PRD要求：最大体力120
        total_play_time=current_user.total_play_time,
        created_at=current_user.created_at.isoformat(),
        thieves_collected=current_user.thieves_collected or 0,  # PRD要求
        garbage_collected=current_user.garbage_collected or 0   # PRD要求
    )


@router.put("/me")
@track_api_performance("/user/update")
async def update_user_info(
    request: UserUpdateRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新用户信息
    
    可更新的字段：
    - nickname: 昵称
    - avatar_url: 头像URL
    """
    updated = False
    
    if request.nickname is not None:
        # TODO: 添加昵称验证（长度、敏感词等）
        current_user.nickname = request.nickname
        updated = True
    
    if request.avatar_url is not None:
        # TODO: 添加头像URL验证
        current_user.avatar_url = request.avatar_url
        updated = True
    
    if updated:
        await db.commit()
        await db.refresh(current_user)
    
    return {
        "success": True,
        "user_info": {
            "user_id": current_user.user_id,
            "nickname": current_user.nickname,
            "avatar_url": current_user.avatar_url
        }
    }


@router.get("/stats")
@track_api_performance("/user/stats")
async def get_user_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户游戏统计信息
    
    包括：
    - 游戏统计（总时长、会话数、胜率等）
    - 收集统计（文物数、城市进度等）
    - 经济统计（总获得金币、消耗等）
    """
    # TODO: 实现详细的统计查询
    
    return {
        "game_stats": {
            "total_play_time": current_user.total_play_time,
            "total_sessions": 0,  # TODO: 查询实际数据
            "boss_defeated": 0,   # TODO: 查询实际数据
            "hotspots_collected": 0  # TODO: 查询实际数据
        },
        "collection_stats": {
            "total_artifacts": 0,  # TODO: 查询实际数据
            "cities_unlocked": 1,  # TODO: 查询实际数据
            "collection_rate": 0.0  # TODO: 计算收集率
        },
        "economy_stats": {
            "total_gold_earned": 0,  # TODO: 查询实际数据
            "total_gold_spent": 0,   # TODO: 查询实际数据
            "total_diamond_earned": 0,  # TODO: 查询实际数据
            "total_diamond_spent": 0   # TODO: 查询实际数据
        }
    }


@router.delete("/data")
async def delete_user_data(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除用户数据（待实现）
    
    用于满足隐私政策要求，允许用户删除其所有数据
    """
    return {
        "message": "User data deletion is not implemented yet",
        "info": "Please contact support for data deletion requests"
    } 