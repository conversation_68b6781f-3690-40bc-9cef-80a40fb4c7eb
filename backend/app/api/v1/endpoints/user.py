"""
用户相关API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

from app.core.database import get_db
from app.api.deps import get_current_user
from app.models.user import User
from app.models.game import GameSession, SessionStatus
from app.analytics.metrics import track_api_performance

router = APIRouter()


class UserUpdateRequest(BaseModel):
    """用户信息更新请求"""
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None


class UserInfoResponse(BaseModel):
    user_id: str
    nickname: Optional[str]
    avatar_url: Optional[str]
    guardian_level: int  # PRD要求：守护者等级
    guardian_exp: int    # PRD要求：守护者经验
    stamina: int        # PRD要求：当前体力
    max_stamina: int    # PRD要求：最大体力
    total_play_time: int
    created_at: str
    total_sessions: int = 0
    total_artifacts: int = 0
    cities_unlocked: int = 1  # 默认解锁北京
    thieves_collected: int = 0    # PRD要求：收集的小偷数量
    garbage_collected: int = 0    # PRD要求：收集的垃圾数量




@router.get("/me", response_model=UserInfoResponse)
@track_api_performance("/user/me")
async def get_current_user_info(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取当前用户信息

    返回用户的详细信息，包括：
    - 基础信息（昵称、等级、经验等）
    - 游戏统计（总游戏时长、会话数等）
    """
    
    # TODO: 获取额外的统计信息
    # - 总会话数
    # - 收集的文物数
    # - 解锁的城市数

    return UserInfoResponse(
        user_id=current_user.user_id,
        nickname=current_user.nickname,
        avatar_url=current_user.avatar_url,
        guardian_level=current_user.guardian_level or 1,  # PRD要求：守护者等级
        guardian_exp=current_user.guardian_exp or 0,      # PRD要求：守护者经验
        stamina=current_user.stamina or 120,              # PRD要求：当前体力
        max_stamina=current_user.max_stamina or 120,      # PRD要求：最大体力120
        total_play_time=current_user.total_play_time,
        created_at=current_user.created_at.isoformat(),
        thieves_collected=current_user.thieves_collected or 0,  # PRD要求
        garbage_collected=current_user.garbage_collected or 0   # PRD要求
    )


@router.get("/profile", response_model=UserInfoResponse)
@track_api_performance("/user/profile")
async def get_user_profile(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户信息（别名接口）

    与 /me 接口功能相同，为了兼容前端调用
    """
    
    return UserInfoResponse(
        user_id=current_user.user_id,
        nickname=current_user.nickname,
        avatar_url=current_user.avatar_url,
        guardian_level=current_user.guardian_level or 1,  # PRD要求：守护者等级
        guardian_exp=current_user.guardian_exp or 0,      # PRD要求：守护者经验
        stamina=current_user.stamina or 120,              # PRD要求：当前体力
        max_stamina=current_user.max_stamina or 120,      # PRD要求：最大体力120
        total_play_time=current_user.total_play_time,
        created_at=current_user.created_at.isoformat(),
        thieves_collected=current_user.thieves_collected or 0,  # PRD要求
        garbage_collected=current_user.garbage_collected or 0   # PRD要求
    )


@router.put("/me")
@track_api_performance("/user/update")
async def update_user_info(
    request: UserUpdateRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新用户信息
    
    可更新的字段：
    - nickname: 昵称
    - avatar_url: 头像URL
    """
    updated = False
    
    if request.nickname is not None:
        # TODO: 添加昵称验证（长度、敏感词等）
        current_user.nickname = request.nickname
        updated = True
    
    if request.avatar_url is not None:
        # TODO: 添加头像URL验证
        current_user.avatar_url = request.avatar_url
        updated = True
    
    if updated:
        await db.commit()
        await db.refresh(current_user)
    
    return {
        "success": True,
        "user_info": {
            "user_id": current_user.user_id,
            "nickname": current_user.nickname,
            "avatar_url": current_user.avatar_url
        }
    }


@router.get("/stats")
@track_api_performance("/user/stats")
async def get_user_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户游戏统计信息
    
    包括：
    - 游戏统计（总时长、会话数、胜率等）
    - 收集统计（文物数、城市进度等）
    """
    # TODO: 实现详细的统计查询
    
    return {
        "game_stats": {
            "total_play_time": current_user.total_play_time,
            "total_sessions": 0,  # TODO: 查询实际数据
            "boss_defeated": 0,   # TODO: 查询实际数据
            "hotspots_collected": 0  # TODO: 查询实际数据
        },
        "collection_stats": {
            "total_artifacts": 0,  # TODO: 查询实际数据
            "cities_unlocked": 1,  # TODO: 查询实际数据
            "collection_rate": 0.0  # TODO: 计算收集率
        },
       
    }


@router.delete("/data")
async def delete_user_data(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除用户数据（待实现）
    
    用于满足隐私政策要求，允许用户删除其所有数据
    """
    return {
        "message": "User data deletion is not implemented yet",
        "info": "Please contact support for data deletion requests"
    } 