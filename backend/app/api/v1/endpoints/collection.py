"""
收藏系统API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

from app.core.database import get_db
from app.api.deps import get_current_user
from app.models.user import User
from app.services.collection_service import collection_service
from app.schemas.collection import (
    CityCollectionResponse, CityArtifactsResponse, ArtifactDetailResponse,
    UserResourcesResponse,  # 🚫 PRD合规性清理：移除弹药相关schema - PRD中没有弹药概念
    RadarScanRequest, RadarScanResponse
)
from app.analytics.metrics import track_api_performance

router = APIRouter()


@router.get("/city/{city_id}", response_model=CityCollectionResponse)
@track_api_performance("/collection/city")
async def get_city_collections(
    city_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取城市收藏品列表
    
    - 显示城市所有收藏品
    - 标记用户已收集的项目
    - 显示收集进度和完成率
    """
    result = await collection_service.get_city_collections(db, current_user, city_id)
    
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )
    
    return result


@router.get("/artifacts", response_model=CityArtifactsResponse)
@track_api_performance("/collection/artifacts")
async def get_user_artifacts(
    city_id: Optional[str] = Query(None, description="城市ID，不传则返回所有城市"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户文物列表
    
    - 显示用户收集的文物
    - 可按城市筛选
    - 显示收集进度和稀有度
    """
    result = await collection_service.get_user_artifacts(db, current_user, city_id)
    
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )
    
    return result


@router.get("/artifact/{artifact_id}", response_model=ArtifactDetailResponse)
@track_api_performance("/collection/artifact/detail")
async def get_artifact_detail(
    artifact_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取文物详情
    
    - 显示文物详细信息
    - 包含文物故事和描述
    - 显示用户拥有状态
    """
    result = await collection_service.get_artifact_detail(db, current_user, artifact_id)
    
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )
    
    return result


@router.get("/resources", response_model=UserResourcesResponse)
@track_api_performance("/collection/resources")
async def get_user_resources(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户资源信息
    
    - 显示金币、钻石、弹药数量
    - 显示弹药补充状态
    - 显示最大弹药容量
    """
    result = await collection_service.get_user_resources(db, current_user)
    return result

@router.post("/radar/scan", response_model=RadarScanResponse)
@track_api_performance("/collection/radar/scan")
async def radar_scan(
    request: RadarScanRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    雷达扫描
    
    - 消耗弹药扫描附近热点
    - 显示热点位置和奖励预览
    - 有冷却时间限制
    """
    result = await collection_service.radar_scan(
        db, current_user, request.city_id, request.scene_id, request.scan_range
    )
    
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )
    
    return result
