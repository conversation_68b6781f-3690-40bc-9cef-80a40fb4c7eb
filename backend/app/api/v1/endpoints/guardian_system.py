"""
守护者系统API - 经验值、等级、体力、BOSS战斗
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, Optional
from pydantic import BaseModel

from app.services.guardian_experience_service import guardian_experience_service
from app.services.stamina_management_service import stamina_service
# 🚫 PRD合规性清理：移除boss_battle_service导入 - PRD中没有主动攻击BOSS的机制
from app.api.deps import get_current_user_id

router = APIRouter()


# ==================== 请求模型 ====================

class ExperienceGainRequest(BaseModel):
    experience_amount: int
    source: str
    context: Optional[Dict[str, Any]] = None

class StaminaConsumeRequest(BaseModel):
    action: str
    amount: int = 1
    context: Optional[Dict[str, Any]] = None

class BossAttackRequest(BaseModel):
    session_id: str
    action_type: str
    count: int = 1
    context: Optional[Dict[str, Any]] = None

class BossStartRequest(BaseModel):
    session_id: str
    city_id: str
    level_type: str
    target_counts: Optional[Dict[str, int]] = None


# ==================== 经验值系统API ====================

@router.get("/experience/info")
async def get_user_experience_info(user_id: int = Depends(get_current_user_id)):
    """获取用户经验信息"""
    result = await guardian_experience_service.get_user_experience_info(user_id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取经验信息失败"))
    return {"code": 200, "data": result, "message": "获取成功"}

@router.post("/experience/gain")
async def gain_experience(
    request: ExperienceGainRequest,
    user_id: int = Depends(get_current_user_id)
):
    """获得经验值"""
    result = await guardian_experience_service.gain_experience(
        user_id, request.experience_amount, request.source, request.context
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获得经验失败"))
    return {"code": 200, "data": result, "message": "经验获得成功"}

@router.post("/experience/thief")
async def gain_thief_experience(
    thief_count: int = 1,
    context: Optional[Dict[str, Any]] = None,
    user_id: int = Depends(get_current_user_id)
):
    """抓捕小偷获得经验"""
    result = await guardian_experience_service.gain_thief_experience(user_id, thief_count, context)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获得经验失败"))
    return {"code": 200, "data": result, "message": "抓捕成功"}

@router.post("/experience/rubbish")
async def gain_rubbish_experience(
    rubbish_count: int = 1,
    context: Optional[Dict[str, Any]] = None,
    user_id: int = Depends(get_current_user_id)
):
    """清理垃圾获得经验"""
    result = await guardian_experience_service.gain_rubbish_experience(user_id, rubbish_count, context)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获得经验失败"))
    return {"code": 200, "data": result, "message": "清理成功"}

@router.post("/experience/monument")
async def gain_monument_experience(
    correct_answers: int = 1,
    can_double: bool = True,
    context: Optional[Dict[str, Any]] = None,
    user_id: int = Depends(get_current_user_id)
):
    """古迹问答获得经验"""
    result = await guardian_experience_service.gain_monument_experience(
        user_id, correct_answers, can_double, context
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获得经验失败"))
    return {"code": 200, "data": result, "message": "问答成功"}

@router.post("/experience/passive/collect")
async def collect_passive_income(user_id: int = Depends(get_current_user_id)):
    """收集被动收益"""
    result = await guardian_experience_service.collect_passive_income(user_id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "收集失败"))
    return {"code": 200, "data": result, "message": "收集成功"}


# ==================== 体力值系统API ====================

@router.get("/stamina/info")
async def get_stamina_info(user_id: int = Depends(get_current_user_id)):
    """获取用户体力信息"""
    result = await stamina_service.get_user_stamina_info(user_id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取体力信息失败"))
    return {"code": 200, "data": result, "message": "获取成功"}

@router.post("/stamina/consume")
async def consume_stamina(
    request: StaminaConsumeRequest,
    user_id: int = Depends(get_current_user_id)
):
    """消耗体力"""
    result = await stamina_service.consume_stamina(
        user_id, request.action, request.amount, request.context
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "体力消耗失败"))
    return {"code": 200, "data": result, "message": "消耗成功"}

@router.post("/stamina/ad_recover")
async def watch_stamina_ad(user_id: int = Depends(get_current_user_id)):
    """观看广告恢复体力"""
    result = await stamina_service.watch_stamina_ad(user_id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "广告恢复失败"))
    return {"code": 200, "data": result, "message": "恢复成功"}

@router.post("/stamina/potion")
async def use_stamina_potion(user_id: int = Depends(get_current_user_id)):
    """使用体力药水"""
    result = await stamina_service.use_stamina_potion(user_id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "使用失败"))
    return {"code": 200, "data": result, "message": "使用成功"}

@router.post("/stamina/rebel_thief")
async def rebel_thief_recovery(user_id: int = Depends(get_current_user_id)):
    """叛变小偷体力恢复"""
    result = await stamina_service.rebel_thief_recovery(user_id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "恢复失败"))
    return {"code": 200, "data": result, "message": "恢复成功"}


# ==================== BOSS战斗系统API ====================

@router.post("/boss/start")
async def start_boss_battle(
    request: BossStartRequest,
    user_id: int = Depends(get_current_user_id)
):
    """开始BOSS战斗"""
    result = await boss_battle_service.start_boss_battle(
        user_id, request.session_id, request.city_id, request.level_type, request.target_counts
    )
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "开始战斗失败"))
    return {"code": 200, "data": result, "message": "战斗开始"}

# 🚫 PRD合规性清理：移除BOSS攻击端点
# PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少

@router.get("/boss/status/{session_id}")
async def get_boss_status(
    session_id: str,
    user_id: int = Depends(get_current_user_id)
):
    """获取BOSS状态"""
    result = await boss_battle_service.get_boss_status(user_id, session_id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取状态失败"))
    return {"code": 200, "data": result, "message": "获取成功"}

@router.get("/boss/dialogue/{session_id}")
async def get_boss_dialogue(
    session_id: str,
    user_id: int = Depends(get_current_user_id)
):
    """获取BOSS对话"""
    result = await boss_battle_service.get_boss_dialogue(user_id, session_id)
    if not result.get("success"):
        raise HTTPException(status_code=400, detail=result.get("error", "获取对话失败"))
    return {"code": 200, "data": result, "message": "获取成功"}


# ==================== 统一游戏行为API ====================

@router.post("/action/catch_thief")
async def catch_thief_action(
    count: int = 1,
    session_id: str = None,
    city_id: str = None,
    user_id: int = Depends(get_current_user_id)
):
    """抓捕小偷综合行为"""
    try:
        # 1. 消耗体力
        stamina_result = await stamina_service.consume_stamina(
            user_id, "catch_thief", count, {"session_id": session_id, "city_id": city_id}
        )
        if not stamina_result.get("success"):
            return {"code": 400, "data": stamina_result, "message": stamina_result.get("error")}
        
        # 2. 获得经验
        exp_result = await guardian_experience_service.gain_thief_experience(
            user_id, count, {"session_id": session_id, "city_id": city_id}
        )
        
        # 3. 更新BOSS血量（如果有session_id）
        boss_result = None
        if session_id:
            boss_result = await boss_battle_service.update_boss_health(
                user_id, session_id, "thief", count, {"city_id": city_id}
            )
        
        return {
            "code": 200,
            "data": {
                "stamina_result": stamina_result,
                "experience_result": exp_result,
                "boss_result": boss_result
            },
            "message": f"成功抓捕{count}个小偷"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"抓捕小偷失败: {str(e)}")

@router.post("/action/clean_rubbish")
async def clean_rubbish_action(
    count: int = 1,
    session_id: str = None,
    city_id: str = None,
    user_id: int = Depends(get_current_user_id)
):
    """清理垃圾综合行为"""
    try:
        # 1. 消耗体力
        stamina_result = await stamina_service.consume_stamina(
            user_id, "clean_rubbish", count, {"session_id": session_id, "city_id": city_id}
        )
        if not stamina_result.get("success"):
            return {"code": 400, "data": stamina_result, "message": stamina_result.get("error")}
        
        # 2. 获得经验
        exp_result = await guardian_experience_service.gain_rubbish_experience(
            user_id, count, {"session_id": session_id, "city_id": city_id}
        )
        
        # 3. 更新BOSS血量（如果有session_id）
        boss_result = None
        if session_id:
            boss_result = await boss_battle_service.update_boss_health(
                user_id, session_id, "rubbish", count, {"city_id": city_id}
            )
        
        return {
            "code": 200,
            "data": {
                "stamina_result": stamina_result,
                "experience_result": exp_result,
                "boss_result": boss_result
            },
            "message": f"成功清理{count}个垃圾"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理垃圾失败: {str(e)}")

@router.post("/action/monument_quiz")
async def monument_quiz_action(
    correct_answers: int = 1,
    session_id: str = None,
    city_id: str = None,
    monument_id: str = None,
    user_id: int = Depends(get_current_user_id)
):
    """古迹问答综合行为"""
    try:
        # 1. 消耗体力
        stamina_result = await stamina_service.consume_stamina(
            user_id, "monument_quiz", 1, {"session_id": session_id, "city_id": city_id}
        )
        if not stamina_result.get("success"):
            return {"code": 400, "data": stamina_result, "message": stamina_result.get("error")}
        
        # 2. 获得经验
        exp_result = await guardian_experience_service.gain_monument_experience(
            user_id, correct_answers, True, {"session_id": session_id, "city_id": city_id, "monument_id": monument_id}
        )
        
        # 3. 更新BOSS血量（如果有session_id）
        boss_result = None
        if session_id:
            boss_result = await boss_battle_service.update_boss_health(
                user_id, session_id, "monument", correct_answers, {"city_id": city_id, "monument_id": monument_id}
            )
        
        return {
            "code": 200,
            "data": {
                "stamina_result": stamina_result,
                "experience_result": exp_result,
                "boss_result": boss_result
            },
            "message": f"问答完成，正确{correct_answers}题"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"古迹问答失败: {str(e)}")