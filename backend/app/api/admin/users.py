"""
管理系统用户管理API端点
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, desc, select, and_, or_
from pydantic import BaseModel
from datetime import datetime, timedelta
import logging

from app.core.database import get_db
from app.models.user import User
from app.models.game import GameSession
from app.services.admin_auth_service import admin_auth_service

router = APIRouter()
logger = logging.getLogger(__name__)


# 🚫 PRD合规性清理：移除货币更新请求，PRD中没有金币钻石概念


@router.get("/stats", summary="获取用户统计数据")
async def get_user_stats(
    db: AsyncSession = Depends(get_db),
    current_admin = Depends(admin_auth_service.get_current_admin)
):
    """
    获取用户统计数据
    """
    try:
        # 总用户数
        total_users_result = await db.execute(
            select(func.count(User.id))
        )
        total_users = total_users_result.scalar() or 0
        
        # 今日新增用户
        today = datetime.now().date()
        today_users_result = await db.execute(
            select(func.count(User.id)).where(
                func.date(User.created_at) == today
            )
        )
        today_users = today_users_result.scalar() or 0
        
        # 本周新增用户
        week_ago = datetime.now() - timedelta(days=7)
        week_users_result = await db.execute(
            select(func.count(User.id)).where(
                User.created_at >= week_ago
            )
        )
        week_users = week_users_result.scalar() or 0
        
        # 活跃用户（7天内有游戏会话）
        active_users_result = await db.execute(
            select(func.count(func.distinct(GameSession.user_id))).where(
                GameSession.created_at >= week_ago
            )
        )
        active_users = active_users_result.scalar() or 0
        
        # 按登录类型统计
        guest_users_result = await db.execute(
            select(func.count(User.id)).where(User.login_type == 'guest')
        )
        guest_users = guest_users_result.scalar() or 0
        
        google_users_result = await db.execute(
            select(func.count(User.id)).where(User.login_type == 'google')
        )
        google_users = google_users_result.scalar() or 0
        
        crazygames_users_result = await db.execute(
            select(func.count(User.id)).where(User.login_type == 'crazygames')
        )
        crazygames_users = crazygames_users_result.scalar() or 0
        
        return {
            "total_users": total_users,
            "today_users": today_users,
            "week_users": week_users,
            "active_users": active_users,
            "user_types": {
                "guest": guest_users,
                "google": google_users,
                "crazygames": crazygames_users
            },
            "activity_rate": round((active_users / max(total_users, 1)) * 100, 2)
        }
        
    except Exception as e:
        logger.error(f"获取用户统计数据失败: {e}")
        raise HTTPException(
            status_code=500,
            detail="获取用户统计数据失败"
        )


@router.get("/list", summary="获取用户列表")
async def get_user_list(
    page: int = Query(1, description="页码"),
    page_size: int = Query(20, description="每页数量"),
    search: str = Query("", description="搜索关键词"),
    login_type: str = Query("", description="登录类型筛选"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向"),
    db: AsyncSession = Depends(get_db),
    current_admin = Depends(admin_auth_service.get_current_admin)
):
    """
    获取用户列表
    """
    try:
        # 构建查询
        query = select(User)
        
        # 搜索条件
        if search:
            search_condition = or_(
                User.nickname.ilike(f"%{search}%"),
                User.email.ilike(f"%{search}%"),
                User.id.ilike(f"%{search}%")
            )
            query = query.where(search_condition)
        
        # 登录类型筛选
        if login_type:
            query = query.where(User.login_type == login_type)
        
        # 排序
        if sort_by == "created_at":
            order_field = User.created_at
        elif sort_by == "level":
            order_field = User.level
        else:
            order_field = User.created_at
        
        if sort_order == "desc":
            query = query.order_by(desc(order_field))
        else:
            query = query.order_by(order_field)
        
        # 分页
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)
        
        # 执行查询
        result = await db.execute(query)
        users_data = result.scalars().all()

        # 获取总数
        count_query = select(func.count(User.id))
        if search:
            count_query = count_query.where(search_condition)
        if login_type:
            count_query = count_query.where(User.login_type == login_type)

        total_result = await db.execute(count_query)
        total = total_result.scalar() or 0

        # 格式化数据
        users = []
        for user in users_data:
            user_info = {
                "id": user.user_id,
                "nickname": user.nickname,
                "email": user.email,
                "login_type": user.login_type,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "last_login": user.last_login.isoformat() if user.last_login else None,
                "is_active": user.is_active,
                "level": user.level,
                "experience": user.exp
            }
            users.append(user_info)
        
        return {
            "users": users,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        }
        
    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail="获取用户列表失败"
        )


@router.get("/{user_id}", summary="获取用户详情")
async def get_user_detail(
    user_id: str,
    db: AsyncSession = Depends(get_db),
    current_admin = Depends(admin_auth_service.get_current_admin)
):
    """
    获取用户详情
    """
    try:
        # 查询用户基本信息
        user_result = await db.execute(
            select(User).where(User.user_id == user_id)
        )
        user = user_result.scalar_one_or_none()

        if not user:
            raise HTTPException(
                status_code=404,
                detail="用户不存在"
            )
        
        # 查询用户游戏会话统计
        sessions_result = await db.execute(
            select(func.count(GameSession.id)).where(GameSession.user_id == user_id)
        )
        total_sessions = sessions_result.scalar() or 0
        
        # 最近7天的会话数
        week_ago = datetime.now() - timedelta(days=7)
        recent_sessions_result = await db.execute(
            select(func.count(GameSession.id)).where(
                and_(
                    GameSession.user_id == user_id,
                    GameSession.created_at >= week_ago
                )
            )
        )
        recent_sessions = recent_sessions_result.scalar() or 0
        
        # 最后游戏时间
        last_session_result = await db.execute(
            select(GameSession.created_at)
            .where(GameSession.user_id == user_id)
            .order_by(desc(GameSession.created_at))
            .limit(1)
        )
        last_session = last_session_result.scalar_one_or_none()
        
        return {
            "user": {
                "id": user.user_id,
                "nickname": user.nickname,
                "email": user.email,
                "login_type": user.login_type,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "last_login": user.last_login.isoformat() if user.last_login else None,
                "is_active": user.is_active,
                "device_id": user.device_id,
                "device_info": user.device_info
            },
            "resources": {
                "level": user.level,
                "experience": user.exp,
                "ammo": user.ammo_count,
                "max_ammo": 100
            },
            "game_stats": {
                "total_sessions": total_sessions,
                "recent_sessions": recent_sessions,
                "last_session": last_session.isoformat() if last_session else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户详情失败: {e}")
        raise HTTPException(
            status_code=500,
            detail="获取用户详情失败"
        )


@router.post("/currency/update", summary="更新用户货币")
async def update_user_currency(
    request: UpdateCurrencyRequest,
    db: AsyncSession = Depends(get_db),
    current_admin = Depends(admin_auth_service.get_current_admin)
):
    """
    更新用户货币
    """
    try:
        # 查询用户
        user_result = await db.execute(
            select(User).where(User.user_id == request.user_id)
        )
        user = user_result.scalar_one_or_none()

        if not user:
            raise HTTPException(
                status_code=404,
                detail="用户不存在"
            )

        # 🚫 PRD合规性清理：移除货币更新逻辑，PRD中没有金币钻石概念
        return {"message": "货币系统已移除，请使用经验值系统"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户货币失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail="更新用户货币失败"
        )
