# 城市全景寻物游戏后端 Python

基于 FastAPI + MySQL + Redis 的高性能游戏后端架构。

## 🏗️ 架构概览

### 技术栈
- **Web框架**: FastAPI + Uvicorn (异步高性能)
- **数据库**: MySQL (主数据)
- **ORM**: SQLAlchemy 2.0 + Asyncpg
- **缓存**: Redis (会话 + 排行榜 + 热点数据)
- **实时通信**: WebSocket (FastAPI原生支持)
- **消息队列**: Redis Pub/Sub + Celery
- **配置管理**: Pydantic Settings + YAML配置文件

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │  WebSocket      │    │  Config Manager │
│   (FastAPI)     │    │  Server         │    │  (YAML+Pydantic)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Game Service   │    │  Session        │    │  Hotspot        │
│  (FastAPI)      │    │  Service        │    │  Service        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  User Service   │    │  图鉴/广告      │    │  Artifact       │
│                 │    │  Service        │    │  Service        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│  MySQL          │    │     Redis       │
│  (主数据+配置)    │    │   (缓存/会话)    │
└─────────────────┘    └─────────────────┘
```

## 🎮 核心功能

### 1. 用户系统
- 用户注册/登录
- 激励广告系统
- 金币，钻石系统
- VIP系统
- 登录日志统计

### 2. 游戏核心
- **游戏会话管理**: 用户场景记录，动态生成热点，实时追踪进度
- **热点系统**: 基于概率生成不同类型热点 (垃圾、小偷、宝箱等)
- **文物收集**: 稀有度权重随机，去重逻辑，图鉴完成度追踪
- **向垃圾大王发射打炮，消灭BOSS**: 支持金币/钻石升级大炮，进度累积，击杀BOSS奖励图鉴

### 3. 商业化
- **广告系统**: 每日限制次数，奖励验证，金币/钻石奖励
- **通过分享链接拉新**: 开启新城市

### 4. 实时通信
- **WebSocket推送**: 游戏进度、文物发现、排名
- **心跳机制**: 保持连接活跃
- **Redis Pub/Sub**: 支持集群部署的消息推送

## 📁 目录结构

```
backend/
├── app/
│   ├── main.py                 # 应用入口
│   ├── core/                   # 核心配置
│   │   ├── config.py          # 配置管理
│   │   ├── database.py        # MySQL连接
│   │   ├── redis_client.py    # Redis连接管理

│   ├── models/                 # 数据模型
│   │   ├── user.py            # 用户相关模型
│   │   └── game.py            # 游戏相关模型
│   ├── services/               # 业务服务
│   │   ├── user_service.py    # 用户服务
│   │   └── game_service.py    # 游戏服务
│   ├── api/                    # API路由
│   │   ├── v1/
│   │   │   ├── api.py         # 路由汇总
│   │   │   └── endpoints/     # 各模块端点
│   │   └── websocket.py       # WebSocket路由
├── config/                     # 配置文件
│   ├── scenes.yaml            # 场景配置
│   └── artifacts.yaml         # 文物配置
├── requirements.txt            # 依赖
└── README.md                  # 说明文档
```

## 🚀 快速开始

### 1. 环境准备

#### 系统要求
- Python 3.10+
- MySQL 8.0+
- Redis 6.0+

#### Ubuntu 系统安装

**安装 MySQL:**
```bash
sudo apt update
sudo apt install mysql-server
sudo service mysql start
sudo systemctl enable mysql  # 设置开机自启

# 解决权限问题（如果需要）
sudo chmod 777 /var/run/mysqld/mysqld.sock
sudo usermod -a -G mysql $USER

# 登录并创建数据库
mysql -u root -p
CREATE DATABASE universe_vr_game CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

**安装 Redis:**
```bash
sudo apt update
sudo apt install redis-server
sudo service redis-server start
sudo systemctl enable redis-server  # 设置开机自启

# 测试Redis连接
redis-cli ping  # 应该返回 PONG
```

#### 安装Python依赖和配置环境变量

```bash
# 1. 安装Python依赖
pip install -r requirements.txt

# 2. 配置环境变量
cp .env.example .env

# 3. 编辑 .env 文件，配置数据库连接等
nano .env  # 或使用其他编辑器
```

#### 重要的环境变量配置

编辑 `.env` 文件，至少需要配置以下关键项：

```bash
# 数据库连接 - 修改为你的实际密码
DATABASE_URL=mysql+aiomysql://root:your_actual_password@localhost:3306/universe_vr_game

# 安全密钥 - 生产环境必须更换
SECRET_KEY=your-super-secure-secret-key-here
JWT_SECRET_KEY=your-super-secure-jwt-secret-key-here

# 运行环境
ENVIRONMENT=development  # 或 production

# Redis连接（如果有密码）
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your_redis_password_if_any
```

### 2. 数据库初始化
```bash
# MySQL 数据库迁移
alembic upgrade head

# 配置数据导入
python scripts/init_config_data.py
```

### 3. 启动服务
```bash
# 开发模式
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 生产模式
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### 4. 启动后台任务
```bash
# 启动 Celery 工作进程
celery -A app.tasks.celery_worker worker --loglevel=info

# 启动 Celery 定时任务
celery -A app.tasks.celery_worker beat --loglevel=info
```

## 📡 API 文档

启动服务后访问：
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`



## 🔧 配置说明

### 环境变量配置详解

项目使用 `.env` 文件管理环境变量，支持开发、测试、生产等不同环境的配置。

#### 配置文件结构
- `.env.example` - 环境变量模板文件（包含所有配置项说明）
- `.env` - 实际环境变量文件（不提交到版本控制）

#### 核心配置项

**基础配置**
```bash
DEBUG=true                    # 调试模式
SECRET_KEY=your-secret-key    # 应用密钥（生产环境必须更换）
ENVIRONMENT=development       # 运行环境
PROJECT_NAME=城市全景寻物游戏  # 项目名称
```

**数据库配置**
```bash
DATABASE_URL=mysql+aiomysql://root:password@localhost:3306/universe_vr_game
DATABASE_POOL_SIZE=10         # 连接池大小
DATABASE_MAX_OVERFLOW=20      # 最大溢出连接数
DATABASE_ECHO=false           # 是否输出SQL日志
```

**Redis配置**
```bash
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=               # Redis密码（可选）
REDIS_POOL_SIZE=10           # 连接池大小
REDIS_EXPIRE_TIME=3600       # 默认过期时间
```

**JWT认证配置**
```bash
JWT_SECRET_KEY=your-jwt-secret-key    # JWT密钥（生产环境必须更换）
JWT_ALGORITHM=HS256                   # JWT算法
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30    # 访问令牌过期时间
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7       # 刷新令牌过期时间
```

**游戏配置**
```bash
MAX_DAILY_ADS=5              # 每日广告观看上限
MAX_SESSION_DURATION=3600    # 最大会话时长
HOTSPOT_CACHE_TTL=300        # 热点缓存时间
```

**安全配置**
```bash
RATE_LIMIT_PER_MINUTE=60     # 每分钟请求限制
MAX_LOGIN_ATTEMPTS=5         # 最大登录尝试次数
CORS_ORIGINS=["*"]           # CORS允许的源
```

#### 环境特定配置

**开发环境 (.env)**
```bash
DEBUG=true
ENVIRONMENT=development
DATABASE_ECHO=true           # 开发环境可以开启SQL日志
LOG_LEVEL=DEBUG             # 详细日志
CORS_ORIGINS=["http://localhost:3000","*"]
```

**生产环境建议**
```bash
DEBUG=false
ENVIRONMENT=production
SECRET_KEY=your-super-secure-random-secret-key
JWT_SECRET_KEY=your-super-secure-jwt-secret-key
DATABASE_ECHO=false
LOG_LEVEL=WARNING
CORS_ORIGINS=["https://yourdomain.com"]
RATE_LIMIT_PER_MINUTE=30
```

### 场景配置 (config/scenes.yaml)
```yaml
scenes:
  city_scene_1:
    scene_id: "scene_level_1"
    name: "寻物游戏场景 - 等级 1"
    panorama_url: "/panos/scene1.tiles"
    hotspot_types:
      - type: "garbage"
        probability: 0.8
        rewards:
          gold: 10
          exp: 5
```

### 文物配置 (config/artifacts.yaml)
```yaml
artifacts:
  - artifact_id: "bronze_ding"
    name: "青铜鼎"
    rarity: "legendary"
    weight: 1
    category: "青铜器"
```

## ⚙️ 配置管理

### 游戏配置热更新

游戏配置支持运行时更新，无需重启服务即可生效。

#### 1. 配置文件结构 (config/game_config.yaml)

```yaml
# 任务系统配置
tasks:
  daily:
    # 任务列表
    tasks:
      - id: "daily_login"
        name: "每日登录"
        description: "登录游戏"
        target: 1
        type: "login"
        rewards:
          exp: 20  # 经验值奖励
      - id: "collect_hotspots"
        name: "收集热点"
        description: "收集5个热点目标"
        target: 5
        type: "collect"
        rewards:
          exp: 20
    
    # 经验值系统
    experience:
      total_exp: 100          # 基础总经验值
      double_exp_total: 200   # 支持双倍经验值的总经验值
      reset_time: "00:00"     # 每日重置时间
    
    # 奖励等级配置
    reward_levels:
      - level: 1
        required_exp: 20      # 解锁所需经验值
        rewards:
          gold: 50           # 奖励金币
        icon: "/img/page_icons/reward_level_1.png"
      - level: 5
        required_exp: 100
        rewards:
          diamond: 5         # 奖励钻石
        icon: "/img/page_icons/reward_level_5.png"

# 转盘抽奖配置
lottery_wheel:
  daily_limit: 3
  prizes:
    - icon: "💰"
      text: "10金币"
      type: "coins"
      amount: 10
      color: "#FFD700"
      weight: 30           # 权重越高，抽中概率越大
```

#### 2. 配置更改的3种方式

**方法1：重启服务（推荐生产环境）**
```bash
# 修改 game_config.yaml 后重启服务
pkill -f uvicorn
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**方法2：热更新API（开发环境）**
```bash
# 调用配置重载API（需要认证token）
curl -X POST "http://localhost:8000/api/v1/task/config/reload" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 返回示例
{
  "success": true,
  "message": "配置重新加载成功",
  "changes": {
    "tasks": {
      "modified": [{
        "task_id": "daily_login",
        "old": {"rewards": {"exp": 20}},
        "new": {"rewards": {"exp": 25}}
      }]
    },
    "reward_levels": {
      "modified": []
    }
  },
  "timestamp": "2025-07-07T14:32:29.123456"
}
```

**方法3：前端管理界面（计划中）**
- 可视化配置编辑器
- 实时预览配置效果
- 配置变更历史记录

#### 3. 配置更改的影响范围

| 配置类型 | 影响范围 | 对已有用户影响 | 建议更改方式 |
|---------|---------|---------------|------------|
| **任务经验值奖励** | 全局所有用户 | ✅ 未领取任务按新配置 | 安全，可实时更新 |
| **任务目标值** | 全局所有用户 | ⚠️ 可能导致进度异常 | 谨慎，建议测试后更新 |
| **奖励等级配置** | 全局所有用户 | ✅ 自动重新计算解锁状态 | 安全，降低门槛用户受益 |
| **转盘奖励** | 全局所有用户 | ✅ 无历史数据影响 | 安全，立即生效 |

#### 4. 配置更改最佳实践

**安全的配置更改示例：**
```yaml
# ✅ 推荐：降低经验值要求，提高奖励
reward_levels:
  - level: 1
    required_exp: 15      # 从20降到15，让更多用户解锁
    rewards:
      gold: 80           # 从50提高到80，增加用户满意度

# ✅ 推荐：添加新的奖励等级
  - level: 11
    required_exp: 220
    rewards:

# ✅ 推荐：调整转盘奖励权重
lottery_wheel:
  prizes:
    - text: "50"
      weight: 20         # 提高大奖权重
```

**需要谨慎的配置更改：**
```yaml
# ⚠️ 谨慎：提高经验值要求可能让已解锁用户重新锁定
reward_levels:
  - level: 1
    required_exp: 30     # 从20提高到30
    # 如果用户只有25经验值，可能会重新锁定

# ⚠️ 谨慎：大幅修改任务目标值
tasks:
  - id: "collect_hotspots"
    target: 20          # 从5提高到20，影响用户体验
```

#### 5. 配置更改流程

1. **备份当前配置**
   ```bash
   cp config/game_config.yaml config/game_config.yaml.backup.$(date +%Y%m%d_%H%M%S)
   ```

2. **修改配置文件**
   - 使用文本编辑器修改 `config/game_config.yaml`
   - 确保YAML语法正确

3. **应用配置更改**
   ```bash
   # 开发环境：使用热更新
   curl -X POST "http://localhost:8000/api/v1/task/config/reload" \
     -H "Authorization: Bearer $TOKEN"
   
   # 生产环境：重启服务
   systemctl restart city-game-backend
   ```

4. **验证配置生效**
   ```bash
   # 检查任务列表API
   curl -X GET "http://localhost:8000/api/v1/task/daily/experience" \
     -H "Authorization: Bearer $TOKEN"
   
   # 检查服务日志
   journalctl -u city-game-backend -f | grep "Task configuration reloaded"
   ```

#### 6. 常见配置场景

**场景1：新增节日活动**
```yaml
# 双倍经验值活动
tasks:
  daily:
    experience:
      total_exp: 100
      double_exp_total: 200  # 支持双倍经验值
    
    reward_levels:
      # 添加限时奖励等级
      - level: 6
        required_exp: 120
        rewards:
          special_item: "节日礼盒"
```

**场景2：调整游戏经济平衡**
```yaml
# 降低获取门槛，提高用户留存
reward_levels:
  - level: 1
    required_exp: 10      # 从20降到10
    rewards:
      gold: 100          # 从50提高到100
```

**场景3：优化转盘奖励**
```yaml
# 增加稀有奖励的出现概率
lottery_wheel:
  prizes:
    - text: "100钻石"
      type: "diamonds"
      amount: 100
      weight: 5          # 从1提高到5
```

## 📊 数据流转

### 1. 金币钻石系统


### 2. 游戏会话
- **会话创建**: MySQL记录 + Redis热数据
- **实时更新**: Redis存储进度，WebSocket推送
- **结算完成**: 数据库持久化，统计更新

### 3. 文物系统
- **掉落计算**: 权重随机算法
- **去重处理**: 数据库唯一性约束 + 计数
- **图鉴更新**: 实时推送新发现

## 🔒 安全考虑

### 1. 数据验证
- Pydantic模型验证
- SQL注入防护 (SQLAlchemy ORM)
- XSS防护
- 请求频率限制

### 2. 业务安全
- 击杀垃圾大王保证掉落文物图鉴
- 金币钻石消耗跟掉落图鉴相关性
- 会话权限检查
- 奖励合法性验证
- 防刷机制

### 3. 支付安全
- 订单签名验证
- 重复支付检测
- 退款处理
- 异常监控

## 📈 性能优化

### 1. 缓存策略
- **Redis多库分离**: session/cache分库存储
- **热点数据缓存**: 用户信息、游戏进度
- **查询优化**: 数据库索引，慢查询监控

### 2. 异步处理
- **FastAPI异步**: 全异步请求处理
- **数据库连接池**: 高并发连接管理
- **Celery任务队列**: 耗时操作异步化

### 3. 实时通信
- **WebSocket连接管理**: 自动重连，心跳检测
- **消息批量推送**: Redis Pub/Sub集群支持
- **连接清理**: 异常连接自动清理

## 🧪 测试

```bash
# 运行测试
pytest

# 测试覆盖率
pytest --cov=app

# 性能测试
locust -f tests/performance/locustfile.py
```

## 📝 部署

### Docker 部署
```bash
# 构建镜像
docker build -t city-game-backend .

# 运行容器
docker-compose up -d
```

### 生产环境
```bash
# 使用 Gunicorn + Nginx
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker

# 监控和日志
systemctl start city-game-backend
journalctl -u city-game-backend -f
```

《像素寻宝》MVP版本页面功能与布局详细设计
1. 登录页 (Login Screen)
核心功能

作为游戏的入口，提供最快捷的进入方式。

展示服务条款和隐私政策，符合规范。

布局与UI元素

+--------------------------------------+
|                                      |
|            [游戏LOGO]                |
|         《像素寻宝：城市守护者》         |
|                                      |
|                                      |
|          [✅ 同意服务条款与隐私政策]     |
|                                      |
|         [🎮 游客登录/开始游戏]         |
|                                      |
+--------------------------------------+

中心: 醒目的游戏LOGO和名称。

中部: 服务条款和隐私政策的勾选框和链接。

底部: 一个巨大、吸引人点击的“游客登录/开始游戏”按钮。

2. 新手引导页 (Newbie Guide Screen)
核心功能

用最直观、有趣的方式向新玩家介绍核心玩法。

确保玩家在开始游戏前，对要做什么有基本了解。

布局与UI元素
这是一个四页可左右滑动的漫画教程。

通用元素:

右上角: [跳过] 按钮。

底部: 页码指示器 (如 ● ○ ○ ○)。

页面内容:

第1页：故事背景

画面: 巨大的卡通风格垃圾大王俯瞰着美丽的城市。

文字: “宇宙垃圾大王入侵！快成为城市守护者，拯救世界！”

第2页：核心玩法 - 寻找

画面: 一根巨大的手指正在放大和拖动城市地图，一个带放大镜的图标高亮显示。

文字: “城市里藏着许多淘气的小偷！用你的手指[放大]和[拖动]来寻找他们吧！”

第3页：核心玩法 - 战斗

画面: 特写底部的大炮UI，有小偷和垃圾军团飞入大炮的动画轨迹。

文字: “收集小偷和垃圾军团装填[大炮]，然后[瞄准]天上的大王，开火！”

第4页：成长目标

画面: 特写发光的“升级”按钮。

文字: “别忘了用收集到的金币[升级]你的大炮！准备好了吗？”

按钮: 一个巨大的 [开始冒险！] 按钮，取代“下一页”按钮。

3. 主游戏界面 (Main Game Screen)
这是玩家停留时间最长的核心界面，设计上需要做到信息清晰、主次分明，且不干扰核心的地图探索体验。

核心功能
探索地图：通过双指缩放、单指拖拽来浏览千亿像素的城市地图。

发现目标：在地图上寻找并点击“每日小偷”和“垃圾军团”。

收集资源：成功点击目标后，播放特效，资源自动飞向底部UI并更新计数。

攻击BOSS：当弹药足够时，点击大炮向远处的BOSS发射攻击，随机掉落图鉴。

使用道具：点击道具按钮，激活辅助功能。

访问各系统：通过界面上的按钮，打开其他功能弹窗。

切换纯净观景模式：一键隐藏所有游戏UI。

访问官方社群：点击社交图标，跳转至官方社群页面。

布局与UI元素
+-----------------------------------------------------------------+
| [💰5928] [💎1028]                      [👁️ 观景] [☰ 菜单] |
+-----------------------------------------------------------------+
| [📜 任务❗]                                         [📡 雷达]    |
| [🌐 社群 30+]                                      [🔍 放大镜]  |
|                                                                 |
|                      <--- 千亿像素城市地图 --->                      |
|                                                                 |
|                                                     [垃圾大王]  |
|                                                                 |
|         [✨听他们聊天]                                          |
+-----------------------------------------------------------------+
| [小偷: 25/10]      [🔫 发射大炮]      [垃圾军团: 62/10]      |
|                    [✨ 升级 ❗]                             |
+-----------------------------------------------------------------+

顶部/侧边: 货币信息、菜单、任务、社交、道具等按钮。

中心: 游戏地图、目标物、BOSS、以及AI对话气泡的显示区域。

底部: 资源计数器、大炮发射和升级按钮，以及AI对话触发按钮。

4. 大炮升级弹窗 (Cannon Upgrade Modal)
核心功能: 消耗资源，提升大炮的核心战斗属性。
布局与UI元素:

+-------------------------------------------+
|               大炮强化中心                |
|-------------------------------------------|
| 攻击力 Lv.5   [+10伤害]  [升级 消耗: 500💰] |
| 暴击率 Lv.3   [+1%暴击]  [升级 消耗: 300💰] |
| 冷却 Lv.2   [-0.5s冷却] [升级 消耗: 100💎] |
|                      [X 关闭]             |
+-------------------------------------------+

列表形式：清晰展示每个可升级项、当前等级、效果和消耗。

按钮状态: 资源不足时，升级按钮置灰。

5. 任务系统弹窗 (Task System Modal)
核心功能: 引导玩家每日行为，提供常规奖励。
布局与UI元素:

+-----------------------------------------------+
|     [ 每日任务 ]   |    [ 登录奖励 ]           |
|-----------------------------------------------|
| [ ] 抓捕小偷头目 (0/1) [✨获取情报] 或 [ 领取 ] |
| [ ] 抓捕每日小偷 (7/15)   [ 前往 ] 或 [ 领取 ] |
| ...                                           |
+-----------------------------------------------+

页签设计: 分为“每日任务”和“登录奖励”。

AI情报按钮: 在“抓捕头目”任务旁，提供按钮以获取AI生成的线索。

6. 菜单弹窗 (Menu Modal)
核心功能: 作为二级功能的集合入口。
布局与UI元素: 简单的垂直按钮列表。

+----------------------+
|        游戏菜单        |
|----------------------|
|      [ 城市图鉴 ]      |
|      [ 排行榜 ]        |
|      [ 音乐: 开/关 ]    |
| ...                  |
+----------------------+

7. 图鉴收藏家排行榜 (Leaderboard Screen)
核心功能: 展示玩家的长期收集成就，提供社交炫耀和追赶目标。
布局与UI元素:

+-------------------------------------------------+
|            🏆 图鉴收藏家排行榜 🏆             |
|-------------------------------------------------|
| 排名 |   玩家名称 (可点击)   | 图鉴进度 | 游戏时间 |
|------|-------------------|----------|----------|
|  🥇  |     [龙的传人]      | 256/320  |  350 h   |
|  🥈  |     [城市猎人]      | 248/320  |  410 h   |
| ...  |       ...         |   ...    |   ...    |
| 158  |       [你]        | 123/320  |  150 h   |
+-------------------------------------------------+

表格布局: 清晰展示排名、玩家、图鉴进度和游戏时间。

可交互名称: 点击玩家名称，弹出“玩家详情”窗口。

8. 玩家详情弹窗 (Player Profile Modal)
核心功能: 深入展示单个玩家的图鉴收集细节。
布局与UI元素:

+--------------------------------------+
|             龙的传人 的守护之路          |
|--------------------------------------|
|       已守护城市: 16/20              |
|--------------------------------------|
| 北京     [■■■■■■■■■■] 16/16        |
| 纽约     [■■■■■■■■■■] 16/16        |
| 伦敦     [■■■■■■■■□□] 8/16         |
| ...                                  |
+--------------------------------------+

总览: 显示守护城市总数。

进度条列表: 详细列出每个城市的收集进度。

9. 简版图鉴系统弹窗 (Collection System Modal)
核心功能: 集中展示玩家获得的图鉴，并提供AI互动。
布局与UI元素:

+-------------------------------------------------+
|                  北京图鉴 (5/16)                  |
|-------------------------------------------------|
|  [卡片1 ✔]  [卡片2 ✔]  [卡片3 ?]  [卡片4 ✔]    |
|  ...                                            |
|-------------------------------------------------|
|     < 点击卡片查看详情 >                         |
+-------------------------------------------------+

卡片网格: 已获得的高亮，未获得的置灰。

详情与AI故事: 点击已获得的卡片，可进入详情页并触发AI生成的小偷日记。