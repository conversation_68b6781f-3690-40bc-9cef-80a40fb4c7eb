# ========================================
# 城市全景寻物游戏 - 环境变量配置模板
# ========================================
# 复制此文件为 .env 并根据实际环境修改配置值
# 注意：.env 文件包含敏感信息，不应提交到版本控制系统

# ========================================
# 基础配置
# ========================================
# 调试模式 (development: true, production: false)
DEBUG=true

# 应用密钥 (生产环境必须更换为随机字符串)
SECRET_KEY=your-secret-key-change-this-in-production

# 运行环境 (development/staging/production)
ENVIRONMENT=development

# API版本前缀
API_V1_STR=/api/v1

# 项目名称
PROJECT_NAME=城市全景寻物游戏

# ========================================
# 数据库配置
# ========================================
# MySQL数据库连接URL
# 格式: mysql+aiomysql://用户名:密码@主机:端口/数据库名
DATABASE_URL=mysql+aiomysql://root:your_password@localhost:3306/universe_vr_game

# 数据库连接池配置
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# 是否输出SQL日志 (开发环境: true, 生产环境: false)
DATABASE_ECHO=false

# ========================================
# Redis配置
# ========================================
# Redis连接URL
REDIS_URL=redis://localhost:6379/0

# Redis密码 (如果设置了密码)
REDIS_PASSWORD=

# Redis连接池大小
REDIS_POOL_SIZE=10

# Redis响应解码
REDIS_DECODE_RESPONSES=true

# Redis默认过期时间(秒)
REDIS_EXPIRE_TIME=3600

# ========================================
# JWT认证配置
# ========================================
# JWT密钥 (生产环境必须更换为随机字符串)
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production

# JWT算法
JWT_ALGORITHM=HS256

# 访问令牌过期时间(分钟)
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# 刷新令牌过期时间(天)
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# ========================================
# 第三方登录配置
# ========================================
# Google OAuth客户端ID
GOOGLE_CLIENT_ID=your-google-client-id

# ========================================
# CrazyGames平台配置
# ========================================
# CrazyGames测试模式
CRAZYGAMES_TEST_MODE=false

# CrazyGames测试公钥
CRAZYGAMES_TEST_PUBLIC_KEY=

# ========================================
# 游戏核心配置
# ========================================
# 每日广告观看上限
MAX_DAILY_ADS=5


# 最大会话时长(秒)
MAX_SESSION_DURATION=3600

# 热点缓存时间(秒)
HOTSPOT_CACHE_TTL=300

# ========================================
# 安全配置
# ========================================
# 每分钟请求限制
RATE_LIMIT_PER_MINUTE=60

# 最大登录尝试次数
MAX_LOGIN_ATTEMPTS=5

# CORS允许的源 (生产环境应限制具体域名)
CORS_ORIGINS=["*"]

# ========================================
# 监控与分析配置
# ========================================
# 是否启用数据分析
ENABLE_ANALYTICS=true

# Prometheus监控端口
PROMETHEUS_PORT=9090

# 监控指标前缀
METRICS_PREFIX=universe_vr_game

# 健康检查间隔(秒)
HEALTH_CHECK_INTERVAL=30

# ========================================
# 日志配置
# ========================================
# 日志级别 (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE=logs/app.log

# 日志轮转周期
LOG_ROTATION=1 day

# 日志保留时间
LOG_RETENTION=30 days

# 日志格式
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# ========================================
# AI服务配置 (可选)
# ========================================
# AI服务URL
AI_SERVICE_URL=

# AI服务API密钥
AI_API_KEY=

# AI服务超时时间(秒)
AI_TIMEOUT=30

# ========================================
# 生产环境示例配置
# ========================================
# 以下是生产环境的推荐配置示例，取消注释并修改相应值

# 生产环境基础配置
# DEBUG=false
# ENVIRONMENT=production
# SECRET_KEY=your-super-secure-random-secret-key-here
# JWT_SECRET_KEY=your-super-secure-jwt-secret-key-here

# 生产环境数据库配置
# DATABASE_URL=mysql+aiomysql://gameuser:<EMAIL>:3306/universe_vr_game
# DATABASE_ECHO=false

# 生产环境Redis配置
# REDIS_URL=redis://redis.example.com:6379/0
# REDIS_PASSWORD=your_redis_password

# 生产环境安全配置
# CORS_ORIGINS=["https://yourgame.com","https://www.yourgame.com"]
# RATE_LIMIT_PER_MINUTE=30

# 生产环境日志配置
# LOG_LEVEL=WARNING
# LOG_FILE=/var/log/universe_vr_game/app.log
