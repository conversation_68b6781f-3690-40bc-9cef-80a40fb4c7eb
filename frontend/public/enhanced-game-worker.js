// 优化的游戏Worker - 专注于热点可见性管理和动画处理
class OptimizedGameWorker {
    constructor() {
        this.hotspots = new Map()
        this.viewState = { hlookat: 0, vlookat: 0, fov: 90 }
        this.animations = new Map()
        this.imageCache = new Map()
        this.preloadQueue = []
        this.isPreloading = false
        this.lastUpdateTime = 0
        this.updateThrottle = 50 // 50ms节流，提升性能

        // 视野范围配置（简化的可见性判断）
        this.viewportConfig = {
            horizontalBuffer: 20, // 水平缓冲角度
            verticalBuffer: 15,   // 垂直缓冲角度
            maxVisibleDistance: 90 // 最大可见距离
        }

        // 动画配置
        this.animationConfig = {
            thief: {
                duration: 1000,
                phases: [
                    { property: 'ry', from: 0, to: 180, duration: 400, easing: 'easeInOutQuart' },
                    { property: 'scale', from: 1, to: 0, duration: 300, delay: 200, easing: 'easeInBack' },
                    { property: 'alpha', from: 1, to: 0, duration: 300, delay: 200, easing: 'easeInQuart' }
                ]
            },
            garbage: {
                duration: 1200,
                phases: [
                    { property: 'ox', keyframes: [0, 5, -5, 0], duration: 300, easing: 'linear' },
                    { property: 'scale', from: 1, to: 0, duration: 400, delay: 300, easing: 'easeInBack' },
                    { property: 'alpha', from: 1, to: 0, duration: 400, delay: 300, easing: 'easeInQuart' }
                ]
            },
            treasure: {
                duration: 1500,
                phases: [
                    { property: 'alpha', keyframes: [1, 1.5, 1], duration: 400, easing: 'easeOutQuart' },
                    { property: 'rz', from: 0, to: 360, duration: 600, easing: 'easeOutQuart' },
                    { property: 'scale', keyframes: [1, 1.2, 0], duration: 600, easing: 'easeOutBack' },
                    { property: 'alpha', from: 1, to: 0, duration: 300, delay: 600, easing: 'easeInQuart' }
                ]
            }
        }

        // 图片资源列表
        this.imageResources = [
            '/img/thief/thief_1_320/thief1_1.png',
            '/img/thief/thief_1_320/thief1_2.png',
            '/img/thief/thief_1_320/thief1_3.png',
            '/img/thief/thief_1_320/thief1_4.png',
            '/img/thief/thief_1_320/thief1_5.png',
            '/img/rubbish/unnamed(1).png',
            '/img/rubbish/unnamed(2).png',
            '/img/rubbish/unnamed(3).png',
            '/img/zuanshi_bg.png'
        ]

        this.startAnimationLoop()
        this.startPreloadImages()
    }
    
    // 初始化热点数据
    initializeHotspots(hotspotsData) {
        this.hotspots.clear()
        
        hotspotsData.forEach(hotspot => {
            this.hotspots.set(hotspot.name, {
                ...hotspot,
                visible: false,
                loaded: false,
                distance: 0,
                inView: false,
                scale: hotspot.scale || 1.0,
                animationState: 'idle',
                lastUpdate: Date.now()
            })
        })
        
        console.log(`Worker: 初始化 ${this.hotspots.size} 个热点`)
        
        return {
            type: 'hotspotsInitialized',
            count: this.hotspots.size
        }
    }
    
    // 更新视角状态
    updateView(viewState) {
        this.viewState = { ...viewState }
        const updates = this.calculateHotspotUpdates()
        
        if (updates.length > 0) {
            return {
                type: 'hotspotUpdates',
                updates,
                stats: this.getHotspotStats()
            }
        }
        
        return null
    }
    
    // 计算热点更新
    calculateHotspotUpdates() {
        const updates = []
        const visibleHotspots = []
        
        for (const [name, hotspot] of this.hotspots) {
            const update = { name, scale: hotspot.scale }
            
            // 计算距离和视野状态
            const distance = this.calculateAngularDistance(hotspot, this.viewState)
            const inView = this.isInViewport(hotspot, this.viewState)
            
            hotspot.distance = distance
            hotspot.inView = inView
            
            // 动态LOD计算
            const lodScale = this.calculateLODScale(distance)
            update.scale = hotspot.scale * lodScale
            
            // 显示/隐藏逻辑
            if (inView && !hotspot.visible) {
                update.shouldShow = true
                update.shouldLoad = !hotspot.loaded
                hotspot.visible = true
                hotspot.animationState = 'showing'
                
                // 预计算显示动画
                update.showAnimation = this.calculateShowAnimation(hotspot)
                
                visibleHotspots.push({ ...update, priority: this.calculatePriority(hotspot) })
            } else if (!inView && hotspot.visible) {
                update.shouldHide = true
                hotspot.visible = false
                hotspot.animationState = 'hiding'
                
                // 预计算隐藏动画
                update.hideAnimation = this.calculateHideAnimation(hotspot)
            } else if (hotspot.visible) {
                // 更新已显示热点的LOD
                update.shouldUpdateLOD = true
            }
            
            if (Object.keys(update).length > 2) { // 除了name和scale
                updates.push(update)
            }
        }
        
        // 按优先级排序
        visibleHotspots.sort((a, b) => b.priority - a.priority)
        
        return updates
    }
    
    // 计算显示动画
    calculateShowAnimation(hotspot) {
        return {
            type: 'show',
            duration: 800,
            keyframes: [
                { time: 0, alpha: 0, scale: hotspot.scale * 0.3 },
                { time: 0.6, alpha: 1, scale: hotspot.scale * 1.1 },
                { time: 1.0, alpha: 1, scale: hotspot.scale }
            ],
            easing: 'easeOutBack'
        }
    }
    
    // 计算隐藏动画
    calculateHideAnimation(hotspot) {
        return {
            type: 'hide',
            duration: 600,
            keyframes: [
                { time: 0, alpha: 1, scale: hotspot.scale },
                { time: 1.0, alpha: 0, scale: hotspot.scale * 0.2 }
            ],
            easing: 'easeInBack'
        }
    }
    
    // 触发点击动画
    triggerClickAnimation(hotspotName, animationType) {
        const hotspot = this.hotspots.get(hotspotName)
        if (!hotspot) return null
        
        const config = this.animationConfig[animationType]
        if (!config) return null
        
        hotspot.animationState = 'clicking'
        hotspot.visible = false // 标记为即将消失
        
        // 生成动画指令
        const animationData = {
            name: hotspotName,
            type: animationType,
            duration: config.duration,
            phases: config.phases.map(phase => ({
                ...phase,
                startTime: Date.now() + (phase.delay || 0)
            }))
        }
        
        // 添加到动画队列
        this.animations.set(hotspotName, animationData)
        
        return {
            type: 'clickAnimation',
            animation: animationData
        }
    }
    
    // 计算LOD缩放
    calculateLODScale(distance) {
        if (distance < 30) return 1.2      // 很近 - 放大
        if (distance < 60) return 1.0      // 正常距离
        if (distance < 90) return 0.8      // 较远 - 缩小
        return 0.6                         // 很远 - 更小
    }
    
    // 计算优先级
    calculatePriority(hotspot) {
        let priority = 100 - hotspot.distance // 距离越近优先级越高
        
        // 根据类型调整优先级
        if (hotspot.type === 'treasure') priority += 20
        if (hotspot.type === 'thief') priority += 10
        if (hotspot.type === 'garbage') priority += 5
        
        return priority
    }
    
    // 检查是否在视野内
    isInViewport(hotspot, viewState) {
        const hDiff = Math.abs(this.normalizeAngle(hotspot.ath - viewState.hlookat))
        const vDiff = Math.abs(hotspot.atv - viewState.vlookat)
        
        const hFov = (viewState.fov / 2) + 15 // 15度缓冲
        const vFov = (viewState.fov / 2) + 15
        
        return hDiff <= hFov && vDiff <= vFov
    }
    
    // 计算角度距离
    calculateAngularDistance(hotspot, viewState) {
        const hDiff = this.normalizeAngle(hotspot.ath - viewState.hlookat)
        const vDiff = hotspot.atv - viewState.vlookat
        return Math.sqrt(hDiff * hDiff + vDiff * vDiff)
    }
    
    // 标准化角度
    normalizeAngle(angle) {
        while (angle > 180) angle -= 360
        while (angle < -180) angle += 360
        return angle
    }
    
    // 获取热点统计
    getHotspotStats() {
        const stats = {
            total: this.hotspots.size,
            visible: 0,
            loaded: 0,
            animating: 0
        }
        
        for (const hotspot of this.hotspots.values()) {
            if (hotspot.visible) stats.visible++
            if (hotspot.loaded) stats.loaded++
            if (hotspot.animationState !== 'idle') stats.animating++
        }
        
        return stats
    }
    
    // 动画循环
    startAnimationLoop() {
        const updateAnimations = () => {
            const now = Date.now()
            const completedAnimations = []
            
            for (const [name, animation] of this.animations) {
                const elapsed = now - animation.startTime
                
                if (elapsed >= animation.duration) {
                    completedAnimations.push(name)
                    
                    // 通知主线程动画完成
                    self.postMessage({
                        type: 'animationComplete',
                        hotspotName: name,
                        animationType: animation.type
                    })
                }
            }
            
            // 清理完成的动画
            completedAnimations.forEach(name => {
                this.animations.delete(name)
                const hotspot = this.hotspots.get(name)
                if (hotspot) {
                    hotspot.animationState = 'idle'
                }
            })
            
            setTimeout(updateAnimations, 16) // ~60fps
        }
        
        updateAnimations()
    }
    
    // 开始预加载图片
    startPreloadImages() {
        this.preloadQueue = [...this.imageResources]
        this.preloadNextImage()
    }
    
    // 预加载下一张图片
    async preloadNextImage() {
        if (this.preloadQueue.length === 0 || this.isPreloading) return
        
        this.isPreloading = true
        const imageUrl = this.preloadQueue.shift()
        
        try {
            const response = await fetch(imageUrl)
            if (response.ok) {
                const blob = await response.blob()
                this.imageCache.set(imageUrl, {
                    blob,
                    url: URL.createObjectURL(blob),
                    loadTime: Date.now()
                })
                
                // 通知主线程图片已预加载
                self.postMessage({
                    type: 'imagePreloaded',
                    url: imageUrl,
                    cacheSize: this.imageCache.size,
                    remaining: this.preloadQueue.length
                })
            }
        } catch (error) {
            console.error(`预加载图片失败: ${imageUrl}`, error)
        }
        
        this.isPreloading = false
        
        // 继续预加载下一张
        if (this.preloadQueue.length > 0) {
            setTimeout(() => this.preloadNextImage(), 100)
        } else {
            self.postMessage({
                type: 'preloadComplete',
                totalImages: this.imageCache.size
            })
        }
    }
    
    // 获取预加载的图片
    getPreloadedImage(url) {
        return this.imageCache.get(url)
    }
    
    // 清理图片缓存
    cleanupImageCache() {
        for (const [url, data] of this.imageCache) {
            URL.revokeObjectURL(data.url)
        }
        this.imageCache.clear()
    }
}

// Worker实例
const gameWorker = new OptimizedGameWorker()

// 消息处理
self.onmessage = function(e) {
    const { type, data } = e.data
    let result = null
    
    try {
        switch (type) {
            case 'initializeHotspots':
                result = gameWorker.initializeHotspots(data)
                break
                
            case 'updateView':
                result = gameWorker.updateView(data)
                break
                
            case 'triggerClickAnimation':
                result = gameWorker.triggerClickAnimation(data.hotspotName, data.animationType)
                break
                
            case 'getPreloadedImage':
                result = gameWorker.getPreloadedImage(data.url)
                break
                
            case 'cleanup':
                gameWorker.cleanupImageCache()
                result = { type: 'cleanupComplete' }
                break
                
            default:
                console.warn('Worker: 未知消息类型:', type)
        }
        
        if (result) {
            self.postMessage(result)
        }
        
    } catch (error) {
        self.postMessage({
            type: 'error',
            message: error.message,
            stack: error.stack
        })
    }
}
