import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from 'unplugin-vue-components/resolvers'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // 自动导入 Vue 3 组合式 API
    AutoImport({
      imports: [
        'vue',
        'vue-router',
        'vue-i18n',
        'pinia',
        {
          'vant': [
            'showToast',
            'showDialog', 
            'showConfirmDialog',
            'showNotify',
            'showImagePreview',
            'showLoadingToast',
            'showSuccessToast',
            'showFailToast'
          ]
        }
      ],
      dts: true, // 生成类型声明文件
      eslintrc: {
        enabled: true,
        filepath: './.eslintrc-auto-import.json',
        globalsPropValue: true,
      },
    }),
    // 自动导入组件
    Components({
      resolvers: [VantResolver()],
      dts: true, // 生成类型声明文件
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    host: '0.0.0.0', // 监听所有网络接口（局域网内其他设备可访问）
    port: 3000,
    // host: true,
    open: true
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 使用单一入口文件，简化配置
        additionalData: `@import "@/styles/index.scss";`,
        // 完全静默化Sass警告
        quietDeps: true,
        silenceDeprecations: ['legacy-js-api', 'import', 'global-builtin', 'mixed-decls', 'color-4-api'],
        logger: {
          warn: () => {},
          debug: () => {}
        }
      }
    }
  },
  build: {
    // 代码分割配置
    rollupOptions: {
      output: {
        // 手动分割chunks
        manualChunks: {
          // Vue相关
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          // UI库
          'vant-vendor': ['vant'],
          // 工具库
          'utils-vendor': ['axios', 'js-cookie'],
          // 游戏核心
          'game-core': [
            './src/utils/krpano.js',
            './src/services/gameService.js',
            './src/stores/game.js'
          ]
        },
        // 用于控制chunk命名
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop() : 'vendor'
          return `js/[name]-${facadeModuleId}-[hash].js`
        },
        // 入口文件命名
        entryFileNames: 'js/[name]-[hash].js',
        // 资源文件命名
        assetFileNames: (assetInfo) => {
          const extType = assetInfo.name.split('.').at(-1)
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(extType)) {
            return 'images/[name]-[hash][extname]'
          }
          if (/woff|woff2|eot|ttf|otf/i.test(extType)) {
            return 'fonts/[name]-[hash][extname]'
          }
          return '[ext]/[name]-[hash][extname]'
        }
      }
    },
    // 启用 CSS 代码分割
    cssCodeSplit: true,
    // 构建目标
    target: 'es2015',
    // 启用压缩
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info']
      }
    },
    // 警告大小限制
    chunkSizeWarningLimit: 1000,
    // 启用源码映射用于生产调试
    sourcemap: false
  },
  // 优化选项
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'vant',
      'axios',
      'js-cookie'
    ],
    exclude: ['@vueuse/core', 'enhanced-game-worker.js']
  },
  worker: {
    format: 'es',
    plugins: []
  },
  // 确保Worker文件被正确处理
  assetsInclude: ['**/*.worker.js'],
 
}) 