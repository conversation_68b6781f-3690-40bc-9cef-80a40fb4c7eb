import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import dataService from '@/services/dataService'
import { useAuthStore } from '@/stores/auth'
import api from '@/utils/api'
export const useGameStore = defineStore('game', () => {
  // 游戏状态
  const isGameStarted = ref(false)
  const isGamePaused = ref(false)
  const currentLevel = ref(1)
  const gameTime = ref(300) // 倒计时，单位秒
  
  // 用户信息
  const userInfo = ref({
    user_id: null,
    avatar: '',
    nickname: '游客',
    guardian_level: 1, // 守望者等级
    civilization_exp: 0, // 文明经验值
    total_play_time: 0,
    total_sessions: 0,
    cities_unlocked: 1
  })
  
  // 核心游戏资源 - 文明经验值系统
  const civilizationExp = ref(0) // 文明经验值（主要货币）
  
  // 体力值系统
  const stamina = ref(100) // 当前体力值
  const maxStamina = ref(100) // 最大体力值
  
  // 守望者等级系统 (1-14档)
  const guardianLevel = ref(1) // 守望者等级
  const guardianExp = ref(0) // 当前等级经验
  
  // 收集统计（用于任务和成就）
  const thievesCollected = ref(0) // 抓捕的小偷数量
  const garbageCollected = ref(0) // 清理的垃圾数量
  const artifactsCollected = ref(0) // 收集的文化图鉴数量
  const monumentsProtected = ref(0) // 保护的古迹数量

  // 热点数量统计（从XML获取）
  const hotspotCounts = ref({
    totalThieves: 0,
    totalGarbage: 0,
    totalTreasure: 0,
    totalBoss: 0,
    remainingThieves: 0,
    remainingGarbage: 0,
    remainingTreasure: 0,
    remainingBoss: 0
  })
  
  // 道具
  const tools = ref({
    radar: {
      count: 3,
      cooldown: 0,
      maxCooldown: 30
    },
    magnifier: {
      count: 3,
      cooldown: 0,
      maxCooldown: 30
    }
  })
  
  // 关卡信息
  const levels = ref([
    { id: 1, icon: '/img/map_Vatican.ae8dbd6c.png', stars: 0, unlocked: true, requiredCollections: 4 },
    { id: 2, icon: '/img/map_Paris.562136da.png', stars: 0, unlocked: false, requiredCollections: 5 },
    { id: 3, icon: '/img/map_NewYork.3f7de8a5.png', stars: 0, unlocked: false, requiredCollections: 6 },
    { id: 4, icon: '/img/map_Shanghai.624b3b9d.png', stars: 0, unlocked: false, requiredCollections: 7 },
    { id: 5, icon: '/img/map_Prague.fcfba582.png', stars: 0, unlocked: false, requiredCollections: 8 },
    { id: 6, icon: '/img/map_Zurich.95053267.png', stars: 0, unlocked: false, requiredCollections: 9 },
    { id: 7, icon: '/img/map_Iceland.ef8f6dbc.png', stars: 0, unlocked: false, requiredCollections: 10 },
    { id: 8, icon: '/img/map_Venice.02143ea7.png', stars: 0, unlocked: false, requiredCollections: 12 }
  ])
  
  // 当前关卡的小偷和垃圾
  const thieves = ref([])
  const garbages = ref([])
  const treasures = ref([])
  
  // 图鉴收集系统
  const collections = ref({
    thieves: [], // 已收集的小偷
    garbages: [], // 已收集的垃圾
    treasures: [], // 已收集的宝箱
    artifacts: [] // 已收集的文物
  })
  
  // 当前关卡收集进度
  const currentLevelCollections = ref(0)
  
  // 计算属性
  const currentLevelInfo = computed(() => {
    return levels.value.find(level => level.id === currentLevel.value)
  })
  
  const canUseRadar = computed(() => {
    return tools.value.radar.count > 0 || tools.value.radar.cooldown === 0
  })
  
  const canUseMagnifier = computed(() => {
    return tools.value.magnifier.count > 0 || tools.value.magnifier.cooldown === 0
  })
  
  // 检查是否可以开启时空门
  const canOpenTimegate = computed(() => {
    const levelInfo = currentLevelInfo.value
    return levelInfo ? currentLevelCollections.value >= levelInfo.requiredCollections : false
  })
  
  // 总收集数量
  const totalCollections = computed(() => {
    return collections.value.thieves.length + 
           collections.value.garbages.length + 
           collections.value.treasures.length + 
           collections.value.artifacts.length
  })

  // BOSS系统
  const bossHealth = ref(100) // BOSS血量百分比
  const bossMaxHealth = ref(100)
  const bossDialogueStage = ref(0) // 对话阶段 (0: 未开始, 1-4: 对应80%, 60%, 40%, 20%)
  
  // 文化图鉴系统
  const culturalAtlas = ref([]) // 已收集的文化图鉴
  
  // 持久化存储键名
  const STORAGE_KEY = 'gameState'
  
  // 保存游戏状态到持久化存储
  const saveGameState = () => {
    const gameState = {
      userInfo: userInfo.value,
      civilizationExp: civilizationExp.value,
      stamina: stamina.value,
      guardianLevel: guardianLevel.value,
      guardianExp: guardianExp.value,
      thievesCollected: thievesCollected.value,
      garbageCollected: garbageCollected.value,
      artifactsCollected: artifactsCollected.value,
      monumentsProtected: monumentsProtected.value,
      bossHealth: bossHealth.value,
      bossDialogueStage: bossDialogueStage.value,
      culturalAtlas: culturalAtlas.value,
      tools: tools.value,
      currentLevel: currentLevel.value,
      levels: levels.value,
      collections: collections.value,
      lastSaved: new Date().toISOString()
    }
    
    // 使用 dataService 保存，支持 CrazyGames 数据模块
    dataService.setJSON(STORAGE_KEY, gameState)
    console.log('游戏状态已保存:', gameState)
  }

  
  // 从持久化存储加载游戏状态
  const loadGameState = () => {
    const savedState = dataService.getJSON(STORAGE_KEY)
    
    if (savedState) {
      console.log('加载已保存的游戏状态:', savedState)
      
      // 恢复用户信息
      if (savedState.userInfo) {
        userInfo.value = { ...userInfo.value, ...savedState.userInfo }
      }
      
      // 恢复游戏资源
      if (savedState.civilizationExp !== undefined) {
        civilizationExp.value = savedState.civilizationExp
      }
      if (savedState.stamina !== undefined) {
        stamina.value = savedState.stamina
      }
      if (savedState.guardianLevel !== undefined) {
        guardianLevel.value = savedState.guardianLevel
      }
      if (savedState.guardianExp !== undefined) {
        guardianExp.value = savedState.guardianExp
      }
      if (savedState.thievesCollected !== undefined) {
        thievesCollected.value = savedState.thievesCollected
      }
      if (savedState.garbageCollected !== undefined) {
        garbageCollected.value = savedState.garbageCollected
      }
      if (savedState.artifactsCollected !== undefined) {
        artifactsCollected.value = savedState.artifactsCollected
      }
      if (savedState.monumentsProtected !== undefined) {
        monumentsProtected.value = savedState.monumentsProtected
      }
      if (savedState.bossHealth !== undefined) {
        bossHealth.value = savedState.bossHealth
      }
      if (savedState.bossDialogueStage !== undefined) {
        bossDialogueStage.value = savedState.bossDialogueStage
      }
      if (savedState.culturalAtlas) {
        culturalAtlas.value = savedState.culturalAtlas
      }
      
      // 恢复道具
      if (savedState.tools) {
        tools.value = { ...tools.value, ...savedState.tools }
      }
      
      // 恢复关卡进度
      if (savedState.currentLevel !== undefined) {
        currentLevel.value = savedState.currentLevel
      }
      if (savedState.levels) {
        levels.value = savedState.levels
      }
      
      // 恢复收集进度
      if (savedState.collections) {
        collections.value = savedState.collections
      }
      
      console.log('游戏状态恢复完成')
      return true
    }
    
    return false
  }
  
  // 清除游戏状态
  const clearGameState = () => {
    dataService.removeItem(STORAGE_KEY)
    console.log('游戏状态已清除')
  }
  
  // 方法
  const startGame = () => {
    isGameStarted.value = true
    isGamePaused.value = false
    gameTime.value = 300
    currentLevelCollections.value = 0
  }
  
  const pauseGame = () => {
    isGamePaused.value = true
  }
  
  const resumeGame = () => {
    isGamePaused.value = false
  }
  
  // 文明经验值操作
  const addCivilizationExp = (amount) => {
    civilizationExp.value += amount
    checkGuardianLevelUp()
    
    // 如果是游客模式，保存数据
    if (isGuestMode()) {
      dataService.updateGuestResources({ civilizationExp: civilizationExp.value })
    }
    saveGameState()
    
    console.log(`获得文明经验值: +${amount}, 总计: ${civilizationExp.value}`)
    return civilizationExp.value
  }
  
  const spendCivilizationExp = (amount) => {
    if (civilizationExp.value >= amount) {
      civilizationExp.value -= amount
      
      if (isGuestMode()) {
        dataService.updateGuestResources({ civilizationExp: civilizationExp.value })
      }
      saveGameState()
      return true
    }
    return false
  }
  
  // 体力值操作
  const consumeStamina = (amount = 1) => {
    if (stamina.value >= amount) {
      stamina.value -= amount
      saveGameState()
      return true
    }
    return false
  }
  
  const restoreStamina = (amount) => {
    stamina.value = Math.min(stamina.value + amount, maxStamina.value)
    saveGameState()
  }
  
  const restoreStaminaByAd = () => {
    restoreStamina(20) // 看广告恢复20点体力
  }
  
  const useTool = (toolType) => {
    if (tools.value[toolType].count > 0) {
      tools.value[toolType].count--
      // 如果是游客模式，保存数据
      if (isGuestMode()) {
        dataService.updateGuestTools(tools.value)
      }
      // 保存游戏状态
      saveGameState()
      return true
    }
    return false
  }
  
  const watchAd = async (toolType, provider) => {
    const authStore = useAuthStore()
    const isLoggedIn = authStore.isLoggedIn || false
    if(provider === 'crazygames') {
      const callbacks = {
        adFinished: () => {
          console.log("End midgame ad")
          adApi(toolType, 'crazygames')
        },
        adError: (error) => console.log("Error midgame ad", error),
        adStarted: () => console.log("Start midgame ad")
      };
      window.CrazyGames.SDK.ad.requestAd("midgame", callbacks);
      // or
      // window.CrazyGames.SDK.ad.requestAd("rewarded", callbacks);
    }
    const adApi = async (toolType, provider) => {
      // 看广告后恢复体力值
    if (toolType === 'stamina') {
      restoreStaminaByAd()
    } else {
      // 看广告后增加道具次数
      tools.value[toolType].count++
      tools.value[toolType].cooldown = tools.value[toolType].maxCooldown
    }
      // 如果是未登录，保存数据
      if (!isLoggedIn) {
        dataService.updateGuestTools(tools.value)
      } else {
        try {
          const response = await api.ads.watchAd(toolType, null, 30, provider)
          const result = response.data
          return { success: true, data: result }
        } catch (error) {
          console.error('观看广告失败:', error)
          return { success: false, error: error.message }
        }
      }
      // 保存游戏状态
      saveGameState()
    }
  }
  
  // 守望者等级系统 (1-14档)
  const getGuardianLevelExp = (level) => {
    // 14档守望者等级所需经验值
    const expTable = [
      0, 100, 250, 450, 750, 1200, 1800, 2550, 
      3500, 4700, 6200, 8000, 10200, 13000, 16500
    ]
    return expTable[level] || expTable[14]
  }
  
  const checkGuardianLevelUp = () => {
    if (guardianLevel.value >= 14) return { leveledUp: false }
    
    const requiredExp = getGuardianLevelExp(guardianLevel.value + 1)
    
    if (civilizationExp.value >= requiredExp) {
      guardianLevel.value++
      
      // 如果是游客模式，保存数据
      if (isGuestMode()) {
        dataService.updateGuestUserInfo({
          guardian_level: guardianLevel.value,
          civilization_exp: civilizationExp.value
        })
      }
      
      saveGameState()
      
      console.log(`守望者等级提升到 ${guardianLevel.value} 档！`)
      return {
        leveledUp: true,
        newLevel: guardianLevel.value,
        currentExp: civilizationExp.value
      }
    }
    
    return { leveledUp: false }
  }
  
  // 获取守望者等级名称
  const getGuardianLevelName = (level) => {
    const levelNames = [
      '新手守卫', '青铜守卫', '白银守卫', '黄金守卫',
      '白金守卫', '钻石守卫', '大师守卫', '宗师守卫',
      '传奇守卫', '史诗守卫', '神话守卫', '至尊守卫',
      '永恒守卫', '终极守护者', '文明之光'
    ]
    return levelNames[level - 1] || '未知等级'
  }
  
  const unlockNextLevel = () => {
    const nextLevel = levels.value.find(level => level.id === currentLevel.value + 1)
    if (nextLevel) {
      nextLevel.unlocked = true
      // 如果是游客模式，保存数据
      if (isGuestMode()) {
        dataService.updateGuestProgress({ levels: levels.value })
      }
      // 保存游戏状态
      saveGameState()
    }
  }
  
  const updateLevelStars = (stars) => {
    const level = levels.value.find(l => l.id === currentLevel.value)
    if (level) {
      level.stars = Math.max(level.stars, stars)
      // 如果是游客模式，保存数据
      if (isGuestMode()) {
        dataService.updateGuestProgress({ levels: levels.value })
      }
      // 保存游戏状态
      saveGameState()
    }
  }
  
  const removeThief = (thiefId) => {
    const index = thieves.value.findIndex(t => t.id === thiefId || t.name === thiefId)
    if (index > -1) {
      const removedThief = thieves.value[index]
      thieves.value.splice(index, 1)
      
      // 消耗体力值
      const staminaCost = stamina.value > 0 ? 1 : 0
      if (staminaCost > 0) {
        consumeStamina(staminaCost)
      }
      
      // 添加到图鉴收集
      addToCollection('thieves', removedThief.id || removedThief.name || thiefId)
      
      // 更新抓捕小偷数量
      thievesCollected.value += 1
      
      // 获得文明经验值（体力不足时减半）
      const baseExp = 10
      const expGained = stamina.value <= 0 ? Math.floor(baseExp * 0.5) : baseExp
      addCivilizationExp(expGained)
      
      // 攻击BOSS
      attackBoss(5)
      
      // 更新任务进度
      updateTaskProgress('catch_thieves', thievesCollected.value)
      
      console.log(`抓捕小偷: ${thiefId}, 获得经验: ${expGained}, 剩余体力: ${stamina.value}`)
    }
  }

  // 只移除热点UI，不更新收集数量（在线模式使用）
  const removeThiefFromUI = (thiefId) => {
    const index = thieves.value.findIndex(t => t.id === thiefId || t.name === thiefId)
    if (index > -1) {
      const removedThief = thieves.value[index]
      thieves.value.splice(index, 1)
      // 添加到图鉴收集，使用id作为收集标识
      addToCollection('thieves', removedThief.id || removedThief.name || thiefId)
      
      console.log('从UI移除小偷:', removedThief)
    }
  }
  
  const removeGarbage = (garbageId) => {
    const index = garbages.value.findIndex(g => g.id === garbageId || g.name === garbageId)
    if (index > -1) {
      const removedGarbage = garbages.value[index]
      garbages.value.splice(index, 1)
      
      // 消耗体力值
      const staminaCost = stamina.value > 0 ? 1 : 0
      if (staminaCost > 0) {
        consumeStamina(staminaCost)
      }
      
      // 添加到图鉴收集
      addToCollection('garbages', removedGarbage.id || removedGarbage.name || garbageId)
      
      // 更新清理垃圾数量
      garbageCollected.value += 1
      
      // 获得文明经验值（体力不足时减半）
      const baseExp = 8
      const expGained = stamina.value <= 0 ? Math.floor(baseExp * 0.5) : baseExp
      addCivilizationExp(expGained)
      
      // 攻击BOSS
      attackBoss(3)
      
      // 更新任务进度
      updateTaskProgress('clean_garbage', garbageCollected.value)
      
      console.log(`清理垃圾: ${garbageId}, 获得经验: ${expGained}, 剩余体力: ${stamina.value}`)
    }
  }

  // 只移除热点UI，不更新收集数量（在线模式使用）
  const removeGarbageFromUI = (garbageId) => {
    const index = garbages.value.findIndex(g => g.id === garbageId || g.name === garbageId)
    if (index > -1) {
      const removedGarbage = garbages.value[index]
      garbages.value.splice(index, 1)
      // 添加到图鉴收集，使用id作为收集标识
      addToCollection('garbages', removedGarbage.id || removedGarbage.name || garbageId)
      
      console.log('从UI移除垃圾:', removedGarbage)
    }
  }
  
  const removeTreasure = (treasureId) => {
    const index = treasures.value.findIndex(t => t.id === treasureId || t.name === treasureId)
    if (index > -1) {
      const removedTreasure = treasures.value[index]
      treasures.value.splice(index, 1)
      // 添加到图鉴收集，使用id作为收集标识
      addToCollection('treasures', removedTreasure.id || removedTreasure.name || treasureId)
      console.log('移除宝箱:', removedTreasure)
    }
  }
  
  // 添加到图鉴收集
  const addToCollection = (type, itemId) => {
    if (!collections.value[type].includes(itemId)) {
      collections.value[type].push(itemId)
      currentLevelCollections.value++
      
      // 保存到数据服务
      dataService.setJSON('gameCollections', collections.value)
      // 保存游戏状态
      saveGameState()
    }
  }
  
  // 从数据服务加载收集数据
  const loadCollections = () => {
    collections.value = dataService.getJSON('gameCollections', {
      thieves: [],
      garbages: [],
      treasures: [],
      artifacts: []
    })
  }

  // 初始化游客模式数据
  const initGuestMode = () => {
    const guestData = dataService.getGuestData()
    
    // 加载游客数据到游戏状态
    userInfo.value = { ...userInfo.value, ...guestData.userInfo }
    civilizationExp.value = guestData.gameResources.civilizationExp || 0
    stamina.value = guestData.gameResources.stamina || 100
    guardianLevel.value = guestData.userInfo.guardian_level || 1
    tools.value = guestData.tools
    currentLevel.value = guestData.gameProgress.currentLevel
    levels.value = guestData.gameProgress.levels
    
    console.log('游客模式数据已加载:', guestData)
  }

  // 保存游客模式数据
  const saveGuestData = () => {
    if (!dataService.isGuestMode()) return
    
    const guestData = {
      userInfo: userInfo.value,
      gameResources: {
        civilizationExp: civilizationExp.value,
        stamina: stamina.value
      },
      tools: tools.value,
      gameProgress: {
        currentLevel: currentLevel.value,
        levels: levels.value
      }
    }
    
    dataService.saveGuestData(guestData)
  }

  // 检查是否为游客模式
  const isGuestMode = () => {
    return dataService.isGuestMode()
  }
  
  // 重置当前关卡收集进度
  const resetCurrentLevelCollections = () => {
    currentLevelCollections.value = 0
  }
  
  // 更新用户信息（用于登录后同步数据）
  const updateUserInfo = (newUserInfo) => {
    userInfo.value = { ...userInfo.value, ...newUserInfo }
    // 保存游戏状态
    saveGameState()
  }
  
  // 同步资源数据（用于登录后同步）
  const syncResources = (newCivilizationExp, newStamina) => {
    if (newCivilizationExp !== undefined) {
      civilizationExp.value = newCivilizationExp
    }
    if (newStamina !== undefined) {
      stamina.value = newStamina
    }
    saveGameState()
  }
  
  // 同步任务相关资源
  const syncTaskResources = (taskResources) => {
    if (taskResources) {
      if (typeof taskResources.civilization_exp === 'number') {
        civilizationExp.value = taskResources.civilization_exp
      }
      if (typeof taskResources.stamina === 'number') {
        stamina.value = taskResources.stamina
      }
      if (typeof taskResources.thieves_collected === 'number') {
        thievesCollected.value = taskResources.thieves_collected
      }
      if (typeof taskResources.garbage_collected === 'number') {
        garbageCollected.value = taskResources.garbage_collected
      }
      if (typeof taskResources.artifacts_collected === 'number') {
        artifactsCollected.value = taskResources.artifacts_collected
      }
      if (typeof taskResources.monuments_protected === 'number') {
        monumentsProtected.value = taskResources.monuments_protected
      }
      
      console.log('任务资源已同步:', {
        文明经验值: civilizationExp.value,
        体力值: stamina.value,
        小偷收集: thievesCollected.value,
        垃圾收集: garbageCollected.value,
        文化图鉴: artifactsCollected.value,
        保护古迹: monumentsProtected.value
      })
      
      saveGameState()
    }
  }
  
  // 更新会话数据（用于同步前后端数据）
  const updateSessionData = (sessionData) => {
    // 注意：不要覆盖热点数组！热点数组应该只从XML场景文件加载
    
    // 同步收集数量
    if (sessionData.thievesCollected !== undefined) {
      thievesCollected.value = sessionData.thievesCollected
    }
    if (sessionData.garbageCollected !== undefined) {
      garbageCollected.value = sessionData.garbageCollected
    }
    if (sessionData.artifactsCollected !== undefined) {
      artifactsCollected.value = sessionData.artifactsCollected
    }
    if (sessionData.monumentsProtected !== undefined) {
      monumentsProtected.value = sessionData.monumentsProtected
    }
    
    // 同步文明经验值和体力
    if (sessionData.civilizationExp !== undefined) {
      civilizationExp.value = sessionData.civilizationExp
    }
    if (sessionData.stamina !== undefined) {
      stamina.value = sessionData.stamina
    }
    
    console.log('会话数据已同步:', {
      剩余小偷: thieves.value.length,
      剩余垃圾: garbages.value.length,
      收集小偷: thievesCollected.value,
      收集垃圾: garbageCollected.value,
      文化图鉴: artifactsCollected.value,
      保护古迹: monumentsProtected.value,
      文明经验值: civilizationExp.value,
      体力值: stamina.value
    })
  }
  
  // BOSS系统方法
  const attackBoss = (damage) => {
    const oldHealth = bossHealth.value
    bossHealth.value = Math.max(0, bossHealth.value - damage)
    
    // 检查是否需要触发BOSS对话
    checkBossDialogue(oldHealth, bossHealth.value)
    
    console.log(`攻击BOSS: -${damage}, 剩余血量: ${bossHealth.value}%`)
    
    if (bossHealth.value <= 0) {
      onBossDefeated()
    }
  }
  
  const checkBossDialogue = (oldHealth, newHealth) => {
    const thresholds = [80, 60, 40, 20]
    
    for (let i = 0; i < thresholds.length; i++) {
      const threshold = thresholds[i]
      if (oldHealth > threshold && newHealth <= threshold && bossDialogueStage.value <= i) {
        bossDialogueStage.value = i + 1
        triggerBossDialogue(i + 1, threshold)
        break
      }
    }
  }
  
  const triggerBossDialogue = (stage, healthPercent) => {
    // 暂停游戏
    pauseGame()
    
    // 触发BOSS对话事件（由UI组件监听）
    console.log(`触发BOSS对话: 阶段${stage}, 血量${healthPercent}%`)
    
    // 可以发送事件给UI组件显示对话
    window.dispatchEvent(new CustomEvent('bossDialogue', {
      detail: { stage, healthPercent }
    }))
  }
  
  const onBossDefeated = () => {
    console.log('BOSS被击败！')
    // 给予丰厚奖励
    addCivilizationExp(100)
    
    // 触发胜利事件
    window.dispatchEvent(new CustomEvent('bossDefeated'))
  }
  
  // 添加文化图鉴
  const addCulturalAtlas = (atlas) => {
    if (atlas && !culturalAtlas.value.find(a => a.id === atlas.id)) {
      culturalAtlas.value.push(atlas)
      artifactsCollected.value += 1
      
      // 获得文明经验值奖励
      addCivilizationExp(25)
      
      // 更新任务进度
      updateTaskProgress('collect_artifacts', artifactsCollected.value)
      
      console.log('获得文化图鉴:', atlas)
      saveGameState()
    }
  }
  
  // 保护古迹（问答系统）
  const protectMonument = (success) => {
    if (success) {
      monumentsProtected.value += 1
      addCivilizationExp(15)
      
      // 更新任务进度
      updateTaskProgress('protect_monuments', monumentsProtected.value)
      
      console.log('成功保护古迹，获得15文明经验值')
    } else {
      // 失败扣除经验
      const lostExp = Math.min(10, civilizationExp.value)
      civilizationExp.value -= lostExp
      console.log(`保护古迹失败，失去${lostExp}文明经验值`)
    }
    saveGameState()
  }
  

  // 更新热点数量统计
  const updateHotspotCounts = (counts) => {
    hotspotCounts.value = { ...hotspotCounts.value, ...counts }
  }

  // 收集热点后减少剩余数量
  const collectHotspotByType = (type) => {
    switch (type) {
      case 'thief':
        if (hotspotCounts.value.remainingThieves > 0) {
          hotspotCounts.value.remainingThieves--
        }
        break
      case 'garbage':
        if (hotspotCounts.value.remainingGarbage > 0) {
          hotspotCounts.value.remainingGarbage--
        }
        break
      case 'treasure':
        if (hotspotCounts.value.remainingTreasure > 0) {
          hotspotCounts.value.remainingTreasure--
        }
        break
      case 'boss':
        if (hotspotCounts.value.remainingBoss > 0) {
          hotspotCounts.value.remainingBoss--
        }
        break
    }
  }
  
  // 更新任务进度
  const updateTaskProgress = (taskType, currentProgress) => {
    // 发送事件给TaskStore更新任务进度
    window.dispatchEvent(new CustomEvent('taskProgress', {
      detail: { taskType, currentProgress }
    }))
  }
  
  return {
    // state
    isGameStarted,
    isGamePaused,
    currentLevel,
    gameTime,
    userInfo,
    civilizationExp,
    stamina,
    maxStamina,
    guardianLevel,
    guardianExp,
    thievesCollected,
    garbageCollected,
    artifactsCollected,
    monumentsProtected,
    bossHealth,
    bossMaxHealth,
    bossDialogueStage,
    culturalAtlas,
    tools,
    levels,
    thieves,
    garbages,
    treasures,
    collections,
    currentLevelCollections,
    hotspotCounts,
    
    // computed
    currentLevelInfo,
    canUseRadar,
    canUseMagnifier,
    canOpenTimegate,
    totalCollections,
    
    // actions
    startGame,
    pauseGame,
    resumeGame,
    addCivilizationExp,
    spendCivilizationExp,
    consumeStamina,
    restoreStamina,
    restoreStaminaByAd,
    useTool,
    watchAd,
    checkGuardianLevelUp,
    getGuardianLevelExp,
    getGuardianLevelName,
    unlockNextLevel,
    updateLevelStars,
    removeThief,
    removeThiefFromUI,
    removeGarbage,
    removeGarbageFromUI,
    removeTreasure,
    addToCollection,
    loadCollections,
    resetCurrentLevelCollections,
    // BOSS系统
    attackBoss,
    checkBossDialogue,
    triggerBossDialogue,
    onBossDefeated,
    // 文化图鉴和古迹系统
    addCulturalAtlas,
    protectMonument,
    // 游客模式相关
    initGuestMode,
    saveGuestData,
    isGuestMode,
    // 持久化相关
    saveGameState,
    loadGameState,
    clearGameState,
    updateUserInfo,
    syncResources,
    syncTaskResources,
    // 新方法
    updateSessionData,
    // 热点数量统计相关
    updateHotspotCounts,
    collectHotspotByType,
    // 任务进度相关
    updateTaskProgress
  }
}) 