/**
 * 每日任务配置
 * 根据游戏设计文档要求配置任务类型和数量
 */

export const DAILY_TASK_TEMPLATES = [
  // 抓小偷任务 (10, 50, 100)
  {
    id: 'catch_thieves_10',
    type: 'catch_thieves',
    name: '新手守卫',
    description: '抓捕10个小偷',
    target: 10,
    reward: {
      civilization_exp: 100,
      stamina: 10
    },
    icon: '/img/page_icons/thieficon-2.png'
  },
  {
    id: 'catch_thieves_50',
    type: 'catch_thieves',
    name: '熟练守卫',
    description: '抓捕50个小偷',
    target: 50,
    reward: {
      civilization_exp: 300,
      stamina: 20
    },
    icon: '/img/page_icons/thieficon-2.png'
  },
  {
    id: 'catch_thieves_100',
    type: 'catch_thieves',
    name: '专业守卫',
    description: '抓捕100个小偷',
    target: 100,
    reward: {
      civilization_exp: 600,
      stamina: 30
    },
    icon: '/img/page_icons/thieficon-2.png'
  },
  
  // 清理垃圾任务 (10, 50, 100)
  {
    id: 'clean_garbage_10',
    type: 'clean_garbage',
    name: '环保新人',
    description: '清理10个垃圾',
    target: 10,
    reward: {
      civilization_exp: 80,
      stamina: 10
    },
    icon: '/img/page_icons/rubbish-1.png'
  },
  {
    id: 'clean_garbage_50',
    type: 'clean_garbage',
    name: '环保达人',
    description: '清理50个垃圾',
    target: 50,
    reward: {
      civilization_exp: 250,
      stamina: 20
    },
    icon: '/img/page_icons/rubbish-1.png'
  },
  {
    id: 'clean_garbage_100',
    type: 'clean_garbage',
    name: '环保专家',
    description: '清理100个垃圾',
    target: 100,
    reward: {
      civilization_exp: 500,
      stamina: 30
    },
    icon: '/img/page_icons/rubbish-1.png'
  },
  
  // 图鉴收集任务 (1, 5)
  {
    id: 'collect_atlas_1',
    type: 'collect_artifacts',
    name: '文化探索者',
    description: '收集1个文化图鉴',
    target: 1,
    reward: {
      civilization_exp: 200,
      stamina: 15
    },
    icon: '/img/page_icons/collection_item.png'
  },
  {
    id: 'collect_atlas_5',
    type: 'collect_artifacts',
    name: '文化收藏家',
    description: '收集5个文化图鉴',
    target: 5,
    reward: {
      civilization_exp: 800,
      stamina: 40
    },
    icon: '/img/page_icons/collection_item.png'
  },
  
  // 保护遗迹任务 (1, 5)
  {
    id: 'protect_monument_1',
    type: 'protect_monuments',
    name: '遗迹守护者',
    description: '保护1个文明古迹',
    target: 1,
    reward: {
      civilization_exp: 150,
      stamina: 10
    },
    icon: '/img/page_icons/vatican_cities.png'
  },
  {
    id: 'protect_monument_5',
    type: 'protect_monuments',
    name: '文明守护者',
    description: '保护5个文明古迹',
    target: 5,
    reward: {
      civilization_exp: 600,
      stamina: 35
    },
    icon: '/img/page_icons/vatican_cities.png'
  }
]

/**
 * 生成每日任务
 * 每天随机选择不同难度的任务
 */
export function generateDailyTasks() {
  const selectedTasks = []
  const taskTypes = ['catch_thieves', 'clean_garbage', 'collect_artifacts', 'protect_monuments']
  
  // 每种类型随机选择一个难度
  taskTypes.forEach(type => {
    const tasksOfType = DAILY_TASK_TEMPLATES.filter(task => task.type === type)
    const randomTask = tasksOfType[Math.floor(Math.random() * tasksOfType.length)]
    selectedTasks.push({
      ...randomTask,
      task_id: `daily_${type}_${Date.now()}`,
      current_progress: 0,
      is_completed: false,
      is_claimed: false,
      created_at: new Date().toISOString()
    })
  })
  
  return selectedTasks
}

/**
 * 检查任务进度更新
 */
export function checkTaskProgress(taskType, currentCount) {
  return {
    type: taskType,
    current_progress: currentCount,
    is_completed: false // 由调用方根据target判断
  }
}