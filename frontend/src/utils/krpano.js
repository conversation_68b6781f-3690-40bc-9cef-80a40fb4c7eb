/**
 * krpano管理类
 * 负责与krpano进行交互
 */
class KrpanoManager {
  constructor() {
    this.krpano = null
    this.ready = false
    this.callbacks = new Map()
    
    // 提前设置全局回调函数，防止krpano在初始化前调用
    this.setupGlobalCallbacks()
  }

  /**
   * 初始化krpano
   * @param {string} target - 目标DOM元素ID
   * @param {string} xml - XML配置文件路径
   * @param {object} options - 其他选项
   */
  init(target, xml = '/tour.xml', options = {}) {
    return new Promise((resolve, reject) => {
      if (!window.embedpano) {
        reject(new Error('krpano.js未加载'))
        return
      }

      const defaultOptions = {
        xml,
        target,
        html5: 'auto',
        mobilescale: 1.0,
        passQueryParameters: 'startscene,startlookat',
        initvars: {
          design: 'flat'
        },
        onready: (krpano) => {
          this.krpano = krpano
          this.ready = true
          this.setupGlobalCallbacks()
          resolve(krpano)
        }
      }

      window.embedpano({ ...defaultOptions, ...options })
    })
  }

  /**
   * 设置全局回调函数
   */
  setupGlobalCallbacks() {
    // 将回调函数挂载到window对象上，供krpano调用
    window.onThiefClicked = (name) => {
      this.trigger('thiefClicked', name)
    }

    window.onGarbageClicked = (name) => {
      this.trigger('garbageClicked', name)
    }

    window.onTreasureClicked = (name) => {
      this.trigger('treasureClicked', name)
    }

    window.onTimegateClicked = () => {
      this.trigger('timegateClicked')
    }

    // krpano XML中调用的新全景加载回调
    window.appVueOnNewPanoLoaded = () => {
      this.trigger('newPanoLoaded')
    }

    // 场景切换回调
    window.executeSceneTransition = () => {
      this.trigger('sceneTransition')
    }

    // 进入下一关回调
    window.enterNextLevel = (gateName) => {
      this.trigger('enterNextLevel', gateName)
    }

  }

  /**
   * 注册事件监听
   */
  on(event, callback) {
    if (!this.callbacks.has(event)) {
      this.callbacks.set(event, [])
    }
    this.callbacks.get(event).push(callback)
  }

  /**
   * 触发事件
   */
  trigger(event, ...args) {
    if (this.callbacks.has(event)) {
      this.callbacks.get(event).forEach(callback => {
        callback(...args)
      })
    }
  }

  /**
   * 调用krpano方法
   */
  call(action, ...args) {
    if (!this.ready || !this.krpano) {
      console.warn('krpano未准备就绪')
      return
    }
    return this.krpano.call(action, ...args)
  }

  /**
   * 获取krpano变量
   */
  get(variable) {
    if (!this.ready || !this.krpano) {
      console.warn('krpano未准备就绪')
      return null
    }
    return this.krpano.get(variable)
  }

  /**
   * 设置krpano变量
   */
  set(variable, value) {
    if (!this.ready || !this.krpano) {
      console.warn('krpano未准备就绪')
      return
    }
    return this.krpano.set(variable, value)
  }

  /**
   * 加载场景
   */
  loadScene(sceneName, variables = null) {
    const vars = variables ? `, ${variables}` : ''
    return this.call(`loadscene(${sceneName}${vars})`)
  }

  /**
   * 添加热点
   */
  addHotspot(name, config) {
    this.call(`addhotspot(${name})`)
    
    Object.keys(config).forEach(key => {
      this.set(`hotspot[${name}].${key}`, config[key])
    })
  }

  /**
   * 移除热点
   */
  removeHotspot(name) {
    return this.call(`removehotspot(${name})`)
  }

  /**
   * 获取热点信息
   */
  getHotspot(name) {
    const hotspot = {}
    const props = ['ath', 'atv', 'scale', 'alpha', 'visible', 'url']
    
    props.forEach(prop => {
      hotspot[prop] = this.get(`hotspot[${name}].${prop}`)
    })
    
    return hotspot
  }

  /**
   * 获取所有热点
   */
  getAllHotspots() {
    const count = this.get('hotspot.count')
    const hotspots = []
    
    for (let i = 0; i < count; i++) {
      const name = this.get(`hotspot[${i}].name`)
      if (name) {
        hotspots.push({
          name,
          ...this.getHotspot(name)
        })
      }
    }
    
    return hotspots
  }


  /**
   * 获取所有小偷热点
   */
  getThiefHotspots() {
    const allHotspots = this.getAllHotspots()
    return allHotspots.filter(h => 
      h.name.includes('thief')
    )
  }

  /**
   * 获取所有垃圾热点
   */
  getGarbageHotspots() {
    const allHotspots = this.getAllHotspots()
    return allHotspots.filter(h => 
      h.name.includes('garbage')
    )
  }

  /**
   * 雷达功能 - 寻找最近的目标并定位
   */
  useRadar() {
    const currentView = this.getView()
    const thiefHotspots = this.getThiefHotspots()
    const garbageHotspots = this.getGarbageHotspots()
    const allTargets = [...thiefHotspots, ...garbageHotspots]
    if (allTargets.length === 0) {
      return {
        success: false,
        message: '当前场景没有发现目标'
      }
    }

    // 找到最近的目标
    let closestTarget = null
    let minDistance = Infinity

    allTargets.forEach(target => {
      const hDiff = Math.abs(currentView.hlookat - parseFloat(target.ath))
      const vDiff = Math.abs(currentView.vlookat - parseFloat(target.atv))
      const distance = Math.sqrt(hDiff * hDiff + vDiff * vDiff)
      
      if (distance < minDistance) {
        minDistance = distance
        closestTarget = target
      }
    })

    if (closestTarget) {
      // 显示雷达波动画
      this.showRadarWave(closestTarget.ath, closestTarget.atv)
      
      // 平滑转向目标
      this.lookToTarget(closestTarget.ath, closestTarget.atv)
      
      return {
        success: true,
        target: closestTarget,
        distance: minDistance,
        message: `发现目标：${closestTarget.name.includes('thief') ? '小偷' : '垃圾'}`
      }
    }

    return {
      success: false,
      message: '未发现有效目标'
    }
  }

  /**
   * 显示雷达波动画
   */
  showRadarWave(ath, atv) {
    // 创建雷达波效果
    const radarWaveName = `radar_wave_${Date.now()}`
    const radarMarkerName = `radar_marker_${Date.now()}`
    
    // 创建雷达波热点（使用现有图片作为雷达波）
    this.addHotspot(radarWaveName, {
      url: '/img/guaqia_cities_BG.png',  // 使用现有图片作为雷达波
      ath: ath,
      atv: atv,
      scale: 0.1,
      alpha: 0.3,
      zorder: 98
    })
    
    // 创建标记点热点
    this.addHotspot(radarMarkerName, {
      url: '/img/page_icons/thieficon-2.png',  // 使用小偷图标作为标记
      ath: ath,
      atv: atv,
      scale: 0.5,
      alpha: 0,
      zorder: 99
    })
    
    // 雷达波扩散动画
    this.call(`
      tween(hotspot[${radarWaveName}].scale, 3.0, 2.0, easeout);
      tween(hotspot[${radarWaveName}].alpha, 0, 2.0, default,
        removehotspot(${radarWaveName});
      );
    `)
    
    // 标记点闪烁动画
    this.call(`
      tween(hotspot[${radarMarkerName}].scale, 1.2, 0.5, easeout);
      tween(hotspot[${radarMarkerName}].alpha, 1.0, 0.5, default,
        tween(hotspot[${radarMarkerName}].alpha, 0.3, 0.5);
        tween(hotspot[${radarMarkerName}].alpha, 1.0, 0.5);
        tween(hotspot[${radarMarkerName}].alpha, 0.3, 0.5);
        delayedcall(2.0,
          tween(hotspot[${radarMarkerName}].alpha, 0, 1.0, default,
            removehotspot(${radarMarkerName});
          );
        );
      );
    `)
  }

  /**
   * 平滑转向目标
   */
  lookToTarget(ath, atv, fov = null) {
    const duration = 1.5  // 转向持续时间
    
    if (fov !== null) {
      this.call(`
        tween(view.hlookat, ${ath}, ${duration}, easeout);
        tween(view.vlookat, ${atv}, ${duration}, easeout);
        tween(view.fov, ${fov}, ${duration}, easeout);
      `)
    } else {
      this.call(`
        tween(view.hlookat, ${ath}, ${duration}, easeout);
        tween(view.vlookat, ${atv}, ${duration}, easeout);
      `)
    }
  }

  /**
   * 放大镜功能 - 放大当前视野范围内的目标
   */
  useMagnifier(range = 60) {
    const currentView = this.getView()
    const thiefHotspots = this.getThiefHotspots()
    const garbageHotspots = this.getGarbageHotspots()
    const allTargets = [...thiefHotspots, ...garbageHotspots]

    if (allTargets.length === 0) {
      return {
        success: false,
        message: '当前场景没有发现目标'
      }
    }

    // 找到视野范围内的目标
    const targetsInView = allTargets.filter(target => {
      const hDiff = Math.abs(currentView.hlookat - parseFloat(target.ath))
      const vDiff = Math.abs(currentView.vlookat - parseFloat(target.atv))
      const distance = Math.sqrt(hDiff * hDiff + vDiff * vDiff)
      return distance <= range
    })

    if (targetsInView.length === 0) {
      // 如果当前视野没有目标，放大显示最近的几个目标
      const sortedTargets = allTargets
        .map(target => ({
          ...target,
          distance: (() => {
            // 使用简化的距离计算
            const hDiff = Math.abs(currentView.hlookat - parseFloat(target.ath))
            const vDiff = Math.abs(currentView.vlookat - parseFloat(target.atv))
            return Math.sqrt(hDiff * hDiff + vDiff * vDiff)
          })()
        }))
        .sort((a, b) => a.distance - b.distance)
        .slice(0, 3)  // 最近的3个目标

      this.magnifyTargets(sortedTargets)
      this.showMagnifierEffect()

      return {
        success: true,
        targets: sortedTargets,
        message: `发现 ${sortedTargets.length} 个目标，已高亮显示`
      }
    } else {
      // 放大视野内的目标
      this.magnifyTargets(targetsInView)
      this.showMagnifierEffect()

      return {
        success: true,
        targets: targetsInView,
        message: `当前视野发现 ${targetsInView.length} 个目标`
      }
    }
  }

  /**
   * 放大指定目标
   */
  magnifyTargets(targets) {
    targets.forEach(target => {
      // 获取当前的放大scale
      const currentScale = this.get(`hotspot[${target.name}].scale`)
      // 放大目标
      this.call(`
        tween(hotspot[${target.name}].scale, ${currentScale * 1.5}, 0.8, easeout);
        
        // 添加高亮效果
        tween(hotspot[${target.name}].alpha, 1.0, 0.3);
        
        // 6秒后恢复原状
        delayedcall(6.0,
          tween(hotspot[${target.name}].scale, ${currentScale}, 1.0, easein);
        );
      `)

      // 添加闪烁边框效果
      const borderName = `magnifier_border_${target.name}_${Date.now()}`
      this.addHotspot(borderName, {
        url: '/img/frame_ani/bigger_ani.png',  // 使用现有图片作为边框
        ath: target.ath,
        atv: target.atv,
        scale: 2,
        alpha: 0,
        zorder: 99,
        onloaded: "do_crop_animation(204, 226, 10);"
      })

      // 边框闪烁动画
      this.call(`
        tween(hotspot[${borderName}].alpha, 0.8, 0.5);
        delayedcall(0.5,
          tween(hotspot[${borderName}].alpha, 0.3, 0.5);
          delayedcall(0.5,
            tween(hotspot[${borderName}].alpha, 0.8, 0.5);
            delayedcall(0.5,
              tween(hotspot[${borderName}].alpha, 0.3, 0.5);
              delayedcall(0.5,
                tween(hotspot[${borderName}].alpha, 0, 1.0, default,
                  removehotspot(${borderName});
                );
              );
            );
          );
        );
      `)
    })
  }

  /**
   * 显示放大镜视野效果
   */
  showMagnifierEffect() {
    const currentView = this.getView()
    
    // 创建放大镜视野圈
    const magnifierName = `magnifier_view_${Date.now()}`
    this.addHotspot(magnifierName, {
      url: '/img/guaqia_cities_BG.png',  // 使用现有图片作为放大镜圈
      ath: currentView.hlookat,
      atv: currentView.vlookat,
      scale: 0.1,
      alpha: 0,
      zorder: 102
    })

    // 放大镜视野动画
    this.call(`
      tween(hotspot[${magnifierName}].scale, 4.0, 1.0, easeout);
      tween(hotspot[${magnifierName}].alpha, 0.6, 0.5, default,
        delayedcall(2.0,
          tween(hotspot[${magnifierName}].alpha, 0, 1.0, default,
            removehotspot(${magnifierName});
          );
        );
      );
    `)

    // 轻微缩放视野以突出放大效果
    const originalFov = currentView.fov
    const zoomedFov = Math.max(30, originalFov - 20)
    
    this.call(`
      tween(view.fov, ${zoomedFov}, 0.8, easeout);
      delayedcall(6.0,
        tween(view.fov, ${originalFov}, 1.0, easein);
      );
    `)
  }

  /**
   * 获取视角信息
   */
  getView() {
    if (!this.ready || !this.krpano) {
      console.warn('krpano未准备就绪，ready:', this.ready, 'krpano:', !!this.krpano)
      return { hlookat: null, vlookat: null, fov: null }
    }
    
    try {
      // 尝试多种方式获取视角
      let hlookat = this.get('view.hlookat')
      let vlookat = this.get('view.vlookat') 
      let fov = this.get('view.fov')
      
      // 如果标准方式失败，尝试备用方式
      if (hlookat === null || hlookat === undefined) {
        hlookat = this.krpano.get('hlookat') || this.krpano.get('view.h') || 0
      }
      if (vlookat === null || vlookat === undefined) {
        vlookat = this.krpano.get('vlookat') || this.krpano.get('view.v') || 0  
      }
      if (fov === null || fov === undefined) {
        fov = this.krpano.get('fov') || this.krpano.get('view.fov') || 90
      }
      
      console.log('获取当前视角:', { hlookat, vlookat, fov })
      
      return {
        hlookat: parseFloat(hlookat) || 0,
        vlookat: parseFloat(vlookat) || 0,
        fov: parseFloat(fov) || 90
      }
    } catch (error) {
      console.error('获取视角时出错:', error)
      return { hlookat: 0, vlookat: 0, fov: 90 }
    }
  }

  /**
   * 检查krpano是否准备就绪
   */
  isReady() {
    return this.ready && this.krpano && typeof this.krpano.get === 'function'
  }

  /**
   * 设置视角
   */
  lookTo(hlookat, vlookat, fov = null) {
    if (fov !== null) {
      this.call(`lookto(${hlookat}, ${vlookat}, ${fov})`)
    } else {
      this.call(`lookto(${hlookat}, ${vlookat})`)
    }
  }

  /**
   * 缩放
   */
  zoomIn() {
    const currentFov = this.get('view.fov')
    const newFov = Math.max(30, currentFov - 10)
    this.call(`tween(view.fov, ${newFov}, 0.5)`)
  }

  zoomOut() {
    const currentFov = this.get('view.fov')
    const newFov = Math.min(120, currentFov + 10)
    this.call(`tween(view.fov, ${newFov}, 0.5)`)
  }

  /**
   * 显示雷达扫描结果
   */
  showRadarResults(hotspots) {
    if (!hotspots || hotspots.length === 0) {
      return
    }

    hotspots.forEach((hotspot, index) => {
      setTimeout(() => {
        // 在热点位置显示雷达标记
        this.call(`add_radar_ping(${hotspot.ath}, ${hotspot.atv})`)
      }, index * 300)
    })
  }
}

// 导出单例
export default new KrpanoManager()