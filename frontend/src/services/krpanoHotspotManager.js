/**
 * 基于krpano内置功能的高性能热点管理器
 * 使用krpano的距离计算和事件系统，而不是JavaScript计算
 */

class KrpanoHotspotManager {
  constructor() {
    this.hotspots = new Map()
    this.visibilityRules = new Map()
    this.isInitialized = false
    this.currentScene = null
  }

  /**
   * 初始化热点管理器
   */
  async initialize(sceneId) {
    this.currentScene = sceneId
    
    // 注入krpano动作和事件
    await this.injectKrpanoActions()
    
    this.isInitialized = true
    console.log('Krpano热点管理器初始化完成')
  }

  /**
   * 向krpano注入距离计算和LOD管理动作（简化版，XML已包含动作）
   */
  async injectKrpanoActions() {
    // XML文件中已包含所有必要的动作，这里只需要确保它们被激活
    console.log('使用XML中预定义的热点LOD管理动作')
  }





  /**
   * 移除热点（使用krpano内置方法）
   */
  async removeHotspot(hotspotName) {
    const krpanoManager = await import('@/utils/krpano')
    
    try {
      // 使用krpano内置的removehotspot命令
      krpanoManager.default.call(`removehotspot('${hotspotName}');`)
      this.hotspots.delete(hotspotName)
      console.log(`热点已移除: ${hotspotName}`)
    } catch (error) {
      console.error(`移除热点失败: ${hotspotName}`, error)
    }
  }

  /**
   * 清理所有受管理的热点
   */
  async cleanup() {
    const krpanoManager = await import('@/utils/krpano')
    
    // 停止事件监听
    krpanoManager.default.call('cleanup_hotspot_events();')
    
    // 移除所有受管理的热点
    for (const hotspotName of this.hotspots.keys()) {
      await this.removeHotspot(hotspotName)
    }
    
    this.hotspots.clear()
    console.log('热点管理器已清理')
  }
 

}

// 创建单例
export const krpanoHotspotManager = new KrpanoHotspotManager()
export default krpanoHotspotManager