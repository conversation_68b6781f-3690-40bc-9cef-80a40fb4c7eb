/**
 * 文化图鉴收集服务
 */
import { api } from '@/utils/api'
import { useGameStore } from '@/stores/game'

class CollectionsService {
  constructor() {
    this.collectionsCache = new Map()
    this.userStatsCache = null
    this.lastStatsUpdate = null
  }

  /**
   * 获取城市文化图鉴列表
   */
  async getCityCollections(cityId, category = 'all', rarity = 'all', collected = 'all') {
    try {
      // 构建缓存键
      const cacheKey = `${cityId}_${category}_${rarity}_${collected}`
      const cached = this.collectionsCache.get(cacheKey)
      
      if (cached && Date.now() - cached.timestamp < 300000) { // 5分钟缓存
        return { success: true, data: cached.data }
      }

      const response = await api.get(`/collections/city/${cityId}`, {
        params: { category, rarity, collected }
      })
      
      const data = response.data.data
      
      // 更新缓存
      this.collectionsCache.set(cacheKey, {
        data,
        timestamp: Date.now()
      })
      
      return { success: true, data }
    } catch (error) {
      console.error('获取城市图鉴失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 收集文化图鉴
   */
  async collectItem(collectionId, collectedFrom, sourceLocation = null) {
    try {
      const response = await api.post('/collections/collect', {
        collection_id: collectionId,
        collected_from: collectedFrom,
        source_location: sourceLocation
      })
      
      const result = response.data.data
      
      // 更新游戏状态
      const gameStore = useGameStore()
      
      if (result.exp_gained > 0) {
        gameStore.addCivilizationExp(result.exp_gained)
      }
      
      // 更新收集统计
      if (gameStore.userInfo.collections_count !== undefined) {
        gameStore.userInfo.collections_count++
      }
      
      // 清除相关缓存
      this.clearCollectionsCache()
      this.userStatsCache = null
      
      return { success: true, data: result }
    } catch (error) {
      console.error('收集图鉴失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取用户收集统计
   */
  async getUserStats(cityId = null) {
    try {
      // 检查缓存
      if (this.userStatsCache && this.lastStatsUpdate && 
          Date.now() - this.lastStatsUpdate < 60000) { // 1分钟缓存
        return { success: true, data: this.userStatsCache }
      }

      const response = await api.get('/collections/user/stats', {
        params: cityId ? { city_id: cityId } : {}
      })
      
      const data = response.data.data
      
      // 更新缓存
      this.userStatsCache = data
      this.lastStatsUpdate = Date.now()
      
      return { success: true, data }
    } catch (error) {
      console.error('获取收集统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取随机图鉴掉落（用于热点交互）
   */
  async getRandomDrop(cityId, sourceType = 'thief', rarityBoost = 1.0) {
    try {
      const response = await api.get(`/collections/random-drop/${cityId}`, {
        params: { source_type: sourceType, rarity_boost: rarityBoost }
      })
      
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取随机掉落失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 处理热点收集时的图鉴掉落
   */
  async handleHotspotDrop(hotspotType, hotspotName, cityId = 'beijing') {
    try {
      // 根据热点类型确定掉落概率和稀有度加成
      let dropChance = 0
      let rarityBoost = 1.0
      
      switch (hotspotType) {
        case 'thief':
          dropChance = 0.15 // 15%掉落概率
          rarityBoost = 1.0
          break
        case 'garbage':
          dropChance = 0.10 // 10%掉落概率
          rarityBoost = 0.8
          break
        case 'treasure':
          dropChance = 0.50 // 50%掉落概率
          rarityBoost = 2.0 // 更高稀有度
          break
        case 'boss':
          dropChance = 0.80 // 80%掉落概率
          rarityBoost = 3.0 // 最高稀有度
          break
      }
      
      // 判断是否掉落
      if (Math.random() > dropChance) {
        return { hasDrop: false }
      }

      // 获取随机掉落
      const dropResult = await this.getRandomDrop(cityId, hotspotType, rarityBoost)
      
      if (!dropResult.success || !dropResult.data) {
        return { hasDrop: false }
      }

      const dropData = dropResult.data
      
      // 自动收集掉落的图鉴
      if (dropData.collection_id && !dropData.already_collected) {
        const collectResult = await this.collectItem(
          dropData.collection_id, 
          hotspotType, 
          hotspotName
        )
        
        if (collectResult.success) {
          return {
            hasDrop: true,
            collection: dropData,
            expGained: collectResult.data.exp_gained,
            newCollect: true
          }
        }
      }
      
      return {
        hasDrop: true,
        collection: dropData,
        newCollect: false,
        expGained: 0
      }
      
    } catch (error) {
      console.error('处理热点掉落失败:', error)
      return { hasDrop: false, error: error.message }
    }
  }

  /**
   * 获取稀有度颜色
   */
  getRarityColor(rarity) {
    const colors = {
      common: '#8B7355',    // 灰棕色
      rare: '#3366FF',      // 蓝色
      epic: '#9933FF',      // 紫色
      legendary: '#FF9933'  // 橙色
    }
    return colors[rarity] || colors.common
  }

  /**
   * 获取稀有度中文名称
   */
  getRarityName(rarity) {
    const names = {
      common: '普通',
      rare: '稀有', 
      epic: '史诗',
      legendary: '传说'
    }
    return names[rarity] || '普通'
  }

  /**
   * 获取分类中文名称
   */
  getCategoryName(category) {
    const names = {
      food: '美食',
      architecture: '建筑',
      culture: '文化',
      art: '艺术',
      history: '历史',
      nature: '自然'
    }
    return names[category] || '其他'
  }

  /**
   * 计算收集完成度
   */
  calculateCompletionRate(collected, total) {
    if (total === 0) return 0
    return Math.round((collected / total) * 100)
  }

  /**
   * 获取收集成就
   */
  getCollectionAchievements(stats) {
    const achievements = []
    
    if (stats.total_count >= 10) {
      achievements.push({ name: '初级收藏家', icon: '🏆' })
    }
    
    if (stats.total_count >= 50) {
      achievements.push({ name: '文化学者', icon: '🎓' })
    }
    
    if (stats.total_count >= 100) {
      achievements.push({ name: '文物专家', icon: '🏛️' })
    }
    
    if (stats.completion_rate >= 100) {
      achievements.push({ name: '完美收集', icon: '⭐' })
    }
    
    return achievements
  }

  /**
   * 离线模式的图鉴收集（本地模拟）
   */
  async offlineCollectionDrop(hotspotType, hotspotName, cityId = 'beijing') {
    try {
      const gameStore = useGameStore()
      
      // 离线模式的掉落概率
      let dropChance = 0
      let expBonus = 0
      
      switch (hotspotType) {
        case 'thief':
          dropChance = 0.12 // 12%
          expBonus = 10
          break
        case 'garbage':
          dropChance = 0.08 // 8%
          expBonus = 8
          break
        case 'treasure':
          dropChance = 0.40 // 40%
          expBonus = 25
          break
        case 'boss':
          dropChance = 0.70 // 70%
          expBonus = 50
          break
      }
      
      // 判断是否掉落
      if (Math.random() > dropChance) {
        return { hasDrop: false }
      }

      // 生成模拟的图鉴数据
      const rarityRoll = Math.random()
      let rarity = 'common'
      
      if (rarityRoll < 0.05) rarity = 'legendary'
      else if (rarityRoll < 0.15) rarity = 'epic'
      else if (rarityRoll < 0.35) rarity = 'rare'
      
      const collection = this.generateOfflineCollection(cityId, rarity)
      
      // 给予经验奖励
      const finalExp = expBonus + this.getRarityExpBonus(rarity)
      gameStore.addCivilizationExp(finalExp)
      
      // 更新收集统计
      if (gameStore.userInfo.collections_count !== undefined) {
        gameStore.userInfo.collections_count++
      }
      
      console.log('离线模式图鉴掉落:', collection)
      
      return {
        hasDrop: true,
        collection,
        expGained: finalExp,
        newCollect: true
      }
      
    } catch (error) {
      console.error('离线图鉴掉落失败:', error)
      return { hasDrop: false, error: error.message }
    }
  }

  /**
   * 生成离线模式的图鉴数据
   */
  generateOfflineCollection(cityId, rarity) {
    const beijingCollections = [
      { name: '北京烤鸭', category: 'food', description: '北京最著名的传统美食' },
      { name: '景泰蓝', category: 'art', description: '北京传统工艺品' },
      { name: '天安门', category: 'architecture', description: '北京标志性建筑' },
      { name: '京剧脸谱', category: 'culture', description: '传统戏曲艺术' },
      { name: '故宫红墙', category: 'architecture', description: '紫禁城的经典色彩' }
    ]
    
    const randomItem = beijingCollections[Math.floor(Math.random() * beijingCollections.length)]
    
    return {
      id: Date.now(),
      name: randomItem.name,
      category: randomItem.category,
      rarity,
      description: randomItem.description,
      image_url: `/collections/${cityId}/${randomItem.name.toLowerCase()}.jpg`,
      cultural_value: `${randomItem.description}，具有重要的文化价值`,
      exp_reward: this.getRarityExpBonus(rarity)
    }
  }

  /**
   * 获取稀有度经验加成
   */
  getRarityExpBonus(rarity) {
    const bonus = {
      common: 5,
      rare: 15,
      epic: 30,
      legendary: 60
    }
    return bonus[rarity] || 5
  }

  /**
   * 清除图鉴缓存
   */
  clearCollectionsCache(cityId = null) {
    if (cityId) {
      // 清除特定城市的缓存
      for (const key of this.collectionsCache.keys()) {
        if (key.startsWith(`${cityId}_`)) {
          this.collectionsCache.delete(key)
        }
      }
    } else {
      this.collectionsCache.clear()
    }
  }

  /**
   * 清除用户统计缓存
   */
  clearStatsCache() {
    this.userStatsCache = null
    this.lastStatsUpdate = null
  }
}

// 创建单例实例
export const collectionsService = new CollectionsService()

// 默认导出
export default collectionsService