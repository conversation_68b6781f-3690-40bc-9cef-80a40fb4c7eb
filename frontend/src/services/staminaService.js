/**
 * 体力系统服务
 */
import { api } from '@/utils/api'
import { useGameStore } from '@/stores/game'

class StaminaService {
  constructor() {
    this.recoveryInterval = null
    this.lastStatusUpdate = null
    this.statusCache = null
    this.recoveryRate = 1 // 每分钟恢复1点体力
    this.recoveryIntervalMs = 60000 // 1分钟间隔
  }

  /**
   * 获取用户体力状态
   */
  async getStaminaStatus() {
    try {
      // 检查缓存（30秒内有效）
      if (this.statusCache && this.lastStatusUpdate && 
          Date.now() - this.lastStatusUpdate < 30000) {
        return { success: true, data: this.statusCache }
      }

      const response = await api.get('/user/stamina/status')
      const data = response.data.data
      
      // 更新缓存
      this.statusCache = data
      this.lastStatusUpdate = Date.now()
      
      // 更新游戏状态
      const gameStore = useGameStore()
      gameStore.stamina = data.current_stamina
      gameStore.maxStamina = data.max_stamina
      
      return { success: true, data }
    } catch (error) {
      console.error('获取体力状态失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 消耗体力
   */
  async consumeStamina(amount, reason, sourceId = null) {
    try {
      const response = await api.post('/user/stamina/consume', {
        amount,
        reason,
        source_id: sourceId
      })
      
      const result = response.data.data
      
      // 更新游戏状态
      const gameStore = useGameStore()
      gameStore.stamina = result.remaining_stamina
      
      // 清除缓存
      this.clearStatusCache()
      
      return { success: true, data: result }
    } catch (error) {
      console.error('消耗体力失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 恢复体力
   */
  async recoverStamina(recoveryType = 'ad_reward', adCompletionToken = null, amount = 20) {
    try {
      const response = await api.post('/user/stamina/recover', {
        recovery_type: recoveryType,
        ad_completion_token: adCompletionToken,
        amount
      })
      
      const result = response.data.data
      
      // 更新游戏状态
      const gameStore = useGameStore()
      gameStore.stamina = result.new_stamina
      
      // 清除缓存
      this.clearStatusCache()
      
      return { success: true, data: result }
    } catch (error) {
      console.error('恢复体力失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 触发自然回复
   */
  async triggerNaturalRecovery() {
    try {
      const response = await api.post('/user/stamina/natural-recovery')
      const result = response.data.data
      
      // 更新游戏状态
      const gameStore = useGameStore()
      if (result.recovered_amount > 0) {
        gameStore.stamina = result.new_stamina
      }
      
      // 清除缓存
      this.clearStatusCache()
      
      return { success: true, data: result }
    } catch (error) {
      console.error('自然回复失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取体力回复信息
   */
  async getRecoveryInfo() {
    try {
      const response = await api.get('/user/stamina/recovery-info')
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取回复信息失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 开始自动体力回复（客户端定时器）
   */
  startAutoRecovery() {
    // 清除现有定时器
    this.stopAutoRecovery()
    
    // 设置新的定时器
    this.recoveryInterval = setInterval(async () => {
      try {
        await this.triggerNaturalRecovery()
      } catch (error) {
        console.warn('自动体力回复失败:', error)
      }
    }, this.recoveryIntervalMs)
    
    console.log('体力自动回复已开始')
  }

  /**
   * 停止自动体力回复
   */
  stopAutoRecovery() {
    if (this.recoveryInterval) {
      clearInterval(this.recoveryInterval)
      this.recoveryInterval = null
      console.log('体力自动回复已停止')
    }
  }

  /**
   * 检查是否有足够体力执行操作
   */
  async checkStaminaRequirement(requiredStamina, actionName = '该操作') {
    try {
      const statusResult = await this.getStaminaStatus()
      if (!statusResult.success) {
        return { sufficient: false, error: '无法获取体力状态' }
      }

      const currentStamina = statusResult.data.current_stamina
      
      if (currentStamina < requiredStamina) {
        return {
          sufficient: false,
          current: currentStamina,
          required: requiredStamina,
          deficit: requiredStamina - currentStamina,
          message: `体力不足！${actionName}需要${requiredStamina}点体力，当前仅有${currentStamina}点`
        }
      }

      return {
        sufficient: true,
        current: currentStamina,
        required: requiredStamina
      }
    } catch (error) {
      console.error('检查体力需求失败:', error)
      return { sufficient: false, error: error.message }
    }
  }

  /**
   * 获取操作的体力消耗配置
   */
  getActionStaminaCost(actionType) {
    const costs = {
      catch_thief: 2,
      clean_garbage: 1,
      attack_boss: 5,
      monument_challenge: 3,
      treasure_box: 2,
      radar_scan: 1
    }
    return costs[actionType] || 1
  }

  /**
   * 计算体力恢复时间
   */
  calculateRecoveryTime(currentStamina, maxStamina, recoveryRate = 1) {
    const staminaNeeded = maxStamina - currentStamina
    if (staminaNeeded <= 0) {
      return { minutes: 0, seconds: 0, totalSeconds: 0 }
    }

    const totalMinutes = Math.ceil(staminaNeeded / recoveryRate)
    const totalSeconds = totalMinutes * 60
    
    return {
      minutes: totalMinutes,
      seconds: 0,
      totalSeconds,
      staminaNeeded
    }
  }

  /**
   * 格式化体力回复时间显示
   */
  formatRecoveryTime(totalSeconds) {
    if (totalSeconds <= 0) {
      return '已满'
    }

    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60

    if (hours > 0) {
      return `${hours}时${minutes}分`
    } else if (minutes > 0) {
      return `${minutes}分${seconds}秒`
    } else {
      return `${seconds}秒`
    }
  }

  /**
   * 获取体力状态颜色
   */
  getStaminaStatusColor(current, max) {
    const percentage = (current / max) * 100
    
    if (percentage >= 80) return '#52C41A' // 绿色
    if (percentage >= 50) return '#FA8C16' // 橙色
    if (percentage >= 20) return '#FAAD14' // 黄色
    return '#FF4D4F' // 红色
  }

  /**
   * 获取体力状态文本
   */
  getStaminaStatusText(current, max) {
    const percentage = (current / max) * 100
    
    if (percentage >= 90) return '充沛'
    if (percentage >= 70) return '良好'
    if (percentage >= 50) return '一般'
    if (percentage >= 30) return '较低'
    if (percentage >= 10) return '不足'
    return '耗尽'
  }

  /**
   * 离线模式的体力系统（本地模拟）
   */
  async offlineStaminaSystem() {
    try {
      const gameStore = useGameStore()
      
      // 模拟体力状态
      const data = {
        current_stamina: gameStore.stamina || 100,
        max_stamina: gameStore.maxStamina || 100,
        recovery_rate: 1,
        next_recovery_in: 60,
        can_recover: true
      }

      return { success: true, data }
    } catch (error) {
      console.error('离线体力系统失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 离线模式消耗体力
   */
  async offlineConsumeStamina(amount, reason) {
    try {
      const gameStore = useGameStore()
      
      if (gameStore.stamina < amount) {
        return { 
          success: false, 
          error: `体力不足！需要${amount}点体力，当前仅有${gameStore.stamina}点` 
        }
      }

      gameStore.addStamina(-amount)
      
      const result = {
        consumed_amount: amount,
        remaining_stamina: gameStore.stamina,
        reason
      }

      console.log('离线消耗体力:', result)
      return { success: true, data: result }
    } catch (error) {
      console.error('离线消耗体力失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 离线模式恢复体力
   */
  async offlineRecoverStamina(amount = 20, recoveryType = 'ad_reward') {
    try {
      const gameStore = useGameStore()
      
      const oldStamina = gameStore.stamina
      gameStore.addStamina(amount)
      
      const result = {
        recovery_type: recoveryType,
        recovered_amount: amount,
        old_stamina: oldStamina,
        new_stamina: gameStore.stamina,
        is_full: gameStore.stamina >= gameStore.maxStamina
      }

      console.log('离线恢复体力:', result)
      return { success: true, data: result }
    } catch (error) {
      console.error('离线恢复体力失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 清除状态缓存
   */
  clearStatusCache() {
    this.statusCache = null
    this.lastStatusUpdate = null
  }
}

// 创建单例实例
export const staminaService = new StaminaService()

// 默认导出
export default staminaService