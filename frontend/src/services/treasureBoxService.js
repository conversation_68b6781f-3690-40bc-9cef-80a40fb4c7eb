/**
 * 宝箱系统服务
 */
import { api } from '@/utils/api'

class TreasureBoxService {
  constructor() {
    this.cache = new Map()
  }

  /**
   * 获取用户宝箱列表
   */
  async getUserTreasureBoxes(status = null, limit = 20, offset = 0) {
    try {
      const response = await api.treasure.getUserBoxes(status, limit, offset)
      return { success: true, data: response.data }
    } catch (error) {
      console.error('获取宝箱列表失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 开启宝箱
   */
  async openTreasureBox(boxId, sourceAction, useAdDouble = false, context = null) {
    try {
      const response = await api.treasure.openBox(boxId, sourceAction, useAdDouble, context)
      return { success: true, data: response.data }
    } catch (error) {
      console.error('开启宝箱失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 领取宝箱奖励
   */
  async claimTreasureBoxReward(boxId, useAdDouble = false) {
    try {
      const response = await api.treasure.claimReward(boxId, useAdDouble)
      return { success: true, data: response.data }
    } catch (error) {
      console.error('领取宝箱奖励失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取宝箱统计信息
   */
  async getTreasureBoxStatistics(days = 7) {
    try {
      const response = await api.treasure.getStatistics(days)
      return { success: true, data: response.data }
    } catch (error) {
      console.error('获取宝箱统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取宝箱配置
   */
  async getTreasureBoxConfig() {
    try {
      const response = await api.treasure.getConfig()
      return { success: true, data: response.data }
    } catch (error) {
      console.error('获取宝箱配置失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取宝箱掉落率信息
   */
  async getTreasureBoxDropRates() {
    try {
      const response = await api.treasure.getDropRates()
      return { success: true, data: response.data }
    } catch (error) {
      console.error('获取掉落率失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 触发宝箱检查（抓捕小偷）
   */
  async triggerThiefTreasureBox(count = 1, context = null) {
    try {
      const response = await api.treasure.triggerThief(count, context)
      return { success: true, data: response.data }
    } catch (error) {
      console.error('触发小偷宝箱失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 触发宝箱检查（清理垃圾）
   */
  async triggerRubbishTreasureBox(count = 1, context = null) {
    try {
      const response = await api.treasure.triggerRubbish(count, context)
      return { success: true, data: response.data }
    } catch (error) {
      console.error('触发垃圾宝箱失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 触发宝箱检查（古迹问答）
   */
  async triggerMonumentTreasureBox(correctAnswers = 1, context = null) {
    try {
      const response = await api.treasure.triggerMonument(correctAnswers, context)
      return { success: true, data: response.data }
    } catch (error) {
      console.error('触发古迹宝箱失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 触发金宝箱（BOSS击败）
   */
  async triggerBossDefeatTreasureBox(sessionId, cityId, context = null) {
    try {
      const response = await api.treasure.triggerBossDefeat(sessionId, cityId, context)
      return { success: true, data: response.data }
    } catch (error) {
      console.error('触发BOSS宝箱失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear()
  }

  /**
   * 处理宝箱掉落逻辑（根据PRD数值模型）
   */
  async handleTreasureBoxDrop(actionType, count = 1, cityId = null, context = null) {
    try {
      const dropRates = {
        'catch_thief': 6,    // 6%掉落率
        'clean_rubbish': 3,  // 3%掉落率
        'monument_quiz': 15, // 15%掉落率
        'boss_defeat': 100   // 100%必掉
      }

      const dropRate = dropRates[actionType] || 0
      let droppedBoxes = []

      // 根据行为类型触发对应的宝箱检查
      switch (actionType) {
        case 'catch_thief':
          const thiefResult = await this.triggerThiefTreasureBox(count, { ...context, city_id: cityId })
          if (thiefResult.success) {
            droppedBoxes = thiefResult.data.dropped_boxes || []
          }
          break
        case 'clean_rubbish':
          const rubbishResult = await this.triggerRubbishTreasureBox(count, { ...context, city_id: cityId })
          if (rubbishResult.success) {
            droppedBoxes = rubbishResult.data.dropped_boxes || []
          }
          break
        case 'monument_quiz':
          const monumentResult = await this.triggerMonumentTreasureBox(count, { ...context, city_id: cityId })
          if (monumentResult.success) {
            droppedBoxes = monumentResult.data.dropped_boxes || []
          }
          break
        case 'boss_defeat':
          const bossResult = await this.triggerBossDefeatTreasureBox(
            context?.session_id || 'default_session', 
            cityId, 
            context
          )
          if (bossResult.success) {
            droppedBoxes = bossResult.data.dropped_boxes || []
          }
          break
      }

      return {
        success: true,
        data: {
          action_type: actionType,
          count: count,
          drop_rate: dropRate,
          dropped_boxes: droppedBoxes,
          total_drops: droppedBoxes.length
        }
      }
    } catch (error) {
      console.error('处理宝箱掉落失败:', error)
      return { success: false, error: error.message }
    }
  }
}

// 创建单例实例
export const treasureBoxService = new TreasureBoxService()

// 默认导出
export default treasureBoxService