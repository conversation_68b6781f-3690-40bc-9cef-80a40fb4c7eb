/**
 * 文化问答系统服务
 */
import { api } from '@/utils/api'

class CulturalQuizService {
  constructor() {
    this.cache = new Map()
    this.currentQuiz = null
  }

  /**
   * 获取随机问答题目
   */
  async getRandomQuiz(difficulty = null, category = null, excludeAnswered = true) {
    try {
      const response = await api.get('/culture/quiz/random', {
        params: { difficulty, category, exclude_answered: excludeAnswered }
      })
      this.currentQuiz = response.data.data
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取随机题目失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 提交问答答案
   */
  async answerQuiz(quizId, selectedAnswers, timeSpent = null, context = null) {
    try {
      const response = await api.post('/culture/quiz/answer', {
        quiz_id: quizId,
        selected_answers: selectedAnswers,
        time_spent: timeSpent,
        context
      })
      
      const result = response.data.data
      this.currentQuiz = null // 清除当前题目
      
      return { success: true, data: result }
    } catch (error) {
      console.error('提交答案失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 处理古迹问答流程
   */
  async handleMonumentQuiz(monumentId, cityId = null, sessionId = null) {
    try {
      // 1. 获取随机题目
      const quizResult = await this.getRandomQuiz('medium', 'world_heritage', true)
      if (!quizResult.success) {
        throw new Error('获取题目失败')
      }

      const quiz = quizResult.data
      
      return {
        success: true,
        data: {
          quiz,
          monument_id: monumentId,
          city_id: cityId,
          session_id: sessionId,
          instructions: {
            stamina_cost: 5,
            experience_reward: 35,
            boss_damage: 10,
            treasure_box_chance: 15
          }
        }
      }
    } catch (error) {
      console.error('处理古迹问答失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 完成古迹问答（包含综合奖励处理）
   */
  async completeMonumentQuiz(quizId, selectedAnswers, monumentId, cityId = null, sessionId = null, timeSpent = null) {
    try {
      // 1. 提交答案
      const answerResult = await this.answerQuiz(quizId, selectedAnswers, timeSpent, {
        monument_id: monumentId,
        city_id: cityId,
        session_id: sessionId
      })

      if (!answerResult.success) {
        throw new Error('提交答案失败')
      }

      const { correct_count, total_questions, is_perfect } = answerResult.data

      // 2. 通过文明守护者服务处理综合奖励（体力、经验、BOSS血量）
      const { civilizationService } = await import('./civilizationService.js')
      const rewardResult = await civilizationService.monumentQuiz(
        correct_count, 
        sessionId, 
        cityId, 
        monumentId
      )

      return {
        success: true,
        data: {
          quiz_result: answerResult.data,
          reward_result: rewardResult.success ? rewardResult.data : null,
          summary: {
            correct_answers: correct_count,
            total_questions,
            is_perfect,
            experience_gained: rewardResult.data?.experience_result?.experience_gained || 0,
            stamina_consumed: rewardResult.data?.stamina_result?.stamina_consumed || 0,
            boss_damage: rewardResult.data?.boss_result?.damage_dealt || 0
          }
        }
      }
    } catch (error) {
      console.error('完成古迹问答失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear()
    this.currentQuiz = null
  }
}

// 创建单例实例
export const culturalQuizService = new CulturalQuizService()

// 默认导出
export default culturalQuizService