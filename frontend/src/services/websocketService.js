/**
 * WebSocket服务 - 用于H5游戏和小程序之间的实时通信
 */
import { useAuthStore } from '@/stores/auth'
import { useGameStore } from '@/stores/game'

// 消息类型枚举
export const MessageType = {
  // 连接管理
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  HEARTBEAT: 'heartbeat',
  
  // 游戏状态
  GAME_START: 'game_start',
  GAME_END: 'game_end',
  GAME_PAUSE: 'game_pause',
  GAME_RESUME: 'game_resume',
  
  // 游戏数据
  HOTSPOT_COLLECT: 'hotspot_collect',
  BOSS_ATTACK: 'boss_attack',
  SCORE_UPDATE: 'score_update',
  LEVEL_UPDATE: 'level_update',
  RESOURCE_UPDATE: 'resource_update',
  
  // 小程序特定
  MINIPROGRAM_COMMAND: 'miniprogram_command',
  H5_RESPONSE: 'h5_response'
}

// 客户端类型
export const ClientType = {
  H5: 'h5',
  MINIPROGRAM: 'miniprogram'
}

class WebSocketService {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
    this.heartbeatInterval = 30000
    this.heartbeatTimer = null
    this.isConnected = false
    this.isReconnecting = false
    this.messageQueue = []
    this.eventListeners = new Map()
    
    // 配置
    this.config = {
      baseUrl: process.env.NODE_ENV === 'production' 
        ? 'wss://your-domain.com/api/v1/ws' 
        : 'ws://localhost:8000/api/v1/ws',
      clientType: ClientType.H5
    }
  }

  /**
   * 连接WebSocket服务器
   */
  async connect(userId = null) {
    try {
      // 如果已连接，先断开
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.disconnect()
      }

      // 获取用户ID
      if (!userId) {
        const authStore = useAuthStore()
        userId = authStore.user?.user_id || `guest_${Date.now()}`
      }

      // 构建WebSocket URL
      const url = `${this.config.baseUrl}?user_id=${userId}&client_type=${this.config.clientType}`
      
      console.log('正在连接WebSocket:', url)
      
      // 创建WebSocket连接
      this.ws = new WebSocket(url)
      
      // 设置事件监听器
      this.ws.onopen = this.onOpen.bind(this)
      this.ws.onmessage = this.onMessage.bind(this)
      this.ws.onclose = this.onClose.bind(this)
      this.ws.onerror = this.onError.bind(this)
      
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket连接超时'))
        }, 10000)
        
        this.ws.onopen = () => {
          clearTimeout(timeout)
          this.onOpen()
          resolve()
        }
        
        this.ws.onerror = (error) => {
          clearTimeout(timeout)
          this.onError(error)
          reject(error)
        }
      })
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      throw error
    }
  }

  /**
   * 断开WebSocket连接
   */
  disconnect() {
    this.isReconnecting = false
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
    
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    
    this.isConnected = false
    this.reconnectAttempts = 0
    
    console.log('WebSocket已断开连接')
  }

  /**
   * 发送消息
   */
  send(message) {
    if (!this.isConnected) {
      console.warn('WebSocket未连接，消息已加入队列')
      this.messageQueue.push(message)
      return false
    }

    try {
      const messageStr = JSON.stringify(message)
      this.ws.send(messageStr)
      console.log('WebSocket消息已发送:', message)
      return true
    } catch (error) {
      console.error('发送WebSocket消息失败:', error)
      return false
    }
  }

  /**
   * 连接打开事件
   */
  onOpen() {
    console.log('WebSocket连接已建立')
    this.isConnected = true
    this.isReconnecting = false
    this.reconnectAttempts = 0
    
    // 启动心跳
    this.startHeartbeat()
    
    // 发送队列中的消息
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      this.send(message)
    }
    
    // 触发连接事件
    this.emit('connected')
  }

  /**
   * 消息接收事件
   */
  onMessage(event) {
    try {
      const message = JSON.parse(event.data)
      console.log('收到WebSocket消息:', message)
      
      // 处理特殊消息类型
      switch (message.type) {
        case MessageType.CONNECT:
          this.handleConnectMessage(message)
          break
        case MessageType.HEARTBEAT:
          this.handleHeartbeatMessage(message)
          break
        case MessageType.MINIPROGRAM_COMMAND:
          this.handleMiniprogramCommand(message)
          break
        default:
          // 触发消息事件
          this.emit('message', message)
          this.emit(message.type, message)
      }
    } catch (error) {
      console.error('解析WebSocket消息失败:', error)
    }
  }

  /**
   * 连接关闭事件
   */
  onClose(event) {
    console.log('WebSocket连接已关闭:', event.code, event.reason)
    this.isConnected = false
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
    
    // 如果不是主动断开，尝试重连
    if (!this.isReconnecting && event.code !== 1000) {
      this.attemptReconnect()
    }
    
    // 触发断开事件
    this.emit('disconnected', event)
  }

  /**
   * 连接错误事件
   */
  onError(error) {
    console.error('WebSocket错误:', error)
    this.emit('error', error)
  }

  /**
   * 尝试重连
   */
  attemptReconnect() {
    if (this.isReconnecting || this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('达到最大重连次数，停止重连')
      return
    }
    
    this.isReconnecting = true
    this.reconnectAttempts++
    
    console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
    
    setTimeout(() => {
      this.connect().catch(error => {
        console.error('重连失败:', error)
        this.attemptReconnect()
      })
    }, this.reconnectInterval)
  }

  /**
   * 启动心跳
   */
  startHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
    }
    
    this.heartbeatTimer = setInterval(() => {
      this.send({
        type: MessageType.HEARTBEAT,
        timestamp: new Date().toISOString()
      })
    }, this.heartbeatInterval)
  }

  /**
   * 处理连接确认消息
   */
  handleConnectMessage(message) {
    console.log('WebSocket连接确认:', message)
  }

  /**
   * 处理心跳消息
   */
  handleHeartbeatMessage(message) {
    // 心跳响应，无需特殊处理
  }

  /**
   * 处理小程序命令
   */
  handleMiniprogramCommand(message) {
    console.log('收到小程序命令:', message)
    
    const command = message.data
    switch (command.type) {
      case 'pause_game':
        this.emit('miniprogram_pause')
        break
      case 'resume_game':
        this.emit('miniprogram_resume')
        break
      case 'get_game_state':
        this.sendGameState()
        break
      default:
        console.warn('未知的小程序命令:', command.type)
    }
  }

  /**
   * 发送游戏状态给小程序
   */
  sendGameState() {
    const gameStore = useGameStore()
    
    this.send({
      type: MessageType.H5_RESPONSE,
      data: {
        type: 'game_state',
        game_state: {
          civilization_exp: gameStore.userInfo.civilization_exp || 0,
          guardian_level: gameStore.userInfo.guardian_level || 1,
          stamina: gameStore.stamina || 100,
          is_playing: gameStore.isGameStarted,
          timestamp: new Date().toISOString()
        }
      }
    })
  }

  /**
   * 发送游戏开始事件
   */
  sendGameStart(sessionData) {
    this.send({
      type: MessageType.GAME_START,
      data: {
        session_id: sessionData.session_id,
        city_id: sessionData.city_id || 'beijing',
        scene_id: sessionData.scene_id || 'scene_level_1',
        timestamp: new Date().toISOString()
      }
    })
  }

  /**
   * 发送热点收集事件
   */
  sendHotspotCollect(hotspotData) {
    this.send({
      type: MessageType.HOTSPOT_COLLECT,
      data: {
        hotspot_name: hotspotData.name,
        hotspot_type: hotspotData.type,
        reward: hotspotData.reward,
        civilization_exp: hotspotData.civilization_exp || 0,
        stamina_cost: hotspotData.stamina_cost || 0,
        timestamp: new Date().toISOString()
      }
    })
  }

  // 🚫 PRD合规性清理：移除BOSS攻击事件
  // PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少

  /**
   * 发送文明守护者状态更新事件
   */
  sendCivilizationUpdate(civilizationData) {
    this.send({
      type: MessageType.CIVILIZATION_UPDATE,
      data: {
        civilization_exp: civilizationData.civilization_exp,
        guardian_level: civilizationData.guardian_level,
        stamina: civilizationData.stamina,
        collections_count: civilizationData.collections_count || 0,
        timestamp: new Date().toISOString()
      }
    })
  }

  /**
   * 发送游戏结束事件
   */
  sendGameEnd(gameData) {
    this.send({
      type: MessageType.GAME_END,
      data: {
        final_score: gameData.final_score,
        session_duration: gameData.session_duration,
        rewards: gameData.rewards,
        timestamp: new Date().toISOString()
      }
    })
  }

  /**
   * 添加事件监听器
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(callback)
  }

  /**
   * 移除事件监听器
   */
  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event)
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('事件回调执行失败:', error)
        }
      })
    }
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      isReconnecting: this.isReconnecting,
      reconnectAttempts: this.reconnectAttempts,
      readyState: this.ws ? this.ws.readyState : WebSocket.CLOSED
    }
  }
}

// 创建单例实例
export const websocketService = new WebSocketService()

// 默认导出
export default websocketService