/**
 * 新排行榜服务 - 文明守护者系统
 */
import { api } from '@/utils/api'

class RankingService {
  constructor() {
    this.rankingCache = new Map()
    this.cacheExpiry = 300000 // 5分钟缓存
  }

  /**
   * 获取文明经验排行榜
   */
  async getCivilizationExpRanking(limit = 50, cityId = null) {
    try {
      const cacheKey = `civilization_exp_${limit}_${cityId || 'global'}`
      const cached = this.rankingCache.get(cacheKey)
      
      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        return { success: true, data: cached.data }
      }

      const response = await api.get('/ranking/civilization-exp', {
        params: { limit, city_id: cityId }
      })
      
      const data = response.data.data
      
      // 更新缓存
      this.rankingCache.set(cacheKey, {
        data,
        timestamp: Date.now()
      })
      
      return { success: true, data }
    } catch (error) {
      console.error('获取文明经验排行榜失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取图鉴收集排行榜
   */
  async getCollectionsRanking(limit = 50, cityId = null) {
    try {
      const cacheKey = `collections_${limit}_${cityId || 'global'}`
      const cached = this.rankingCache.get(cacheKey)
      
      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        return { success: true, data: cached.data }
      }

      const response = await api.get('/ranking/collections', {
        params: { limit, city_id: cityId }
      })
      
      const data = response.data.data
      
      // 更新缓存
      this.rankingCache.set(cacheKey, {
        data,
        timestamp: Date.now()
      })
      
      return { success: true, data }
    } catch (error) {
      console.error('获取图鉴收集排行榜失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取综合排行榜
   */
  async getCombinedRanking(limit = 50, cityId = null) {
    try {
      const cacheKey = `combined_${limit}_${cityId || 'global'}`
      const cached = this.rankingCache.get(cacheKey)
      
      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        return { success: true, data: cached.data }
      }

      const response = await api.get('/ranking/combined', {
        params: { limit, city_id: cityId }
      })
      
      const data = response.data.data
      
      // 更新缓存
      this.rankingCache.set(cacheKey, {
        data,
        timestamp: Date.now()
      })
      
      return { success: true, data }
    } catch (error) {
      console.error('获取综合排行榜失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取玩家详细信息
   */
  async getPlayerDetails(userId) {
    try {
      const response = await api.get(`/ranking/player/${userId}/guardian-details`)
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取玩家详情失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取当前用户的所有排名
   */
  async getMyRankings() {
    try {
      const response = await api.get('/ranking/my-rank')
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取用户排名失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取全球统计
   */
  async getGlobalStats() {
    try {
      const response = await api.get('/ranking/stats/global')
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取全球统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 刷新排行榜数据
   */
  async refreshRanking(rankingType, cityId = null) {
    try {
      const response = await api.post(`/ranking/refresh/${rankingType}`, 
        cityId ? { city_id: cityId } : {}
      )
      
      // 清除相关缓存
      this.clearRankingCache(rankingType, cityId)
      
      return { success: true, message: response.data.message }
    } catch (error) {
      console.error('刷新排行榜失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取向后兼容的文物收藏者排行榜（适配旧接口）
   */
  async getArtifactCollectors(limit = 50) {
    try {
      // 直接调用新的图鉴收集排行榜
      return await this.getCollectionsRanking(limit)
    } catch (error) {
      console.error('获取文物收藏者排行榜失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取排名颜色（根据排名位置）
   */
  getRankColor(rank) {
    if (rank === 1) return '#FFD700' // 金色
    if (rank === 2) return '#C0C0C0' // 银色
    if (rank === 3) return '#CD7F32' // 铜色
    if (rank <= 10) return '#1890FF' // 蓝色
    if (rank <= 50) return '#52C41A' // 绿色
    return '#8C8C8C' // 灰色
  }

  /**
   * 获取排名图标
   */
  getRankIcon(rank) {
    if (rank === 1) return '🥇'
    if (rank === 2) return '🥈'
    if (rank === 3) return '🥉'
    if (rank <= 10) return '🏆'
    return '📊'
  }

  /**
   * 格式化排名显示
   */
  formatRankDisplay(rank) {
    if (rank <= 3) {
      return `${this.getRankIcon(rank)} 第${rank}名`
    }
    return `第${rank}名`
  }

  /**
   * 获取守望者等级显示信息
   */
  getGuardianLevelDisplay(level) {
    const levelNames = {
      1: { name: '新手守卫', color: '#8C8C8C' },
      2: { name: '学徒护卫', color: '#52C41A' },
      3: { name: '熟练守护者', color: '#1890FF' },
      4: { name: '资深护卫', color: '#722ED1' },
      5: { name: '英勇守卫', color: '#EB2F96' },
      6: { name: '精英守护者', color: '#FA541C' },
      7: { name: '传奇守卫', color: '#FAAD14' },
      8: { name: '传奇守护者', color: '#FA8C16' },
      9: { name: '史诗守卫', color: '#F5222D' },
      10: { name: '文明守护神', color: '#FFD700' }
    }
    
    return levelNames[level] || { name: '未知等级', color: '#8C8C8C' }
  }

  /**
   * 计算综合评分显示
   */
  calculateCombinedScore(civilizationExp, collectionsCount) {
    // 简化的评分计算：文明经验 + 图鉴数量 * 10
    return civilizationExp + (collectionsCount * 10)
  }

  /**
   * 离线模式的排行榜（本地模拟）
   */
  async offlineRanking(rankingType = 'civilization_exp') {
    try {
      // 生成模拟的排行榜数据
      const mockRankings = []
      
      for (let i = 1; i <= 20; i++) {
        const baseScore = Math.floor(Math.random() * 10000) + 1000
        mockRankings.push({
          rank: i,
          user_id: `user_${i}`,
          nickname: `玩家${i}`,
          avatar: `/avatars/default_${i % 5 + 1}.png`,
          civilization_exp: rankingType === 'civilization_exp' ? baseScore : Math.floor(baseScore * 0.8),
          guardian_level: Math.min(10, Math.floor(baseScore / 1000) + 1),
          collections_count: rankingType === 'collections' ? Math.floor(baseScore / 100) : Math.floor(baseScore / 150),
          combined_score: rankingType === 'combined' ? baseScore : baseScore + Math.floor(baseScore / 10)
        })
      }

      // 按相应字段排序
      if (rankingType === 'civilization_exp') {
        mockRankings.sort((a, b) => b.civilization_exp - a.civilization_exp)
      } else if (rankingType === 'collections') {
        mockRankings.sort((a, b) => b.collections_count - a.collections_count)
      } else {
        mockRankings.sort((a, b) => b.combined_score - a.combined_score)
      }

      const data = {
        ranking_type: rankingType,
        city_id: 'global',
        update_time: new Date().toISOString(),
        rankings: mockRankings,
        my_rank: {
          rank: 15,
          civilization_exp: 2500,
          guardian_level: 5,
          collections_count: 25
        }
      }

      console.log('离线排行榜数据:', data)
      return { success: true, data }
    } catch (error) {
      console.error('离线排行榜失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 清除排行榜缓存
   */
  clearRankingCache(rankingType = null, cityId = null) {
    if (rankingType && cityId) {
      // 清除特定类型和城市的缓存
      const cacheKey = `${rankingType}_${cityId || 'global'}`
      this.rankingCache.delete(cacheKey)
    } else if (rankingType) {
      // 清除特定类型的所有缓存
      for (const key of this.rankingCache.keys()) {
        if (key.startsWith(`${rankingType}_`)) {
          this.rankingCache.delete(key)
        }
      }
    } else {
      // 清除所有缓存
      this.rankingCache.clear()
    }
  }

  /**
   * 检查缓存是否有效
   */
  isCacheValid(cacheKey) {
    const cached = this.rankingCache.get(cacheKey)
    return cached && Date.now() - cached.timestamp < this.cacheExpiry
  }
}

// 创建单例实例
export const rankingService = new RankingService()

// 默认导出
export default rankingService