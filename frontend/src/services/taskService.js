/**
 * 每日任务系统服务
 */
import { api } from '@/utils/api'

class TaskService {
  constructor() {
    this.cache = new Map()
  }

  /**
   * 获取用户每日任务
   */
  async getDailyTasks(taskDate = null) {
    try {
      const response = await api.get('/tasks/daily', {
        params: { task_date: taskDate }
      })
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取每日任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 更新抓捕小偷任务进度
   */
  async updateThiefTaskProgress(count = 1, context = null) {
    try {
      const response = await api.post('/tasks/progress/thief', {
        count,
        context
      })
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('更新小偷任务进度失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 更新清理垃圾任务进度
   */
  async updateRubbishTaskProgress(count = 1, context = null) {
    try {
      const response = await api.post('/tasks/progress/rubbish', {
        count,
        context
      })
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('更新垃圾任务进度失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 更新文化学习任务进度
   */
  async updateQuizTaskProgress(count = 1, context = null) {
    try {
      const response = await api.post('/tasks/progress/quiz', {
        count,
        context
      })
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('更新问答任务进度失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 领取任务奖励
   */
  async claimTaskReward(taskId, useAdDouble = false) {
    try {
      const response = await api.post('/tasks/reward/claim', {
        task_id: taskId,
        use_ad_double: useAdDouble
      })
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('领取任务奖励失败:', error)
      return { success: false, error: error.message }
    }
  }
}

// 创建单例实例
export const taskService = new TaskService()

// 默认导出
export default taskService