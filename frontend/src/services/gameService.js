/**
 * 游戏服务 - 封装游戏相关的API调用和业务逻辑
 */
import { api } from '@/utils/api'
import { useAuthStore } from '@/stores/auth'
import { useGameStore } from '@/stores/game'
import dataService from '@/services/dataService'
import deviceService from '@/services/deviceService'
import websocketService from '@/services/websocketService'
import civilizationService from '@/services/civilizationService'
import collectionsService from '@/services/collectionsService'
import taskService from '@/services/taskService'
import staminaService from '@/services/staminaService'
import treasureBoxService from '@/services/treasureBoxService'
import bossService from '@/services/bossService'
import culturalQuizService from '@/services/culturalQuizService'

class GameService {
  constructor() {
    this.currentSession = null
    this.isSessionActive = false
    this.websocketConnected = false
  }

  /**
   * 初始化游戏服务
   */
  async initialize() {
    try {
      // 检查后端健康状态
      // const health = await api.get('/health', { showLoading: false })
      // console.log('后端服务状态:', health.data)
      
      // 如果用户已登录，获取用户信息
      const authStore = useAuthStore()
      if (authStore.isLoggedIn) {
        dataService.resetGuestData()
        await this.refreshUserInfo()
      } else {
        // 如果没有登录，初始化游客模式
        this.initOfflineMode()
      }
      
      // 初始化WebSocket连接
      await this.initWebSocket()
      
      return true
    } catch (error) {
      console.error('游戏服务初始化失败:', error)
      // 在后端不可用时，启用离线模式
      this.initOfflineMode()
      return false
    }
  }

  /**
   * 初始化WebSocket连接
   */
  async initWebSocket() {
    try {
      const authStore = useAuthStore()
      const userId = authStore.user?.user_id || `guest_${Date.now()}`
      
      // 连接WebSocket
      await websocketService.connect(userId)
      this.websocketConnected = true
      
      // 设置事件监听器
      websocketService.on('connected', () => {
        console.log('WebSocket连接成功')
        this.websocketConnected = true
      })
      
      websocketService.on('disconnected', () => {
        console.log('WebSocket连接断开')
        this.websocketConnected = false
      })
      
      websocketService.on('miniprogram_pause', () => {
        console.log('收到小程序暂停指令')
        // 可以在这里处理游戏暂停逻辑
      })
      
      websocketService.on('miniprogram_resume', () => {
        console.log('收到小程序恢复指令')
        // 可以在这里处理游戏恢复逻辑
      })
      
      console.log('WebSocket服务初始化完成')
    } catch (error) {
      console.warn('WebSocket初始化失败:', error)
      this.websocketConnected = false
    }
  }

  /**
   * 检查是否在 CrazyGames 平台
   */
  isCrazyGamesPlatform() {
    // 检查 CrazyGames SDK 是否存在
    return !!(window.CrazyGames && window.CrazyGames.SDK)
  }

  /**
   * 初始化离线模式（游客模式）
   */
  initOfflineMode() {
    console.log('初始化离线模式')
    
    // 检查用户登录状态
    const authStore = useAuthStore()
    if (authStore.isLoggedIn) {
      console.log('用户已登录，不需要初始化游客模式')
      return
    }
    
    // 确保游客数据已初始化
    const guestData = dataService.getGuestData()
    
    // 加载游客数据到游戏状态
    const gameStore = useGameStore()
    gameStore.initGuestMode()
    
    console.log('离线模式已初始化，游客数据:', guestData)
  }

  /**
   * 游客登录
   */
  async guestLogin() {
    try {
      const deviceId = this.generateDeviceId()
      const deviceInfo = this.getDeviceInfo()
      
      const response = await api.auth.guestLogin(deviceId, deviceInfo)
      const { user_info, token, refresh_token } = response.data
      
      // 保存认证信息
      const authStore = useAuthStore()
      authStore.login({
        provider: 'guest',
        user: {
          ...user_info,
          isGuest: true
        },
        token,
        refreshToken: refresh_token
      })
      
      // 清除游客模式数据（已经登录了，不再是纯游客模式）
      dataService.clearGuestDataAfterLogin()
      console.log('游客登录成功，已清除本地游客数据')
      
      // 同步用户数据到游戏状态
      await this.syncUserDataToGameStore(user_info)
      
      console.log('游客登录成功:', user_info)
      return { success: true, user: user_info }
    } catch (error) {
      console.error('游客登录失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * CrazyGames 用户登录
   */
  async crazyGamesLogin(userToken) {
    try {
      console.log('尝试 CrazyGames 用户登录')

      const deviceId = this.generateDeviceId()
      const deviceInfo = this.getDeviceInfo()
      console.log(userToken, deviceId, deviceInfo,'----')
      const response = await api.auth.crazyGamesLogin(userToken, deviceId, deviceInfo)

      // 检查响应是否成功
      if (!response.data) {
        console.error('CrazyGames 登录失败：后端返回空数据')
        return { success: false, error: '后端返回空数据' }
      }

      const { user_id, token, refresh_token, user_info } = response.data

      // 验证必要字段
      if (!user_id || !token || !user_info) {
        console.error('CrazyGames 登录失败：缺少必要字段')
        return { success: false, error: '缺少必要字段' }
      }

      // 保存认证信息
      const authStore = useAuthStore()
      authStore.login({
        provider: 'crazygames',
        user: {
          user_id: user_id,
          ...user_info
        },
        token,
        refreshToken: refresh_token
      })

      // 清除游客模式状态（如果存在）
      const gameStore = useGameStore()
      if (dataService.isGuestMode()) {
        console.log('CrazyGames 登录成功，清除游客模式状态')
        dataService.resetGuestData()

        // 强制刷新游客模式状态检查
        console.log('游客模式状态已清除，当前状态:', dataService.isGuestMode())
      }

      // 同步用户数据到游戏状态
      await this.syncUserDataToGameStore(user_info)

      console.log('CrazyGames 用户登录成功:', user_info)
      console.log('用户登录状态:', authStore.isLoggedIn)
      console.log('游客模式状态:', gameStore.isGuestMode())

      return { success: true, user: user_info }
    } catch (error) {
      console.error('CrazyGames 用户登录失败:', error)

      // 返回详细的失败信息
      return {
        success: false,
        error: error.message || '网络连接失败',
        details: error
      }
    }
  }

  /**
   * 同步用户数据到游戏状态
   */
  async syncUserDataToGameStore(userInfo) {
    try {
      const gameStore = useGameStore()

      // 更新用户基本信息
      gameStore.updateUserInfo(userInfo)

      // 更新游戏资源
      gameStore.civilizationExp = userInfo.civilization_exp || 0
      gameStore.stamina = userInfo.stamina || 100

      // 尝试获取更多用户数据
      try {
        // 获取完整的用户资料信息
        const profileResponse = await api.user.getProfile()
        if (profileResponse.data) {
          console.log('用户资料信息同步成功:', profileResponse.data)

          // 更新更完整的用户信息
          gameStore.updateUserInfo(profileResponse.data)

          // 更新资源（使用最新数据）
          gameStore.civilizationExp = profileResponse.data.civilization_exp || gameStore.civilizationExp
          gameStore.stamina = profileResponse.data.stamina || gameStore.stamina
          
          // 更新文明守护者数据
          gameStore.userInfo.total_thieves_captured = profileResponse.data.total_thieves_captured || 0
          gameStore.userInfo.total_garbage_cleaned = profileResponse.data.total_garbage_cleaned || 0
          gameStore.userInfo.total_monuments_protected = profileResponse.data.total_monuments_protected || 0
          gameStore.userInfo.collections_count = profileResponse.data.collections_count || 0
          gameStore.userInfo.guardian_level = profileResponse.data.guardian_level || 1
        }
      } catch (profileError) {
        console.warn('获取用户资料失败，使用登录返回的数据:', profileError.message)
      }


      // 保存游戏状态
      gameStore.saveGameState()
      console.log('用户数据同步完成并已保存')
    } catch (error) {
      console.error('同步用户数据失败:', error)
      // 不抛出异常，允许登录继续
    }
  }

  /**
   * 刷新用户信息
   */
  async refreshUserInfo() {
    try {
      const response = await api.user.getProfile()
      const userInfo = response.data
      
      // 更新认证状态
      const authStore = useAuthStore()
      authStore.updateUser(userInfo)
      
      // 更新游戏状态
      const gameStore = useGameStore()
      gameStore.userInfo = {
        ...gameStore.userInfo,
        user_id: userInfo.user_id,
        nickname: userInfo.nickname || '游客',
        level: userInfo.level || 1,
        exp: userInfo.exp || 0,
        vip_level: userInfo.vip_level || 0,
        avatar_url: userInfo.avatar_url,
        total_play_time: userInfo.total_play_time || 0,
        total_sessions: userInfo.total_sessions || 0,
        collections_count: userInfo.collections_count || 0,
        cities_unlocked: userInfo.cities_unlocked || 1,
        // 文明守护者数据
        total_thieves_captured: userInfo.total_thieves_captured || 0,
        total_garbage_cleaned: userInfo.total_garbage_cleaned || 0,
        total_monuments_protected: userInfo.total_monuments_protected || 0,
        guardian_level: userInfo.guardian_level || 1
      }
      gameStore.civilizationExp = userInfo.civilization_exp || gameStore.civilizationExp
      gameStore.stamina = userInfo.stamina || gameStore.stamina
      gameStore.maxStamina = userInfo.max_stamina || 100
      
      console.log('用户信息更新成功:', userInfo, {
        文明经验: gameStore.civilizationExp,
        体力: `${gameStore.stamina}/${gameStore.maxStamina}`,
        守望者等级: gameStore.userInfo.guardian_level,
        图鉴收集: gameStore.userInfo.collections_count
      })
      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }

  /**
   * 获取城市列表
   */
  async getCities() {
    try {
      const response = await api.game.getCities()
      console.log('城市列表获取成功:', response.data)
      return { success: true, data: response.data }
    } catch (error) {
      console.error('获取城市列表失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 从krpano XML获取热点数据 - 使用krpano工具类
   */
  async getHotspotsFromKrpano() {
    try {
      // 使用krpano工具类，不依赖window.krpano
      const krpanoManager = await import('@/utils/krpano')

      if (!krpanoManager.default.ready) {
        return { thieves: [], garbage: [], treasure: [], boss: [] }
      }

      // 使用现成的方法获取热点
      const thiefHotspots = krpanoManager.default.getThiefHotspots()
      const garbageHotspots = krpanoManager.default.getGarbageHotspots()
      const allHotspots = krpanoManager.default.getAllHotspots()

      // 获取宝箱和boss热点
      const treasureHotspots = allHotspots.filter(h =>
        h.name.startsWith('treasure_') && h.visible && h.alpha > 0
      )
      const bossHotspots = allHotspots.filter(h =>
        h.name.startsWith('boss_') && h.visible && h.alpha > 0
      )

      const hotspots = {
        thieves: thiefHotspots.map(h => ({
          name: h.name,
          type: 'thief',
          ath: h.ath,
          atv: h.atv,
          scale: h.scale,
          visible: h.visible,
          baseScale: h.scale
        })),
        garbage: garbageHotspots.map(h => ({
          name: h.name,
          type: 'garbage',
          ath: h.ath,
          atv: h.atv,
          scale: h.scale,
          visible: h.visible,
          baseScale: h.scale
        })),
        treasure: treasureHotspots.map(h => ({
          name: h.name,
          type: 'treasure',
          ath: h.ath,
          atv: h.atv,
          scale: h.scale,
          visible: h.visible,
          baseScale: h.scale
        })),
        boss: bossHotspots.map(h => ({
          name: h.name,
          type: 'boss',
          ath: h.ath,
          atv: h.atv,
          scale: h.scale,
          visible: h.visible,
          baseScale: h.scale
        }))
      }

      return hotspots
    } catch (error) {
      console.error('从krpano获取热点数据失败:', error)
      return { thieves: [], garbage: [], treasure: [], boss: [] }
    }
  }

  /**
   * 开始游戏会话
   */
  async startGameSession(cityId = 'beijing', sceneId = 'scene_level_1') {
    try {
      // 检查用户登录状态
      const authStore = useAuthStore()
      const gameStore = useGameStore()
      
      // 从krpano获取热点数据并更新游戏状态
      const hotspotsFromXML = await this.getHotspotsFromKrpano()
      gameStore.updateHotspotCounts({
        totalThieves: hotspotsFromXML.thieves.length,
        totalGarbage: hotspotsFromXML.garbage.length,
        totalTreasure: hotspotsFromXML.treasure.length,
        totalBoss: hotspotsFromXML.boss.length,
        remainingThieves: hotspotsFromXML.thieves.length,
        remainingGarbage: hotspotsFromXML.garbage.length,
        remainingTreasure: hotspotsFromXML.treasure.length,
        remainingBoss: hotspotsFromXML.boss.length
      })
      
      // 如果用户已登录，优先尝试在线会话（不管是否有游客模式标记）
      if (authStore.isLoggedIn) {
        console.log('用户已登录，尝试在线会话')
        try {
      const response = await api.game.startSession(cityId, sceneId)
      const { session_id } = response.data
      
      this.currentSession = {
        id: session_id,
        cityId,
        sceneId,
        startTime: Date.now(),
        isOffline: false
      }
      this.isSessionActive = true
      
      console.log('在线游戏会话开始:', this.currentSession)
      console.log('✅ 热点数据已从XML更新到gameStore')
      
      // 发送游戏开始事件到WebSocket
      if (this.websocketConnected) {
        websocketService.sendGameStart(this.currentSession)
      }
      
      return {
        success: true,
        session: this.currentSession
          }
        } catch (error) {
          // 已登录用户的后端会话创建失败，仍然尝试离线模式作为备用
          console.warn('已登录用户的后端会话创建失败，使用离线模式作为备用:', error.message)
          return this.startOfflineSession(cityId, sceneId)
        }
      }
      
      // 如果用户未登录，使用纯游客模式
      if (!authStore.isLoggedIn) {
        console.log('用户未登录，使用纯游客模式（离线会话）')
        // 确保游客数据已初始化
        if (!dataService.getJSON('guestGameData')) {
          dataService.initGuestData()
          gameStore.initGuestMode()
        }
        return this.startOfflineSession(cityId, sceneId)
      }
    } catch (error) {
      console.error('开始游戏会话失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 开始离线游戏会话
   */
  startOfflineSession(cityId = 'beijing', sceneId = 'scene_level_1') {
    console.log('开始离线游戏会话')
    
    // 生成默认热点数据
    const defaultHotspots = this.generateDefaultHotspots()
    
    this.currentSession = {
      id: 'offline_' + Date.now(),
      cityId,
      sceneId,
      hotspots: defaultHotspots,
      startTime: Date.now(),
      isOffline: true
    }
    this.isSessionActive = true
    
    // 更新游戏状态中的热点数据
    const gameStore = useGameStore()
    gameStore.thieves = []
    gameStore.garbages = []
    gameStore.treasures = []
    
    // 根据热点类型分类
    defaultHotspots.forEach(hotspot => {
      const hotspotData = {
        id: hotspot.id,
        name: hotspot.name || hotspot.id,
        type: hotspot.type,
        position: hotspot.position,
        reward: hotspot.reward_preview,
        // 添加krpano所需的属性（离线模式默认值）
        image_url: hotspot.image_url || this.getDefaultImageUrl(hotspot.type),
        scale: hotspot.scale || this.getDefaultScale(hotspot.type),
        onclick_action: hotspot.onclick_action || this.getDefaultOnclickAction(hotspot.type, hotspot.name),
        visible: hotspot.visible !== false,
        description: hotspot.description || ''
      }
      
      switch (hotspot.type) {
        case 'thief':
        case 'boss_thief':
          gameStore.thieves.push(hotspotData)
          break
        case 'garbage':
          gameStore.garbages.push(hotspotData)
          break
        case 'treasure':
          gameStore.treasures.push(hotspotData)
          break
      }
    })
    
    console.log('离线游戏会话开始:', this.currentSession)
    console.log('热点分布:', {
      thieves: gameStore.thieves.length,
      garbages: gameStore.garbages.length,
      treasures: gameStore.treasures.length
    })
    
    return {
      success: true,
      session: this.currentSession
    }
  }

  /**
   * 生成默认热点数据（离线模式）
   */
  generateDefaultHotspots() {
    const hotspots = []
    
    // 生成小偷热点
    for (let i = 1; i <= 4; i++) {
      const name = `thief_${i}`
      hotspots.push({
        id: name,
        name: name,
        type: 'thief',
        position: { x: Math.random() * 360 - 180, y: Math.random() * 90 - 45 },
        reward_preview: {
          type: 'civilization_exp',
          amount: Math.floor(Math.random() * 15) + 5
        },
        image_url: '/img/thief/thief_1_320/thief1_1.png',
        scale: 0.8,
        onclick_action: `js(onThiefClicked('${name}'));`,
        visible: true,
        description: ''
      })
    }
    
    // 生成垃圾热点
    for (let i = 1; i <= 3; i++) {
      const name = `garbage_${i}`
      hotspots.push({
        id: name,
        name: name,
        type: 'garbage',
        position: { x: Math.random() * 360 - 180, y: Math.random() * 90 - 45 },
        reward_preview: {
          type: 'civilization_exp',
          amount: Math.floor(Math.random() * 12) + 3
        },
        image_url: '/img/icons/laji_icon.png',
        scale: 0.03,
        onclick_action: `js(onGarbageClicked('${name}'));`,
        visible: true,
        description: ''
      })
    }
    
    // 生成宝箱热点
    for (let i = 1; i <= 2; i++) {
      const name = `treasure_${i}`
      hotspots.push({
        id: name,
        name: name,
        type: 'treasure',
        position: { x: Math.random() * 360 - 180, y: Math.random() * 90 - 45 },
        reward_preview: {
          type: 'civilization_exp',
          amount: Math.floor(Math.random() * 25) + 10
        },
        image_url: '/img/page_icons/START-iocn.png',
        scale: 0.04,
        onclick_action: `js(onTreasureClicked('${name}'));`,
        visible: true,
        description: ''
      })
    }
    
    return hotspots
  }

  /**
   * 从热点名称推断热点类型
   */
  getHotspotTypeFromName(hotspotName) {
    if (hotspotName.startsWith('thief_')) {
      return 'thief'
    } else if (hotspotName.startsWith('garbage_')) {
      return 'garbage'
    } else if (hotspotName.startsWith('treasure_')) {
      return 'treasure'
    } else if (hotspotName.startsWith('boss_')) {
      return 'boss'
    } else {
      // 默认返回thief类型
      return 'thief'
    }
  }

  /**
   * 收集热点 - 新文明守护者系统版本
   */
  async collectHotspotCivilization(hotspotName, cityId = 'beijing') {
    try {
      if (!this.isSessionActive || !this.currentSession) {
        throw new Error('没有活跃的游戏会话')
      }

      // 从热点名称推断类型
      const hotspotType = this.getHotspotTypeFromName(hotspotName)
      const gameStore = useGameStore()
      const authStore = useAuthStore()
      
      // 检查体力要求
      const staminaCost = staminaService.getActionStaminaCost(hotspotType === 'thief' ? 'catch_thief' : 'clean_garbage')
      const staminaCheck = await staminaService.checkStaminaRequirement(staminaCost, '收集热点')
      
      if (!staminaCheck.sufficient) {
        return { success: false, error: staminaCheck.message }
      }

      let result = null
      let collectionDrop = null

      // 如果用户已登录且是在线会话，使用新的文明守护者接口
      if (authStore.isLoggedIn && !this.currentSession.isOffline) {
        try {
          // 消耗体力
          const staminaResult = await staminaService.consumeStamina(staminaCost, 
            hotspotType === 'thief' ? 'catch_thief' : 'clean_garbage', hotspotName)
          
          if (!staminaResult.success) {
            return { success: false, error: staminaResult.error }
          }

          // 使用新的热点交互接口（假设后端已更新）
          const response = await api.post('/game/hotspot/interact', {
            hotspot_id: hotspotName,
            hotspot_type: hotspotType,
            action: hotspotType === 'thief' ? 'capture' : 'clean',
            city_id: cityId
          })

          result = response.data.data

          // 更新游戏状态
          if (result.rewards) {
            if (result.rewards.civilization_exp > 0) {
              gameStore.addCivilizationExp(result.rewards.civilization_exp)
            }
            
            // 处理文化图鉴掉落
            if (result.rewards.collection_drop) {
              collectionDrop = result.rewards.collection_drop
            }
          }

          // 更新BOSS状态（如果有伤害）
          if (result.boss_status && result.boss_status.current_hp !== undefined) {
            // BOSS状态会通过civilizationService缓存更新
            civilizationService.clearBossCache(cityId)
          }

          // 自动更新任务进度
          await taskService.autoUpdateProgress(
            hotspotType === 'thief' ? 'catch_thief' : 'clean_garbage',
            { [hotspotType + 'Id']: hotspotName }
          )

        } catch (error) {
          console.warn('在线热点收集失败，使用离线逻辑:', error.message)
          return this.collectOfflineHotspotCivilization(hotspotName, cityId)
        }
      } else {
        // 离线模式
        return this.collectOfflineHotspotCivilization(hotspotName, cityId)
      }

      // 更新热点剩余数量
      gameStore.collectHotspotByType(hotspotType)

      // 发送WebSocket事件
      if (this.websocketConnected) {
        websocketService.sendHotspotCollect({
          name: hotspotName,
          type: hotspotType,
          reward: result?.rewards,
          current_exp: gameStore.civilizationExp,
          stamina: gameStore.stamina
        })
      }

      console.log('文明守护者热点收集成功:', { result, collectionDrop })
      
      return { 
        success: true, 
        result, 
        collectionDrop,
        expGained: result?.rewards?.civilization_exp || 0,
        staminaConsumed: staminaCost
      }

    } catch (error) {
      console.error('文明守护者热点收集失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 收集热点 - 使用热点名称（保持向后兼容）
   */
  async collectHotspot(hotspotName) {
    try {
      if (!this.isSessionActive || !this.currentSession) {
        throw new Error('没有活跃的游戏会话')
      }

      // 从热点名称推断类型
      const hotspotType = this.getHotspotTypeFromName(hotspotName)
      
      // 检查用户登录状态和会话类型
      const authStore = useAuthStore()
      const gameStore = useGameStore()
      
      // 如果用户已登录且是在线会话，尝试在线接口
      if (authStore.isLoggedIn && !this.currentSession.isOffline) {
        try {
      const response = await api.game.collectHotspot(this.currentSession.id, hotspotName, hotspotType)
      const result = response.data
      
      // 更新游戏状态 - 使用后端返回的最新数据
      if (result.success && result.progress) {
        // 更新文明经验值
        if (result.progress.civilization_exp !== undefined) {
          gameStore.civilizationExp = result.progress.civilization_exp
        }
        // 更新体力值
        if (result.progress.stamina !== undefined) {
          gameStore.stamina = result.progress.stamina
        }
        
        // 更新收集进度
        gameStore.currentLevelCollections = result.progress.thieves_collected + result.progress.garbage_collected
        
        // 弹药数量基于材料数量自动计算
        console.log(`当前弹药数: ${gameStore.ammoCount}（基于材料：${gameStore.thievesCollected + gameStore.garbageCollected}）`)

        // synthesisProgress 现在是计算属性，自动计算
        if (result.progress.synthesis_progress !== undefined) {
          console.log(`合成进度: ${gameStore.synthesisProgress}/10`)
        }

        // 更新材料收集数量
        if (result.progress.thieves_collected !== undefined) {
          gameStore.thievesCollected = result.progress.thieves_collected
        }
        if (result.progress.garbage_collected !== undefined) {
          gameStore.garbageCollected = result.progress.garbage_collected
        }

        // 如果有合成结果，显示合成信息
        if (result.synthesis_result && result.synthesis_result.success) {
          console.log('弹药合成成功:', result.synthesis_result)
        }

        // 更新热点剩余数量
        gameStore.collectHotspotByType(hotspotType)
      }
      
      console.log('在线热点收集成功:', result)
      
      // 发送热点收集事件到WebSocket
      if (this.websocketConnected) {
        websocketService.sendHotspotCollect({
          name: hotspotName,
          type: hotspotType,
          reward: result.reward,
          current_score: gameStore.civilizationExp,
          ammo_count: gameStore.ammoCount
        })
      }
      
      return { success: true, result }
        } catch (error) {
          // 后端接口调用失败，使用离线逻辑作为备用
          console.warn('后端热点收集失败，使用离线逻辑作为备用:', error.message)
          return this.collectOfflineHotspot(hotspotName)
        }
      } else if (!authStore.isLoggedIn || this.currentSession.isOffline) {
        // 未登录用户或离线会话，使用离线收集逻辑
        console.log('未登录或离线会话，使用离线热点收集')
        return this.collectOfflineHotspot(hotspotName)
      } else {
        console.log('其他情况，使用离线逻辑')
        return this.collectOfflineHotspot(hotspotName)
      }
    } catch (error) {
      console.error('收集热点失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 离线模式文明守护者热点收集
   */
  async collectOfflineHotspotCivilization(hotspotName, cityId = 'beijing') {
    try {
      const hotspotType = this.getHotspotTypeFromName(hotspotName)
      const gameStore = useGameStore()

      // 检查体力
      const staminaCost = staminaService.getActionStaminaCost(
        hotspotType === 'thief' ? 'catch_thief' : 'clean_garbage'
      )
      
      const staminaResult = await staminaService.offlineConsumeStamina(staminaCost, 
        hotspotType === 'thief' ? '抓捕小偷' : '清理垃圾')
      
      if (!staminaResult.success) {
        return { success: false, error: staminaResult.error }
      }

      // 生成文明经验奖励
      let expReward = 10
      if (hotspotType === 'thief') {
        expReward = 15
      } else if (hotspotType === 'garbage') {
        expReward = 12
      } else if (hotspotType === 'treasure') {
        expReward = 25
      } else if (hotspotType === 'boss') {
        expReward = 50
      }

      // 给予文明经验
      gameStore.addCivilizationExp(expReward)

      // 处理文化图鉴掉落
      const collectionDrop = await collectionsService.offlineCollectionDrop(
        hotspotType, hotspotName, cityId)

      // 🚫 PRD合规性清理：移除BOSS攻击逻辑
      // PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少

      // 更新任务进度
      await taskService.offlineUpdateProgress(
        hotspotType === 'thief' ? 'catch_thief' : 'clean_garbage',
        { [hotspotType + 'Id']: hotspotName }
      )

      // 更新热点剩余数量
      gameStore.collectHotspotByType(hotspotType)

      // 更新收集统计
      if (hotspotType === 'thief') {
        gameStore.userInfo.total_thieves_captured = (gameStore.userInfo.total_thieves_captured || 0) + 1
      } else if (hotspotType === 'garbage') {
        gameStore.userInfo.total_garbage_cleaned = (gameStore.userInfo.total_garbage_cleaned || 0) + 1
      }

      const result = {
        success: true,
        rewards: {
          civilization_exp: expReward,
          stamina_consumed: staminaCost,
          boss_damage_dealt: bossResult?.damage_dealt || 0
        },
        collection_drop: collectionDrop.hasDrop ? collectionDrop.collection : null,
        boss_result: bossResult,
        stamina: gameStore.stamina
      }

      console.log('离线文明守护者热点收集:', result)
      
      return { 
        success: true, 
        result,
        collectionDrop: collectionDrop.hasDrop ? collectionDrop : null,
        expGained: expReward,
        staminaConsumed: staminaCost
      }
    } catch (error) {
      console.error('离线文明守护者热点收集失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 离线收集热点 - 使用固定奖励逻辑，不依赖/start接口数据（保持向后兼容）
   */
  collectOfflineHotspot(hotspotName) {
    try {
      // 根据热点名称生成简单的固定奖励
      const hotspotType = this.getHotspotTypeFromName(hotspotName)
      const gameStore = useGameStore()
      
      // 生成文明经验奖励
      let reward = { type: 'civilization_exp', amount: 10 }
      
      if (hotspotType === 'thief') {
        // 小偷: 90%经验值 + 10%掉落宝箱
        reward = { type: 'civilization_exp', amount: 15 }
      } else if (hotspotType === 'garbage') {
        // 垃圾: 95%经验值 + 5%掉落宝箱
        reward = { type: 'civilization_exp', amount: 12 }
      } else if (hotspotType === 'treasure') {
        // 宝箱给予更多经验
        reward = { type: 'civilization_exp', amount: 25 }
      } else if (hotspotType === 'boss') {
        // BOSS给予大量经验
        reward = { type: 'civilization_exp', amount: 50 }
      }
      
      // 给予文明经验奖励
      if (reward.type === 'civilization_exp') {
        gameStore.addCivilizationExp(reward.amount)
      }
      
      // 添加经验值
      const expGained = Math.floor(reward.amount / 5) + 1
      const levelUpResult = dataService.addGuestExp(expGained)
      if (levelUpResult.leveledUp) {
        gameStore.userInfo.level = levelUpResult.newLevel
        gameStore.userInfo.exp = levelUpResult.currentExp
        gameStore.userInfo.expMax = levelUpResult.maxExp
      }
      
      // 更新热点剩余数量
      gameStore.collectHotspotByType(hotspotType)

      // 记录收集统计
      const guestData = dataService.getGuestData()
      if (reward.type === 'civilization_exp') {
        dataService.updateGuestSessionStats({
          total_civilization_exp_earned: (guestData.sessionStats.total_civilization_exp_earned || 0) + reward.amount
        })
      }
      
      const result = {
        success: true,
        reward: {
          type: reward.type,
          amount: reward.amount
        },
        exp_gained: expGained,
        level_up: levelUpResult.leveledUp ? {
          new_level: levelUpResult.newLevel,
          current_exp: levelUpResult.currentExp,
          max_exp: levelUpResult.maxExp
        } : null
      }
      
      console.log('离线热点收集成功:', result)
      return { success: true, result }
    } catch (error) {
      console.error('离线热点收集失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 🚫 PRD合规性清理：移除BOSS攻击方法
  // PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少

  // 🚫 PRD合规性清理：移除攻击BOSS方法（旧系统）
  // PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少

  // 🚫 PRD合规性清理：移除离线BOSS攻击方法
  // PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少

  /**
   * 结束游戏会话
   */
  async endGameSession() {
    try {
      if (!this.isSessionActive || !this.currentSession) {
        console.log('没有活跃的游戏会话需要结束')
        return { success: true }
      }

      // 如果是离线模式，不需要调用后端
      if (this.currentSession.isOffline) {
        this.currentSession = null
        this.isSessionActive = false
        console.log('离线游戏会话已结束')
        return { success: true }
      }

      const response = await api.game.endSession(this.currentSession.id)
      const result = response.data
      
      // 更新游戏状态
      const gameStore = useGameStore()
      if (result.summary) {
        if (result.summary.civilization_exp_earned) {
          gameStore.addCivilizationExp(result.summary.civilization_exp_earned)
        }
        if (result.summary.exp_earned) {
          gameStore.userInfo.exp += result.summary.exp_earned
          gameStore.checkLevelUp()
        }
        
        // 添加获得的文物到收集
        if (result.summary.artifacts_found && result.summary.artifacts_found.length > 0) {
          result.summary.artifacts_found.forEach(artifactId => {
            gameStore.addToCollection('artifacts', artifactId)
          })
        }
      }
      
      // 处理升级
      if (result.level_up) {
        gameStore.userInfo.level = result.level_up.new_level
        // 发放升级奖励
        if (result.level_up.rewards) {
          Object.entries(result.level_up.rewards).forEach(([type, amount]) => {
            if (type === 'civilization_exp') {
              gameStore.addCivilizationExp(amount)
            }
          })
        }
      }
      
      // 清理会话状态
      this.currentSession = null
      this.isSessionActive = false
      
      console.log('游戏会话结束:', result)
      return { success: true, result }
    } catch (error) {
      console.error('结束游戏会话失败:', error)
      return { success: false, error: error.message }
    }
  }





  /**
   * 获取文明经验排行榜
   */
  async getCivilizationExpLeaderboard(limit = 50, cityId = null) {
    try {
      const authStore = useAuthStore()
      
      if (!authStore.isLoggedIn) {
        // 离线模式使用模拟排行榜
        return await this.getOfflineRanking('civilization_exp', limit)
      }

      const response = await api.get('/ranking/civilization-exp', {
        params: { limit, city_id: cityId }
      })
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取文明经验排行榜失败:', error)
      // 失败时使用离线排行榜作为备用
      return await this.getOfflineRanking('civilization_exp', limit)
    }
  }

  /**
   * 获取图鉴收集排行榜
   */
  async getCollectionsLeaderboard(limit = 50, cityId = null) {
    try {
      const authStore = useAuthStore()
      
      if (!authStore.isLoggedIn) {
        return await this.getOfflineRanking('collections', limit)
      }

      const response = await api.get('/ranking/collections', {
        params: { limit, city_id: cityId }
      })
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取图鉴收集排行榜失败:', error)
      return await this.getOfflineRanking('collections', limit)
    }
  }

  /**
   * 获取综合排行榜
   */
  async getCombinedLeaderboard(limit = 50, cityId = null) {
    try {
      const authStore = useAuthStore()
      
      if (!authStore.isLoggedIn) {
        return await this.getOfflineRanking('combined', limit)
      }

      const response = await api.get('/ranking/combined', {
        params: { limit, city_id: cityId }
      })
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取综合排行榜失败:', error)
      return await this.getOfflineRanking('combined', limit)
    }
  }

  /**
   * 获取离线模拟排行榜
   */
  async getOfflineRanking(rankingType, limit = 50) {
    const mockRankings = []
    const gameStore = useGameStore()
    
    // 生成模拟排行榜数据
    for (let i = 1; i <= limit; i++) {
      const baseScore = Math.floor(Math.random() * 5000) + 500
      mockRankings.push({
        rank: i,
        user_id: `offline_user_${i}`,
        nickname: `守护者${i}`,
        avatar: `/avatars/default_${(i % 5) + 1}.png`,
        civilization_exp: rankingType === 'civilization_exp' ? baseScore : Math.floor(baseScore * 0.8),
        guardian_level: Math.min(10, Math.floor(baseScore / 500) + 1),
        collections_count: rankingType === 'collections' ? Math.floor(baseScore / 100) : Math.floor(baseScore / 150),
        combined_score: rankingType === 'combined' ? baseScore + Math.floor(baseScore / 10) : baseScore
      })
    }

    // 添加当前用户到排行榜
    const myRank = {
      rank: 25,
      user_id: 'current_user',
      nickname: gameStore.userInfo?.nickname || '我',
      avatar: gameStore.userInfo?.avatar_url || '/avatars/default_1.png',
      civilization_exp: gameStore.civilizationExp || 0,
      guardian_level: gameStore.userInfo?.guardian_level || 1,
      collections_count: gameStore.userInfo?.collections_count || 0
    }

    return {
      success: true,
      data: {
        ranking_type: rankingType,
        city_id: 'global',
        update_time: new Date().toISOString(),
        rankings: mockRankings,
        my_rank: myRank
      }
    }
  }

  /**
   * 获取排行榜（向后兼容旧接口）
   */
  async getLeaderboard(limit = 100) {
    try {
      // 默认返回图鉴收集排行榜以保持兼容性
      const result = await this.getCollectionsLeaderboard(limit)
      
      if (result.success) {
        // 转换数据格式以匹配旧接口
        const convertedData = {
          leaderboard_type: 'artifact_collector',
          update_time: result.data.update_time,
          my_rank: result.data.my_rank?.rank,
          rankings: result.data.rankings.map(item => ({
            rank: item.rank,
            user_id: item.user_id,
            nickname: item.nickname,
            artifacts_collected: item.collections_count,
            total_artifacts: 100,
            play_time: 0,
            cities_completed: 0,
            total_cities: 8
          }))
        }
        
        return { success: true, data: convertedData }
      }
      
      return result
    } catch (error) {
      console.error('获取排行榜失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取玩家进度
   */
  async getPlayerProgress(userId) {
    try {
      const authStore = useAuthStore()
      
      // 如果是未登录用户，从本地数据获取
      if (!authStore.isLoggedIn) {
        console.log('未登录用户，使用本地游戏进度')
        const guestData = dataService.getGuestData()
        
        return {
          artifacts_collected: guestData.stats.total_artifacts || 0,
          rank: 999, // 游客默认排名
          level: guestData.userInfo.level || 1,
          exp: guestData.userInfo.exp || 0,
          total_score: guestData.stats.total_score || 0
        }
      }

      // 尝试从后端API获取玩家详细信息
      const response = await api.leaderboard.getPlayerDetails(userId)
      console.log('玩家进度数据获取成功:', response.data)
      
      // 转换API返回的数据格式
      const playerData = response.data
      
      // 计算总的文物收集数量（从各城市进度累加）
      let totalArtifacts = 0
      if (playerData.city_progress && Array.isArray(playerData.city_progress)) {
        totalArtifacts = playerData.city_progress.reduce((sum, city) => {
          return sum + (city.artifacts_collected || 0)
        }, 0)
      }
      
      const result = {
        artifacts_collected: totalArtifacts,
        total_artifacts: 320, // 总共320个文物（根据后端设定）
        rank: 999, // 排名需要从排行榜API获取
        level: playerData.player_info?.level || 1,
        exp: 0,
        total_score: playerData.player_info?.total_play_time || 0,
        cities_guarded: playerData.collection_stats?.cities_guarded || 0,
        total_cities: playerData.collection_stats?.total_cities || 0
      }
      
      return { success: true, data: result }
    } catch (error) {
      console.warn('获取玩家进度失败，使用本地数据:', error.message)
      
      // API失败时，从游戏状态获取数据
      const gameStore = useGameStore()
      const authStore = useAuthStore()
      
      return {
        success: true,
        data: {
          artifacts_collected: gameStore.userInfo?.total_artifacts || 
                              authStore.userInfo?.total_artifacts || 0,
          rank: 999,
          level: gameStore.userInfo?.level || authStore.userInfo?.level || 1,
          exp: gameStore.userInfo?.exp || authStore.userInfo?.exp || 0,
          total_score: gameStore.userInfo?.total_score || 0
        }
      }
    }
  }

  /**
   * 获取每日任务
   */
  async getDailyTasks() {
    try {
      const response = await api.task.getDailyTasks()
      return { success: true, data: response.data }
    } catch (error) {
      console.error('获取每日任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 领取任务奖励
   */
  async claimTaskReward(taskId) {
    try {
      const response = await api.task.claimTaskReward(taskId)
      const result = response.data
      
      // 更新用户资源（如果后端返回了奖励数据）
      if (result.rewards) {
        const gameStore = useGameStore()
        Object.entries(result.rewards).forEach(([resource, amount]) => {
          if (resource === 'civilization_exp') {
            gameStore.addCivilizationExp(amount)
          }
        })
      }
      
      console.log('任务奖励领取结果:', result)
      return { success: true, result }
    } catch (error) {
      console.error('领取任务奖励失败:', error)
      return { success: false, error: error.message }
    }
  }


  /**
   * 重置用户场景热点
   */
  async resetUserSceneHotspots(cityId = 'beijing', sceneId = 'scene_level_1') {
    try {
      const response = await api.game.resetUserSceneHotspots(cityId, sceneId)
      const result = response.data

      return { success: true, data: result }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取热点类型的默认图片URL
   */
  getDefaultImageUrl(type) {
    const defaults = {
      'thief': '/img/thief/thief_1_320/thief1_1.png',
      'boss_thief': '/img/thief/thief_1_320/thief1_1.png',
      'garbage': '/img/icons/laji_icon.png',
      'treasure': '/img/gold.png'
    }
    return defaults[type] || '/img/hotspot_default.png'
  }

  /**
   * 获取热点类型的默认缩放值
   */
  getDefaultScale(type) {
    const defaults = {
      'thief': 0.8,
      'boss_thief': 0.8,
      'garbage': 0.03,
      'treasure': 0.04
    }
    return defaults[type] || 0.5
  }

  /**
   * 获取热点类型的默认点击动作
   */
  getDefaultOnclickAction(type, name) {
    const actions = {
      'thief': `js(onThiefClicked('${name}'));`,
      'boss_thief': `js(onThiefClicked('${name}'));`,
      'garbage': `js(onGarbageClicked('${name}'));`,
      'treasure': `js(onTreasureClicked('${name}'));`
    }
    return actions[type] || `js(onHotspotClicked('${name}'));`
  }

  /**
   * 处理PRD系统的收集逻辑
   * 集成BOSS血量系统、宝箱系统、经验系统等
   */
  async handlePRDCollectionLogic(hotspotType, cityId, hotspotName, count = 1) {
    try {
      const gameStore = useGameStore()
      
      // 1. 根据收集行为类型获取对应的行为标识
      const actionTypeMap = {
        'thief': 'catch_thief',
        'boss_thief': 'catch_thief',
        'garbage': 'clean_rubbish',
        'rubbish': 'clean_rubbish'
      }
      
      const actionType = actionTypeMap[hotspotType]
      if (!actionType) {
        console.log('非标准收集行为，跳过PRD逻辑处理')
        return { success: true, skipped: true }
      }

      // 2. 处理BOSS血量影响
      const bossResult = await bossService.handleCollectionDamage(
        cityId, actionType, count, hotspotName
      )
      
      if (bossResult.success) {
        console.log('BOSS血量更新:', bossResult.data)
        
        // 更新游戏状态中的BOSS信息
        if (bossResult.data.boss_status) {
          gameStore.updateBossStatus(bossResult.data.boss_status)
        }
      }

      // 3. 处理宝箱掉落
      const treasureResult = await treasureBoxService.handleTreasureBoxDrop(
        actionType, count, cityId, {
          hotspot_name: hotspotName,
          session_id: this.currentSession?.sessionId
        }
      )
      
      if (treasureResult.success && treasureResult.data.total_drops > 0) {
        console.log('宝箱掉落:', treasureResult.data.dropped_boxes)
        
        // 显示宝箱获得提示
        this.showTreasureBoxNotification(treasureResult.data.dropped_boxes)
      }

      // 4. 检查BOSS是否被击败，触发金宝箱
      if (bossService.isBossDefeated()) {
        console.log('BOSS被击败！触发金宝箱')
        
        const bossDefeatResult = await treasureBoxService.handleTreasureBoxDrop(
          'boss_defeat', 1, cityId, {
            defeat_source: actionType,
            hotspot_name: hotspotName,
            session_id: this.currentSession?.sessionId
          }
        )
        
        if (bossDefeatResult.success) {
          console.log('BOSS击败金宝箱:', bossDefeatResult.data)
          this.showBossDefeatNotification(bossDefeatResult.data)
        }

        // 显示BOSS击败特效
        this.showBossDefeatEffect(cityId)
      }

      // 5. 体力消耗
      const staminaCost = this.getStaminaCostByAction(actionType)
      if (staminaCost > 0) {
        const staminaResult = await api.stamina.consume(
          staminaCost,
          actionType,
          hotspotName
        )
        
        if (staminaResult.data) {
          gameStore.updateStamina(staminaResult.data.current_stamina)
        }
      }

      // 6. 经验值奖励
      const expReward = this.getExperienceRewardByAction(actionType)
      if (expReward > 0) {
        const expResult = await api.experience.addExperience(
          expReward,
          actionType,
          hotspotName
        )
        
        if (expResult.data) {
          gameStore.updateExperience(expResult.data)
        }
      }

      return {
        success: true,
        data: {
          boss_result: bossResult.data,
          treasure_result: treasureResult.data,
          boss_defeated: bossService.isBossDefeated(),
          experience_gained: expReward,
          stamina_cost: staminaCost
        }
      }
    } catch (error) {
      console.error('PRD收集逻辑处理失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取行为对应的体力消耗
   */
  getStaminaCostByAction(actionType) {
    const staminaCosts = {
      'catch_thief': 1,
      'clean_rubbish': 1,
      'monument_quiz': 5
    }
    return staminaCosts[actionType] || 0
  }

  /**
   * 获取行为对应的经验奖励
   */
  getExperienceRewardByAction(actionType) {
    const expRewards = {
      'catch_thief': 12,
      'clean_rubbish': 8,
      'monument_quiz': 35
    }
    return expRewards[actionType] || 0
  }

  /**
   * 显示宝箱获得通知
   */
  showTreasureBoxNotification(boxes) {
    boxes.forEach(box => {
      console.log(`获得${box.box_type}宝箱: ${box.box_id}`)
      // 这里可以添加UI通知逻辑
    })
  }

  // 🚫 PRD合规性清理：移除BOSS击败通知
  // PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少

  /**
   * 显示BOSS击败特效
   */
  showBossDefeatEffect(cityId) {
    console.log(`城市${cityId}的BOSS被击败，显示特效`)
    // 这里可以添加特效逻辑
  }

  /**
   * 开始文化问答会话
   */
  async startCulturalQuiz(cityId, monumentId = null, difficulty = 'medium') {
    try {
      const result = await culturalQuizService.startQuizSession(cityId, monumentId, difficulty)
      
      if (result.success) {
        console.log('文化问答会话开始:', result.data)
        return result
      }
      
      return result
    } catch (error) {
      console.error('开始文化问答失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 提交文化问答答案
   */
  async submitCulturalQuizAnswers(cityId, answers, sessionId = null, monumentId = null) {
    try {
      // 验证答案格式
      const validation = culturalQuizService.validateAnswers(answers)
      if (!validation.valid) {
        return { success: false, error: validation.error }
      }

      const result = await culturalQuizService.submitAnswers(cityId, answers, sessionId, monumentId)
      
      if (result.success) {
        console.log('文化问答提交成功:', result.data)
        
        // 处理问答完成后的逻辑（BOSS攻击、宝箱掉落等）
        const completionResult = await culturalQuizService.handleQuizCompletion(result.data, cityId)
        
        return {
          success: true,
          data: {
            quiz_result: result.data,
            completion_result: completionResult.data
          }
        }
      }
      
      return result
    } catch (error) {
      console.error('提交文化问答答案失败:', error)
      return { success: false, error: error.message }
    }
  }
}

// 创建单例实例
export const gameService = new GameService()

// 默认导出
export default gameService
