/**
 * 古迹问答系统服务
 */
import { api } from '@/utils/api'
import { useGameStore } from '@/stores/game'

class MonumentService {
  constructor() {
    this.monumentsCache = new Map()
    this.activeChallenge = null
    this.challengeStartTime = null
  }

  /**
   * 获取城市古迹列表
   */
  async getMonuments(cityId) {
    try {
      // 检查缓存
      const cached = this.monumentsCache.get(cityId)
      if (cached && Date.now() - cached.timestamp < 300000) { // 5分钟缓存
        return { success: true, data: cached.data }
      }

      const response = await api.get(`/monuments/${cityId}`)
      const data = response.data.data
      
      // 更新缓存
      this.monumentsCache.set(cityId, {
        data,
        timestamp: Date.now()
      })
      
      return { success: true, data }
    } catch (error) {
      console.error('获取古迹列表失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 开始古迹问答挑战
   */
  async startChallenge(monumentId, cityId) {
    try {
      const response = await api.post(`/monuments/${monumentId}/start-challenge`, {
        city_id: cityId
      })
      
      const result = response.data.data
      
      // 保存当前挑战信息
      this.activeChallenge = {
        challenge_id: result.challenge_id,
        monument_id: monumentId,
        city_id: cityId,
        questions: result.questions,
        time_limit_seconds: result.time_limit_seconds,
        challenge_rules: result.challenge_rules,
        monument: result.monument
      }
      
      this.challengeStartTime = Date.now()
      
      return { success: true, data: result }
    } catch (error) {
      console.error('开始古迹挑战失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 提交古迹问答答案
   */
  async submitChallenge(challengeId, answers) {
    try {
      // 计算答题耗时
      const timeSpent = this.challengeStartTime 
        ? Math.floor((Date.now() - this.challengeStartTime) / 1000)
        : 300 // 默认5分钟

      const response = await api.post(`/monuments/challenge/${challengeId}/submit`, {
        answers,
        time_spent_seconds: timeSpent
      })
      
      const result = response.data.data
      
      // 更新游戏状态
      const gameStore = useGameStore()
      
      if (result.challenge_result.is_success) {
        // 成功保护古迹
        if (result.rewards.civilization_exp > 0) {
          gameStore.addCivilizationExp(result.rewards.civilization_exp)
        }
        
        // 更新守护古迹数量
        if (gameStore.userInfo.total_monuments_protected !== undefined) {
          gameStore.userInfo.total_monuments_protected++
        }
        
        // 如果造成BOSS伤害，攻击BOSS
        if (result.rewards.boss_damage_dealt > 0) {
          // 可以触发BOSS攻击动画或效果
          console.log(`对BOSS造成 ${result.rewards.boss_damage_dealt} 点伤害`)
        }
      } else {
        // 挑战失败，可能有经验惩罚
        if (result.penalties && result.penalties.civilization_exp_lost < 0) {
          gameStore.addCivilizationExp(result.penalties.civilization_exp_lost)
        }
      }
      
      // 清除当前挑战
      this.activeChallenge = null
      this.challengeStartTime = null
      
      return { success: true, data: result }
    } catch (error) {
      console.error('提交挑战答案失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 领取双倍奖励（观看广告）
   */
  async claimDoubleReward(challengeId, adCompletionToken, adProvider = 'unity_ads', adType = 'rewarded_video') {
    try {
      const response = await api.post(`/monuments/challenge/${challengeId}/double-reward`, {
        ad_completion_token: adCompletionToken,
        ad_provider: adProvider,
        ad_type: adType
      })
      
      const result = response.data.data
      
      // 更新游戏状态中的经验值
      const gameStore = useGameStore()
      if (result.bonus_exp > 0) {
        gameStore.addCivilizationExp(result.bonus_exp)
      }
      
      return { success: true, data: result }
    } catch (error) {
      console.error('领取双倍奖励失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取当前活跃挑战信息
   */
  getActiveChallenge() {
    return this.activeChallenge
  }

  /**
   * 获取挑战剩余时间（秒）
   */
  getRemainingTime() {
    if (!this.activeChallenge || !this.challengeStartTime) {
      return 0
    }

    const elapsed = Math.floor((Date.now() - this.challengeStartTime) / 1000)
    const remaining = this.activeChallenge.time_limit_seconds - elapsed
    
    return Math.max(0, remaining)
  }

  /**
   * 取消当前挑战
   */
  cancelChallenge() {
    this.activeChallenge = null
    this.challengeStartTime = null
  }

  /**
   * 验证答案格式
   */
  validateAnswers(answers, questions) {
    if (!Array.isArray(answers)) {
      return { valid: false, error: '答案格式无效' }
    }

    if (answers.length !== questions.length) {
      return { valid: false, error: '答案数量与题目数量不匹配' }
    }

    for (let i = 0; i < answers.length; i++) {
      const answer = answers[i]
      const question = questions[i]

      if (!answer.question_id || !answer.selected_option_id) {
        return { valid: false, error: `第${i + 1}题答案格式无效` }
      }

      if (answer.question_id !== question.id) {
        return { valid: false, error: `第${i + 1}题ID不匹配` }
      }

      // 验证选项ID是否存在于题目中
      const validOptionIds = question.options.map(opt => opt.id)
      if (!validOptionIds.includes(answer.selected_option_id)) {
        return { valid: false, error: `第${i + 1}题选择的选项无效` }
      }
    }

    return { valid: true }
  }

  /**
   * 离线模式的古迹挑战（本地模拟）
   */
  async offlineMonumentChallenge(monumentId, cityId = 'beijing') {
    try {
      const gameStore = useGameStore()
      
      // 检查体力
      if (gameStore.stamina < 3) {
        return { success: false, error: '体力不足！古迹挑战需要3点体力' }
      }

      // 模拟题目和答案
      const questions = this.generateOfflineQuestions(cityId)
      
      // 模拟用户答题（随机正确率70%）
      const correctAnswers = Math.floor(questions.length * 0.7)
      const isSuccess = correctAnswers >= Math.ceil(questions.length * 0.67) // 需要答对2/3以上
      
      // 消耗体力
      gameStore.addStamina(-3)
      
      // 计算奖励
      let expReward = 0
      let bossReduce = 0
      
      if (isSuccess) {
        expReward = 50 + Math.floor(Math.random() * 20)
        bossReduce = 20 + Math.floor(Math.random() * 10)
        
        // 更新守护古迹数量
        if (gameStore.userInfo.total_monuments_protected !== undefined) {
          gameStore.userInfo.total_monuments_protected++
        }
      } else {
        expReward = -10 // 失败惩罚
      }
      
      // 给予奖励
      gameStore.addCivilizationExp(expReward)
      
      const result = {
        challenge_result: {
          is_success: isSuccess,
          questions_total: questions.length,
          questions_correct: correctAnswers,
          accuracy_rate: (correctAnswers / questions.length) * 100,
          time_spent_seconds: 180 + Math.floor(Math.random() * 120)
        },
        rewards: isSuccess ? {
          civilization_exp: expReward,
          boss_damage_dealt: bossReduce
        } : null,
        penalties: !isSuccess ? {
          civilization_exp_lost: expReward
        } : null,
        monument_status: {
          is_protected: isSuccess
        }
      }

      console.log('离线古迹挑战结果:', result)
      return { success: true, data: result }
    } catch (error) {
      console.error('离线古迹挑战失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 生成离线模式的题目
   */
  generateOfflineQuestions(cityId) {
    const beijingQuestions = [
      {
        id: 1,
        question_text: '太和殿主要用于什么重大场合？',
        options: [
          { id: 1, text: '皇帝登基大典', order: 1 },
          { id: 2, text: '日常办公', order: 2 },
          { id: 3, text: '存放宝物', order: 3 },
          { id: 4, text: '皇后居住', order: 4 }
        ],
        correct_answer_id: 1
      },
      {
        id: 2,
        question_text: '紫禁城建于哪个朝代？',
        options: [
          { id: 5, text: '明朝', order: 1 },
          { id: 6, text: '唐朝', order: 2 },
          { id: 7, text: '宋朝', order: 3 },
          { id: 8, text: '清朝', order: 4 }
        ],
        correct_answer_id: 5
      },
      {
        id: 3,
        question_text: '故宫的正门叫什么？',
        options: [
          { id: 9, text: '午门', order: 1 },
          { id: 10, text: '神武门', order: 2 },
          { id: 11, text: '东华门', order: 3 },
          { id: 12, text: '西华门', order: 4 }
        ],
        correct_answer_id: 9
      }
    ]

    // 可以根据不同城市返回不同题目
    return beijingQuestions
  }

  /**
   * 清除古迹缓存
   */
  clearMonumentsCache(cityId = null) {
    if (cityId) {
      this.monumentsCache.delete(cityId)
    } else {
      this.monumentsCache.clear()
    }
  }
}

// 创建单例实例
export const monumentService = new MonumentService()

// 默认导出
export default monumentService