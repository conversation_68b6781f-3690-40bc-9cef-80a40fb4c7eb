/**
 * BOSS血量系统服务
 */
import { api } from '@/utils/api'

class BossService {
  constructor() {
    this.cache = new Map()
    this.bossStatus = null
  }

  // 🚫 PRD合规性清理：移除BOSS状态获取方法
  // PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少

  // 🚫 PRD合规性清理：移除攻击BOSS功能
  // PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少

  // 🚫 PRD合规性清理：移除BOSS对话和重置方法
  // PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少

  // 🚫 PRD合规性清理：移除收集伤害处理方法
  // PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少

  /**
   * 获取BOSS血量阶段
   */
  getBossStage(healthPercentage) {
    if (healthPercentage > 80) return 'arrogant'     // 傲慢阶段
    if (healthPercentage > 50) return 'angry'        // 愤怒阶段
    if (healthPercentage > 20) return 'pleading'     // 求饶阶段
    if (healthPercentage > 0) return 'desperate'     // 绝望阶段
    return 'defeated'                                // 失败阶段
  }

  /**
   * 获取当前BOSS对话内容
   */
  async getCurrentDialogue(cityId) {
    try {
      if (!this.bossStatus) {
        await this.getBossStatus(cityId)
      }

      if (this.bossStatus) {
        const healthPercentage = this.bossStatus.health || 100
        const stage = this.getBossStage(healthPercentage)
        
        const dialogueResult = await this.getBossDialogue(cityId, stage)
        if (dialogueResult.success) {
          return {
            success: true,
            data: {
              stage: stage,
              health_percentage: healthPercentage,
              dialogue: dialogueResult.data.dialogue,
              boss_status: this.bossStatus
            }
          }
        }
      }

      return { success: false, error: 'BOSS状态不可用' }
    } catch (error) {
      console.error('获取当前对话失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 检查BOSS是否被击败
   */
  isBossDefeated() {
    return this.bossStatus && this.bossStatus.health <= 0
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear()
    this.bossStatus = null
  }

  /**
   * 获取BOSS血量百分比
   */
  getBossHealthPercentage() {
    return this.bossStatus ? (this.bossStatus.health || 0) : 100
  }

  /**
   * 获取BOSS状态（本地缓存）
   */
  getCachedBossStatus() {
    return this.bossStatus
  }
}

// 创建单例实例
export const bossService = new BossService()

// 默认导出
export default bossService 