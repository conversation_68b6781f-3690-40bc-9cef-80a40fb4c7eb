/**
 * BOSS血量系统服务
 */
import { api } from '@/utils/api'

class BossService {
  constructor() {
    this.cache = new Map()
    this.bossStatus = null
  }

  /**
   * 获取BOSS状态
   */
  async getBossStatus(cityId) {
    try {
      const response = await api.boss.getStatus(cityId)
      this.bossStatus = response.data
      return { success: true, data: response.data }
    } catch (error) {
      console.error('获取BOSS状态失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 🚫 PRD合规性清理：移除攻击BOSS功能
  // PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少

  /**
   * 获取BOSS对话
   */
  async getBossDialogue(cityId, stage) {
    try {
      const response = await api.boss.getDialogue(cityId, stage)
      return { success: true, data: response.data }
    } catch (error) {
      console.error('获取BOSS对话失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 重置BOSS
   */
  async resetBoss(cityId) {
    try {
      const response = await api.boss.reset(cityId)
      this.bossStatus = null
      return { success: true, data: response.data }
    } catch (error) {
      console.error('重置BOSS失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 处理收集行为对BOSS血量的影响
   */
  async handleCollectionDamage(cityId, actionType, count = 1, sourceId = null) {
    try {
      // 根据PRD定义的血量影响计算伤害
      const damageConfig = {
        'catch_thief': 2,    // 每个小偷-2%血量
        'clean_rubbish': 1,  // 每个垃圾-1%血量
        'monument_quiz': 10  // 每题正确-10%血量
      }

      const damagePerAction = damageConfig[actionType] || 0
      const totalDamage = damagePerAction * count

      if (totalDamage > 0) {
        const result = await this.attackBoss(cityId, totalDamage, actionType, sourceId)
        
        if (result.success) {
          return {
            success: true,
            data: {
              action_type: actionType,
              count: count,
              damage_per_action: damagePerAction,
              total_damage: totalDamage,
              boss_status: result.data.boss_status,
              is_defeated: result.data.boss_status?.health <= 0
            }
          }
        }
      }

      return { success: true, data: { damage: 0 } }
    } catch (error) {
      console.error('处理收集伤害失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取BOSS血量阶段
   */
  getBossStage(healthPercentage) {
    if (healthPercentage > 80) return 'arrogant'     // 傲慢阶段
    if (healthPercentage > 50) return 'angry'        // 愤怒阶段
    if (healthPercentage > 20) return 'pleading'     // 求饶阶段
    if (healthPercentage > 0) return 'desperate'     // 绝望阶段
    return 'defeated'                                // 失败阶段
  }

  /**
   * 获取当前BOSS对话内容
   */
  async getCurrentDialogue(cityId) {
    try {
      if (!this.bossStatus) {
        await this.getBossStatus(cityId)
      }

      if (this.bossStatus) {
        const healthPercentage = this.bossStatus.health || 100
        const stage = this.getBossStage(healthPercentage)
        
        const dialogueResult = await this.getBossDialogue(cityId, stage)
        if (dialogueResult.success) {
          return {
            success: true,
            data: {
              stage: stage,
              health_percentage: healthPercentage,
              dialogue: dialogueResult.data.dialogue,
              boss_status: this.bossStatus
            }
          }
        }
      }

      return { success: false, error: 'BOSS状态不可用' }
    } catch (error) {
      console.error('获取当前对话失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 检查BOSS是否被击败
   */
  isBossDefeated() {
    return this.bossStatus && this.bossStatus.health <= 0
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear()
    this.bossStatus = null
  }

  /**
   * 获取BOSS血量百分比
   */
  getBossHealthPercentage() {
    return this.bossStatus ? (this.bossStatus.health || 0) : 100
  }

  /**
   * 获取BOSS状态（本地缓存）
   */
  getCachedBossStatus() {
    return this.bossStatus
  }
}

// 创建单例实例
export const bossService = new BossService()

// 默认导出
export default bossService 