/**
 * 数据服务 - 统一管理 CrazyGames 数据模块和本地存储
 * 根据 CrazyGames SDK 的初始化状态自动选择存储方式
 */

class DataService {
  constructor() {
    this.isInitialized = false
    this.isCrazyGamesAvailable = false
    this.dataModule = null
  }

  /**
   * 初始化数据服务
   */
  async initialize() {
    try {
      // 检查 CrazyGames SDK 是否可用
      if (window.CrazyGames && window.CrazyGames.SDK) {
        // 初始化 CrazyGames SDK
        await window.CrazyGames.SDK.init()
        this.dataModule = window.CrazyGames.SDK.data
        this.isCrazyGamesAvailable = true
        console.log('CrazyGames 数据模块已初始化')
        
        // 迁移现有的 localStorage 数据到 CrazyGames 数据模块
        await this.migrateLocalStorageData()
      } else {
        console.log('CrazyGames SDK 不可用，使用本地存储')
      }
    } catch (error) {
      console.error('初始化数据服务失败:', error)
    }
    
    this.isInitialized = true
  }

  /**
   * 迁移 localStorage 数据到 CrazyGames 数据模块
   * 只在首次使用 CrazyGames SDK 时执行一次
   */
  async migrateLocalStorageData() {
    if (!this.isCrazyGamesAvailable) return

    // 检查是否已经迁移过
    const migrated = this.dataModule.getItem('dataMigrated')
    if (migrated === 'true') {
      console.log('数据已迁移，跳过迁移流程')
      return
    }

    console.log('开始迁移本地数据到 CrazyGames...')

    // 需要迁移的数据键列表
    const keysToMigrate = [
      'gameAudioSettings',
      'game-language',
      'gameCollections',
      'gameGuideShown',
      'device_id',
      // 游客游戏数据
      'guestGameData',
      'guestUserInfo',
      'guestGameProgress',
      'guestSessionStats',
      // 添加其他需要迁移的键
    ]

    // 迁移数据
    for (const key of keysToMigrate) {
      const value = localStorage.getItem(key)
      if (value !== null) {
        this.dataModule.setItem(key, value)
        console.log(`迁移数据: ${key}`)
      }
    }

    // 标记已完成迁移
    this.dataModule.setItem('dataMigrated', 'true')
    console.log('数据迁移完成')
  }

  /**
   * 获取数据
   * @param {string} key - 数据键
   * @returns {string|null} 数据值
   */
  getItem(key) {
    if (this.isCrazyGamesAvailable && this.dataModule) {
      // 优先使用 CrazyGames 数据模块
      const value = this.dataModule.getItem(key)
      
      // 如果 CrazyGames 中没有数据，尝试从 localStorage 获取（兼容性）
      if (value === null) {
        const localValue = localStorage.getItem(key)
        if (localValue !== null) {
          // 将 localStorage 的数据同步到 CrazyGames
          this.dataModule.setItem(key, localValue)
          return localValue
        }
      }
      
      return value
    } else {
      // 回退到 localStorage
      return localStorage.getItem(key)
    }
  }

  /**
   * 设置数据
   * @param {string} key - 数据键
   * @param {string} value - 数据值
   */
  setItem(key, value) {
    // 确保 value 是字符串
    const stringValue = typeof value === 'string' ? value : JSON.stringify(value)
    
    if (this.isCrazyGamesAvailable && this.dataModule) {
      // 使用 CrazyGames 数据模块
      this.dataModule.setItem(key, stringValue)
    }
    
    // 同时保存到 localStorage 作为备份（可选）
    localStorage.setItem(key, stringValue)
  }

  /**
   * 删除数据
   * @param {string} key - 数据键
   */
  removeItem(key) {
    if (this.isCrazyGamesAvailable && this.dataModule) {
      this.dataModule.removeItem(key)
    }
    localStorage.removeItem(key)
  }

  /**
   * 清空所有数据
   */
  clear() {
    if (this.isCrazyGamesAvailable && this.dataModule) {
      this.dataModule.clear()
    }
    localStorage.clear()
  }

  /**
   * 获取 JSON 格式的数据
   * @param {string} key - 数据键
   * @param {*} defaultValue - 默认值
   * @returns {*} 解析后的数据
   */
  getJSON(key, defaultValue = null) {
    const value = this.getItem(key)
    if (value === null) return defaultValue
    
    try {
      return JSON.parse(value)
    } catch (error) {
      console.error(`解析 JSON 数据失败 (${key}):`, error)
      return defaultValue
    }
  }

  /**
   * 设置 JSON 格式的数据
   * @param {string} key - 数据键
   * @param {*} value - 数据对象
   */
  setJSON(key, value) {
    this.setItem(key, JSON.stringify(value))
  }

  /**
   * 检查是否存在某个键
   * @param {string} key - 数据键
   * @returns {boolean} 是否存在
   */
  hasKey(key) {
    return this.getItem(key) !== null
  }

  /**
   * 获取所有后端同步的用户数据键前缀
   * 用于区分哪些数据需要与后端同步
   */
  getBackendSyncKeys() {
    return [
      'user_profile',      // 用户资料
      'game_progress',     // 游戏进度
      'user_stats',        // 用户统计
      // 其他需要与后端同步的数据
    ]
  }

  /**
   * 同步后端数据到本地存储
   * @param {object} backendData - 后端数据
   */
  syncFromBackend(backendData) {
    // 将后端数据保存到数据模块
    if (backendData.user_profile) {
      this.setJSON('user_profile', backendData.user_profile)
    }
    
    if (backendData.game_progress) {
      this.setJSON('game_progress', backendData.game_progress)
    }
    
    if (backendData.user_stats) {
      this.setJSON('user_stats', backendData.user_stats)
    }
    
  }

  /**
   * 获取需要同步到后端的数据
   * @returns {object} 需要同步的数据
   */
  getDataForBackendSync() {
    const syncData = {}
    
    // 收集需要同步的数据
    const collections = this.getJSON('gameCollections', {})
    if (collections) {
      syncData.collections = collections
    }
    
    // 其他需要同步的游戏数据
    const gameProgress = this.getJSON('game_progress', {})
    if (gameProgress) {
      syncData.game_progress = gameProgress
    }
    
    return syncData
  }

  // ==================== 游客模式数据管理 ====================

  /**
   * 初始化游客数据
   * @returns {object} 初始化的游客数据
   */
  initGuestData() {
    const defaultGuestData = {
      userInfo: {
        user_id: 'guest_' + Date.now(),
        nickname: '游客',
        avatar: '/img/touxiang_bg.png',
        level: 1,
        exp: 0,
        expMax: 100,
        vip_level: 0,
        avatar_url: null,
        total_play_time: 0,
        total_sessions: 0,
        total_artifacts: 0,
        cities_unlocked: 1,
        created_at: new Date().toISOString()
      },
      gameResources: {
        civilization_exp: 0,  // 文明经验值
        stamina: 100  // 体力值
      },
      tools: {
        radar: {
          count: 3,
          cooldown: 0,
          maxCooldown: 30
        },
        magnifier: {
          count: 3,
          cooldown: 0,
          maxCooldown: 30
        }
      },
      gameProgress: {
        currentLevel: 1,
        levels: [
          { id: 1, icon: '/img/map_Vatican.ae8dbd6c.png', stars: 0, unlocked: true, requiredCollections: 4 },
          { id: 2, icon: '/img/map_Paris.562136da.png', stars: 0, unlocked: false, requiredCollections: 5 },
          { id: 3, icon: '/img/map_NewYork.3f7de8a5.png', stars: 0, unlocked: false, requiredCollections: 6 },
          { id: 4, icon: '/img/map_Shanghai.624b3b9d.png', stars: 0, unlocked: false, requiredCollections: 7 },
          { id: 5, icon: '/img/map_Prague.fcfba582.png', stars: 0, unlocked: false, requiredCollections: 8 },
          { id: 6, icon: '/img/map_Zurich.95053267.png', stars: 0, unlocked: false, requiredCollections: 9 },
          { id: 7, icon: '/img/map_Iceland.ef8f6dbc.png', stars: 0, unlocked: false, requiredCollections: 10 },
          { id: 8, icon: '/img/map_Venice.02143ea7.png', stars: 0, unlocked: false, requiredCollections: 12 }
        ]
      },
      sessionStats: {
        total_sessions: 0,
        total_play_time: 0,
        total_civilization_exp_earned: 0,
        total_artifacts_found: 0,
        best_session_score: 0
      },
      lastUpdated: new Date().toISOString()
    }

    // 保存默认数据
    this.setJSON('guestGameData', defaultGuestData)
    console.log('游客数据已初始化')
    
    return defaultGuestData
  }

  /**
   * 获取游客游戏数据
   * @returns {object} 游客游戏数据
   */
  getGuestData() {
    let guestData = this.getJSON('guestGameData')
    
    if (!guestData) {
      guestData = this.initGuestData()
    }
    
    return guestData
  }

  /**
   * 保存游客游戏数据
   * @param {object} guestData - 游客数据
   */
  saveGuestData(guestData) {
    guestData.lastUpdated = new Date().toISOString()
    this.setJSON('guestGameData', guestData)
  }

  /**
   * 更新游客用户信息
   * @param {object} updates - 要更新的字段
   */
  updateGuestUserInfo(updates) {
    const guestData = this.getGuestData()
    guestData.userInfo = { ...guestData.userInfo, ...updates }
    this.saveGuestData(guestData)
  }

  /**
   * 更新游客游戏资源
   * @param {object} updates - 要更新的资源
   */
  updateGuestResources(updates) {
    const guestData = this.getGuestData()
    guestData.gameResources = { ...guestData.gameResources, ...updates }
    this.saveGuestData(guestData)
  }

  /**
   * 更新游客道具数据
   * @param {object} updates - 要更新的道具
   */
  updateGuestTools(updates) {
    const guestData = this.getGuestData()
    guestData.tools = { ...guestData.tools, ...updates }
    this.saveGuestData(guestData)
  }

  /**
   * 更新游客游戏进度
   * @param {object} updates - 要更新的进度
   */
  updateGuestProgress(updates) {
    const guestData = this.getGuestData()
    guestData.gameProgress = { ...guestData.gameProgress, ...updates }
    this.saveGuestData(guestData)
  }


  /**
   * 更新游客会话统计
   * @param {object} updates - 要更新的统计
   */
  updateGuestSessionStats(updates) {
    const guestData = this.getGuestData()
    guestData.sessionStats = { ...guestData.sessionStats, ...updates }
    this.saveGuestData(guestData)
  }

  /**
   * 记录游客游戏会话
   * @param {object} sessionData - 会话数据
   */
  recordGuestSession(sessionData) {
    const guestData = this.getGuestData()
    
    // 更新统计数据
    guestData.sessionStats.total_sessions += 1
    guestData.sessionStats.total_play_time += sessionData.duration || 0
    guestData.sessionStats.total_civilization_exp_earned += sessionData.civilization_exp_earned || 0
    guestData.sessionStats.total_artifacts_found += sessionData.artifacts_found || 0
    
    if (sessionData.score && sessionData.score > guestData.sessionStats.best_session_score) {
      guestData.sessionStats.best_session_score = sessionData.score
    }
    
    // 更新用户总游戏时长
    guestData.userInfo.total_play_time += sessionData.duration || 0
    guestData.userInfo.total_sessions = guestData.sessionStats.total_sessions
    guestData.userInfo.total_artifacts = guestData.sessionStats.total_artifacts_found
    
    this.saveGuestData(guestData)
  }


  /**
   * 添加游客经验值
   * @param {number} exp - 经验值
   * @returns {object} 升级结果
   */
  addGuestExp(exp) {
    const guestData = this.getGuestData()
    const userInfo = guestData.userInfo
    
    userInfo.exp += exp
    
    let leveledUp = false
    let newLevel = userInfo.level
    
    // 检查升级
    while (userInfo.exp >= userInfo.expMax) {
      userInfo.exp -= userInfo.expMax
      userInfo.level += 1
      userInfo.expMax = userInfo.level * 100 // 简单的升级公式
      leveledUp = true
      newLevel = userInfo.level
    }
    
    this.saveGuestData(guestData)
    
    return {
      leveledUp,
      newLevel,
      currentExp: userInfo.exp,
      maxExp: userInfo.expMax
    }
  }

  /**
   * 重置游客数据
   */
  resetGuestData() {
    this.removeItem('guestGameData')
    console.log('游客数据已重置')
  }

  /**
   * 导出游客数据（用于登录后迁移）
   * @returns {object} 游客数据
   */
  exportGuestDataForMigration() {
    const guestData = this.getGuestData()
    const collections = this.getJSON('gameCollections', {})
    
    return {
      userInfo: guestData.userInfo,
      gameResources: guestData.gameResources,
      gameProgress: guestData.gameProgress,
      sessionStats: guestData.sessionStats,
      collections: collections,
      exportTime: new Date().toISOString()
    }
  }

  /**
   * 检查是否为游客模式
   * @returns {boolean} 是否为游客模式
   */
  isGuestMode() {
    // 游客模式的判断逻辑：
    // 1. 用户未登录
    // 2. 存在游客数据
    const guestData = this.getJSON('guestGameData')
    
    // 简化判断：只检查是否存在游客数据
    // 这里避免循环依赖，只检查数据是否存在
    return !!guestData
  }

  /**
   * 检查是否应该使用离线数据
   * @returns {boolean} 是否应该使用离线数据
   */
  shouldUseOfflineData() {
    // 使用离线数据的情况：
    // 1. 游客模式
    // 2. 已登录但网络不可用（未来可扩展）
    return this.isGuestMode()
  }

  /**
   * 清除游客数据（登录后调用）
   */
  clearGuestDataAfterLogin() {
    // 清除游客数据
    this.removeItem('guestGameData')
    console.log('游客数据已清除')
  }
}

// 创建单例实例
const dataService = new DataService()

export default dataService 