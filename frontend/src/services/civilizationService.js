/**
 * 文明守护者服务 - 文明经验、守望者等级、BOSS战斗系统
 */
import { api } from '@/utils/api'
import { useGameStore } from '@/stores/game'

class CivilizationService {
  constructor() {
    this.bossCache = new Map()
    this.lastBossUpdate = null
  }

  /**
   * 获取用户文明守护者状态
   */
  async getUserStatus() {
    try {
      const response = await api.get('/guardian/experience/info')
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取文明状态失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取文明经验记录
   */
  async getExpLogs(limit = 20, offset = 0, source = 'all') {
    try {
      // 注意：需要在guardian service中添加经验记录查询API
      const response = await api.get('/guardian/experience/logs', {
        params: { limit, offset, source }
      })
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取经验记录失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取城市BOSS状态
   */
  async getBossStatus(cityId) {
    try {
      // 检查缓存
      const cacheKey = `boss_${cityId}`
      const cached = this.bossCache.get(cacheKey)
      
      if (cached && Date.now() - cached.timestamp < 30000) { // 30秒缓存
        return { success: true, data: cached.data }
      }

      const response = await api.get(`/guardian/boss/status/${cityId}`)
      const data = response.data.data
      
      // 更新缓存
      this.bossCache.set(cacheKey, {
        data,
        timestamp: Date.now()
      })
      
      return { success: true, data }
    } catch (error) {
      console.error('获取BOSS状态失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 攻击BOSS
   */
  async attackBoss(sessionId, actionType, count = 1, context = {}) {
    try {
      const response = await api.post('/guardian/boss/attack', {
        session_id: sessionId,
        action_type: actionType,
        count: count,
        context: context
      })
      
      const result = response.data.data
      
      // 清除BOSS缓存
      this.bossCache.clear()
      
      // 更新游戏状态中的文明经验（如果有触发对话奖励）
      if (result.experience_result && result.experience_result.experience_gained > 0) {
        const gameStore = useGameStore()
        gameStore.addCivilizationExp(result.experience_result.experience_gained)
      }
      
      return { success: true, data: result }
    } catch (error) {
      console.error('攻击BOSS失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取BOSS对话
   */
  async getBossDialogue(sessionId) {
    try {
      const response = await api.get(`/guardian/boss/dialogue/${sessionId}`)
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取BOSS对话失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 重置BOSS（管理员功能）
   */
  async resetBoss(cityId) {
    try {
      const response = await api.post(`/boss/reset/${cityId}`)
      
      // 清除缓存
      this.bossCache.delete(`boss_${cityId}`)
      
      return { success: true, message: response.data.message }
    } catch (error) {
      console.error('重置BOSS失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 抓捕小偷综合行为
   */
  async catchThief(count = 1, sessionId = null, cityId = null) {
    try {
      const response = await api.post('/guardian/action/catch_thief', {
        count,
        session_id: sessionId,
        city_id: cityId
      })
      const result = response.data.data
      
      // 更新游戏状态
      const gameStore = useGameStore()
      if (result.experience_result?.experience_gained) {
        gameStore.addCivilizationExp(result.experience_result.experience_gained)
      }
      
      return { success: true, data: result }
    } catch (error) {
      console.error('抓捕小偷失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 清理垃圾综合行为
   */
  async cleanRubbish(count = 1, sessionId = null, cityId = null) {
    try {
      const response = await api.post('/guardian/action/clean_rubbish', {
        count,
        session_id: sessionId,
        city_id: cityId
      })
      const result = response.data.data
      
      // 更新游戏状态
      const gameStore = useGameStore()
      if (result.experience_result?.experience_gained) {
        gameStore.addCivilizationExp(result.experience_result.experience_gained)
      }
      
      return { success: true, data: result }
    } catch (error) {
      console.error('清理垃圾失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 古迹问答综合行为
   */
  async monumentQuiz(correctAnswers = 1, sessionId = null, cityId = null, monumentId = null) {
    try {
      const response = await api.post('/guardian/action/monument_quiz', {
        correct_answers: correctAnswers,
        session_id: sessionId,
        city_id: cityId,
        monument_id: monumentId
      })
      const result = response.data.data
      
      // 更新游戏状态
      const gameStore = useGameStore()
      if (result.experience_result?.experience_gained) {
        gameStore.addCivilizationExp(result.experience_result.experience_gained)
      }
      
      return { success: true, data: result }
    } catch (error) {
      console.error('古迹问答失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取体力信息
   */
  async getStaminaInfo() {
    try {
      const response = await api.get('/guardian/stamina/info')
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('获取体力信息失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 观看广告恢复体力
   */
  async watchStaminaAd() {
    try {
      const response = await api.post('/guardian/stamina/ad_recover')
      return { success: true, data: response.data.data }
    } catch (error) {
      console.error('观看广告失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 守望者升级
   */
  async levelUpGuardian() {
    try {
      // 注意：这个API可能需要调整，现在通过经验系统自动升级
      const response = await api.post('/guardian/experience/level_up')
      const result = response.data.data
      
      // 更新游戏状态中的守望者等级
      const gameStore = useGameStore()
      if (result.new_level) {
        gameStore.userInfo.guardian_level = result.new_level
        gameStore.userInfo.guardian_level_name = result.guardian_level_name
      }
      
      return { success: true, data: result }
    } catch (error) {
      console.error('守望者升级失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 检查守望者升级条件
   */
  async checkGuardianUpgrade() {
    try {
      const statusResult = await this.getUserStatus()
      if (!statusResult.success) {
        return { canUpgrade: false }
      }

      const { civilization_exp, guardian_level, next_level } = statusResult.data
      
      if (!next_level) {
        return { canUpgrade: false, reason: '已达到最高等级' }
      }

      const canUpgrade = civilization_exp >= next_level.required_exp
      
      return {
        canUpgrade,
        currentExp: civilization_exp,
        requiredExp: next_level.required_exp,
        currentLevel: guardian_level,
        nextLevel: next_level.level,
        progress: next_level.progress_percentage
      }
    } catch (error) {
      console.error('检查升级条件失败:', error)
      return { canUpgrade: false, error: error.message }
    }
  }

  /**
   * 获取守望者等级配置信息
   */
  getGuardianLevelInfo(level) {
    const levelNames = {
      1: '新手守卫',
      2: '学徒护卫',
      3: '熟练守护者',
      4: '资深护卫',
      5: '英勇守卫',
      6: '精英守护者',
      7: '传奇守卫',
      8: '传奇守护者',
      9: '史诗守卫',
      10: '文明守护神'
    }

    const levelRequirements = {
      1: 0,
      2: 100,
      3: 300,
      4: 600,
      5: 1000,
      6: 1500,
      7: 2500,
      8: 4000,
      9: 6000,
      10: 10000
    }

    return {
      name: levelNames[level] || '未知等级',
      requiredExp: levelRequirements[level] || 0
    }
  }

  /**
   * 计算下一级所需经验
   */
  getExpToNextLevel(currentExp, currentLevel) {
    const nextLevel = currentLevel + 1
    const levelInfo = this.getGuardianLevelInfo(nextLevel)
    
    if (!levelInfo.requiredExp) {
      return { isMaxLevel: true }
    }

    return {
      isMaxLevel: false,
      expNeeded: Math.max(0, levelInfo.requiredExp - currentExp),
      nextLevelExp: levelInfo.requiredExp,
      progress: Math.min(100, (currentExp / levelInfo.requiredExp) * 100)
    }
  }

  /**
   * 清除BOSS缓存
   */
  clearBossCache(cityId = null) {
    if (cityId) {
      this.bossCache.delete(`boss_${cityId}`)
    } else {
      this.bossCache.clear()
    }
  }

  /**
   * 离线模式的BOSS攻击（本地模拟）
   */
  async offlineBossAttack(cityId = 'beijing') {
    try {
      const gameStore = useGameStore()
      
      // 检查体力
      if (gameStore.stamina < 2) {
        return { success: false, error: '体力不足！需要2点体力' }
      }

      // 模拟伤害计算
      const baseDamage = 5 + Math.floor(gameStore.userInfo.guardian_level * 2)
      const damage = baseDamage + Math.floor(Math.random() * 10)
      
      // 模拟BOSS状态
      let bossHp = 100 - (gameStore.userInfo.total_thieves_captured || 0) * 2
      bossHp = Math.max(0, bossHp - damage)
      
      // 消耗体力
      gameStore.addStamina(-2)
      
      // 奖励计算
      const expReward = Math.floor(damage * 1.5) + Math.floor(Math.random() * 5)
      gameStore.addCivilizationExp(expReward)
      
      const result = {
        damage_dealt: damage,
        boss_hp_before: bossHp + damage,
        boss_hp_after: bossHp,
        exp_gained: expReward,
        is_defeated: bossHp <= 0,
        triggered_dialogue: bossHp <= 20 && bossHp + damage > 20
      }

      console.log('离线BOSS攻击结果:', result)
      return { success: true, data: result }
    } catch (error) {
      console.error('离线BOSS攻击失败:', error)
      return { success: false, error: error.message }
    }
  }
}

// 创建单例实例
export const civilizationService = new CivilizationService()

// 默认导出
export default civilizationService