export default {
  common: {
    confirm: '确认',
    cancel: '取消',
    close: '关闭',
    loading: '加载中...',
    tips: '提示',
    warning: '警告',
    success: '成功',
    error: '错误',
    gold: '金币',
    diamond: '钻石',
    hour: '小时',
    hours: '小时',
    minutes: '分钟',
    details: '详情',
    upgrade: '升级',
    purchase: '购买',
    level: '等级'
  },
  ranking: {
    title: '排行榜',
    rank: '排名',
    name: '名称',
    gold: '金币',
    times: '次数',
  },
  home: {
    title: '城市探宝',
    subtitle: '探索世界各地的美景，寻找隐藏的宝藏',
    startGame: '开始游戏',
    quickLogin: '快速登录',
    loginWith: {
      google: 'Google登录',
      facebook: 'Facebook登录', 
      apple: 'Apple登录',
      wechat: '微信登录'
    },
    loginDeveloping: '{platform}登录功能开发中...'
  },
  Tasks: '任务',
  game: {
    attack: '攻击',
    level: 'Lv.{level}',
    coins: '金币',
    time: '时间',
    map: '地图',
    ranking: '排行榜',
    exp: '经验',
    lottery: {
      title: '摇奖',
    },
    tools: {
      radar: '雷达',
      magnifier: '放大镜',
      cooldown: '{seconds}秒',
      watchAdForUse: '看广告使用',
      radarCooldownTip: '雷达冷却中或次数不足',
      magnifierCooldownTip: '放大镜冷却中或次数不足',
      radarScanned: '雷达已扫描到目标！',
      magnifierUsed: '附近的小偷已被放大！',
      noThievesNearby: '附近没有小偷了',
      loadingAd: '正在加载广告...',
      gotRadar: '获得雷达次数+1',
      gotMagnifier: '获得放大镜次数+1'
    },
    
    rewards: {
      caughtThief: '抓到小偷！',
      cleanedGarbage: '清理垃圾！',
      foundTreasure: '发现宝箱！',
      watchAdForDouble: '看广告获得双倍奖励',
      gotDoubleReward: '获得双倍奖励 +{amount}'
    },
    
    levelMap: {
      title: '关卡地图',
      locked: '该关卡尚未解锁',
      currentLevel: '当前已在该关卡',
      switchToLevel: '切换到{name}关卡'
    },
    
    ranking: {
      title: '排行榜',
      rank: '排名',
      name: '名称',
      gold: '金币',
      times: '次数',
      you: '你'
    },
    attackFailed: '攻击失败',
    attackSuccess: '攻击成功',
    notEnoughAmmo: '弹药不足'
  },
  
  cities: {
    rome: '罗马',
    paris: '巴黎',
    newYork: '纽约',
    shanghai: '上海',
    prague: '布拉格',
    zurich: '苏黎世',
    iceland: '冰岛',
    venice: '威尼斯'
  },
  
  settings: {
    title: '游戏设置',
    language: '语言设置',
    sound: '音效',
    music: '音乐',
    vibration: '震动',
    quality: '画质',
    high: '高',
    medium: '中',
    low: '低'
  },
  
  guide: {
    skip: '跳过',
    prev: '上一步',
    next: '下一步',
    start: '开始冒险！',
    step1: {
      title: '寻找淘气的小偷！',
      description: '用你的手指放大和拖动来寻找他们吧！'
    },
    step2: {
      title: '宇宙垃圾大王入侵！',
      description: '快成为城市守护者，拯救世界！'
    },
    step3: {
      title: '收集弹药，开火！',
      description: '收集小偷和垃圾空团，攻击大王！'
    },
    step4: {
      title: '升级装备，变强！',
      description: '准备好了吗？'
    }
  },
  
  // 游戏菜单
  gameMenu: {
    title: '游戏菜单',
    cityGuide: '城市图鉴',
    ranking: '排行榜',
    contactUs: '联系我们',
    // 设置项
    music: '音乐',
    soundEffects: '音效',
    vibration: '震动',
    language: '语言',
    // 提示信息
    musicEnabled: '音乐已开启',
    musicDisabled: '音乐已关闭',
    sfxEnabled: '音效已开启',
    sfxDisabled: '音效已关闭',
    vibrationEnabled: '震动已开启',
    vibrationDisabled: '震动已关闭',
    contactDev: '联系我们功能开发中...',
    showUI: '显示UI',
    hideUI: '隐藏UI'
  },
  Collections: '图鉴',
  cityCollections: {
    title: '城市收藏',
    vatican: {
      title: '梵蒂冈城市',
      card1: '梵蒂冈卡1',
      card2: '梵蒂冈卡2'
    }
  },
  
  // 被动收益系统
  passiveIncome: {
    title: '被动收益',
    offlineTime: '离线时长',
    hourlyRate: '每小时收益',
    guardianBonus: '守护加成',
    collect: '领取收益',
    collectWithAd: '观看广告领取',
    noIncome: '暂无可领取收益'
  },
  
  // 守护等级系统
  guardian: {
    title: '守护等级',
    level: '等级',
    experience: '经验',
    artifactCollection: '图鉴收集',
    totalArtifacts: '总图鉴数',
    unlockedAbilities: '已解锁能力',
    manualUpgrade: '手动升级',
    nextLevelCost: '下一级花费',
    upgradeHistory: '升级历史',
    loadHistory: '加载历史',
    abilities: {
      'auto_collect': '自动领取被动收益',
      'extended_accumulation': '延长积累时间至48小时',
      'passive_income_boost': '被动收益加成',
      'ad_reward_boost': '广告奖励翻倍'
    },
    sources: {
      'artifact_bronze': '青铜图鉴',
      'artifact_silver': '白银图鉴', 
      'artifact_gold': '黄金图鉴',
      'manual_upgrade': '手动升级'
    }
  },
  
  // 图鉴类型
  artifacts: {
    bronze: '青铜',
    silver: '白银',
    gold: '黄金'
  },
  lottery: {
    title: '幸运转盘',
    start: '摇奖',
    start_ing: '摇奖中',
    notice: '点击开始抽奖，即可获得随机奖励！',
    remaining_times: '剩余次数:'
  },
} 