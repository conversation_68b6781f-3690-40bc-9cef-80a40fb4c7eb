export default {
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    close: 'Close',
    loading: 'Loading...',
    tips: 'Tips',
    warning: 'Warning',
    success: 'Success',
    error: 'Error'
  },
  ranking: {
    title: 'Ranking',
    rank: 'Rank',
    name: 'Name',
    gold: 'Gold',
    times: 'Times',
  },
  home: {
    title: 'City Treasure Hunt',
    subtitle: 'Explore beautiful scenery around the world and find hidden treasures',
    startGame: 'Start Game',
    quickLogin: 'Quick Login',
    loginWith: {
      google: 'Login with Google',
      facebook: 'Login with Facebook',
      apple: 'Login with Apple',
      wechat: 'Login with WeChat'
    },
    loginDeveloping: '{platform} login is under development...'
  },
  Tasks: 'Tasks',
  game: {
    attack: 'Attack',
    level: 'Lv.{level}',
    coins: 'Coins',
    time: 'Time',
    map: 'Map',
    ranking: 'Ranking',
    exp: 'EXP',
    lottery: {
      title: 'Spin',
    },
    tools: {
      radar: 'Radar',
      magnifier: 'Zoom in',
      cooldown: '{seconds}s',
      watchAdForUse: 'Watch Ad to Use',
      radarCooldownTip: 'Radar is cooling down or out of uses',
      magnifierCooldownTip: 'Magnifier is cooling down or out of uses',
      radarScanned: 'Radar has detected a target!',
      magnifierUsed: 'Nearby thieves have been magnified!',
      noThievesNearby: 'No thieves nearby',
      loadingAd: 'Loading advertisement...',
      gotRadar: 'Got Radar use +1',
      gotMagnifier: 'Got Magnifier use +1'
    },
    
    rewards: {
      caughtThief: 'Caught a thief!',
      cleanedGarbage: 'Cleaned garbage!',
      foundTreasure: 'Found treasure!',
      watchAdForDouble: 'Watch Ad for Double Reward',
      gotDoubleReward: 'Got double reward +{amount}'
    },
    
    levelMap: {
      title: 'Level Map',
      locked: 'This level is locked',
      currentLevel: 'Already in this level',
      switchToLevel: 'Switch to {name} level'
    },
    
    ranking: {
      title: 'Leaderboard',
      rank: 'Rank',
      name: 'Name',
      gold: 'Gold',
      times: 'Times',
      you: 'You'
    },
    attackFailed: 'Attack failed',
    attackSuccess: 'Attack success',
    notEnoughAmmo: 'Not enough ammo'
  },
  
  cities: {
    rome: 'Rome',
    paris: 'Paris',
    newYork: 'New York',
    shanghai: 'Shanghai',
    prague: 'Prague',
    zurich: 'Zurich',
    iceland: 'Iceland',
    venice: 'Venice'
  },
  
  settings: {
    title: 'Game Settings',
    language: 'Language',
    sound: 'Sound',
    music: 'Music',
    vibration: 'Vibration',
    quality: 'Quality',
    high: 'High',
    medium: 'Medium',
    low: 'Low'
  },
  
  guide: {
    skip: 'Skip',
    prev: 'Previous',
    next: 'Next',
    start: 'Start Adventure!',
    step1: {
      title: 'Find the Naughty Thieves!',
      description: 'Use your finger to zoom and drag to find them!'
    },
    step2: {
      title: 'Space Trash King Invades!',
      description: 'Become a city guardian and save the world!'
    },
    step3: {
      title: 'Collect Ammo, Fire!',
      description: 'Collect thieves and trash to attack the King!'
    },
    step4: {
      title: 'Upgrade Equipment, Get Stronger!',
      description: 'Are you ready?'
    }
  },
  
  // 游戏菜单
  gameMenu: {
    title: 'Game Menu',
    cityGuide: 'City Guide',
    ranking: 'Ranking',
    cardView: 'Card View',
    contactUs: 'Contact Us',
    // 设置项
    music: 'Music',
    soundEffects: 'Sound Effects',
    vibration: 'Vibration',
    language: 'Language',
    // 提示信息
    musicEnabled: 'Music enabled',
    musicDisabled: 'Music disabled',
    sfxEnabled: 'Sound effects enabled',
    sfxDisabled: 'Sound effects disabled',
    vibrationEnabled: 'Vibration enabled',
    vibrationDisabled: 'Vibration disabled',
    contactDev: 'Contact Us feature is under development...',
    showUI: 'Show UI',
    hideUI: 'Hide UI'
  },
  Collections: 'Cards',
  cityCollections: {
    title: 'City Collections',
    vatican: {
      title: 'Vatican Cities',
      card1: 'Vatican Card 1',
      card2: 'Vatican Card 2'
    },
  },
  lottery: {
    title: 'Lucky Spin',
    start: 'Spin',
    start_ing: 'Spinning',
    notice: 'Click to start spinning and get random rewards!',
    remaining_times: 'Remaining times:'
  },

  // Card View System
  cardView: {
    title: 'Card View',
    guardian: 'Guardian',
    experience: 'Experience',
    collection: 'Collection',
    achievements: 'Achievements',
    upgrades: 'Upgrades'
  },

  // Guardian Card
  guardian: {
    title: 'Guardian',
    subtitle: 'City Guardian Information',
    clickForDetails: 'Click for details',
    detailedInfo: 'Detailed Information',
    artifactCollection: 'Artifact Collection',
    artifacts: 'Artifacts',
    abilities: 'Abilities',
    actions: 'Actions',
    unlockedAbilities: 'Unlocked Abilities',
    upgradeOptions: 'Upgrade Options',
    upgradeWithGold: 'Upgrade with Gold',
    upgradeWithDiamond: 'Upgrade with Diamond',
    upgradeSuccess: 'Upgrade successful!',
    upgradeFailed: 'Upgrade failed',
    names: {
      novice: 'Novice Guardian',
      apprentice: 'Apprentice Guardian',
      experienced: 'Experienced Guardian',
      skilled: 'Skilled Guardian',
      veteran: 'Veteran Guardian',
      expert: 'Expert Guardian',
      master: 'Master Guardian',
      grandmaster: 'Grandmaster Guardian'
    },
    levels: {
      novice: 'Novice',
      apprentice: 'Apprentice',
      experienced: 'Experienced',
      skilled: 'Skilled',
      veteran: 'Veteran',
      expert: 'Expert',
      master: 'Master',
      grandmaster: 'Grandmaster'
    },
    descriptions: {
      level1: 'A newcomer just starting the guardian path',
      level2: 'An apprentice learning basic skills',
      level3: 'A guardian with some experience',
      level4: 'A skilled guardian with refined techniques',
      level5: 'An experienced veteran guardian',
      level10: 'An expert guardian with masterful skills',
      level12: 'A revered master-level guardian',
      level14: 'A legendary grandmaster guardian'
    },
    abilities: {
      staminaBoost: 'Stamina Boost',
      staminaBoostDesc: 'Increases maximum stamina',
      treasureFinder: 'Treasure Finder',
      treasureFinderDesc: 'Increases treasure discovery rate',
      masterCollector: 'Master Collector',
      masterCollectorDesc: 'Double rewards from all collection actions'
    }
  },

  // Experience System
  experience: {
    title: 'Experience System',
    subtitle: 'Guardian Growth Progress',
    level: 'Level',
    progress: 'Progress',
    expNeeded: '{exp} EXP needed to level up',
    sources: 'Experience Sources',
    fromThieves: 'From Thieves',
    fromGarbage: 'From Garbage',
    fromMonuments: 'From Monuments',
    fromBonus: 'From Bonus',
    nextLevel: 'Next Level',
    maxLevelReached: 'Max Level Reached',
    maxLevelDescription: 'Congratulations! You are the strongest guardian!',
    viewHistory: 'View History',
    tips: 'Tips'
  },

  // Collection Stats
  collection: {
    statsTitle: 'Collection Stats',
    statsSubtitle: 'Your Collection Achievements',
    total: 'Total',
    todayCollected: 'Today collected {count}',
    bestDay: 'Best day {count}',
    avgPerDay: 'Daily avg {count}',
    byCategory: 'By Category',
    thieves: 'Thieves',
    garbage: 'Garbage',
    monuments: 'Monuments',
    treasures: 'Treasures',
    achievements: 'Achievement Badges',
    efficiency: 'Collection Efficiency',
    perMinute: 'per minute',
    accuracy: 'Accuracy',
    streak: 'Streak',
    viewDetails: 'View Details',
    share: 'Share'
  },

  // Achievement System
  achievements: {
    firstThief: 'First Catch',
    firstThiefDesc: 'Successfully caught your first thief',
    thiefHunter: 'Thief Hunter',
    thiefHunterDesc: 'Caught 100 thieves in total',
    cleanCity: 'City Cleaner',
    cleanCityDesc: 'Cleaned 50 pieces of garbage',
    guardianMaster: 'Guardian Master',
    guardianMasterDesc: 'Completed 1000 collection actions',
    completed: 'Completed',
    reward: 'Reward',
    unlockedOn: 'Unlocked on'
  },

  // Upgrade System
  upgrades: {
    staminaBoost: 'Stamina Enhancement',
    staminaBoostDesc: 'Increases maximum stamina by 20 points',
    treasureFinder: 'Treasure Expert',
    treasureFinderDesc: 'Increases treasure discovery rate by 25%',
    requirements: 'Requirements',
    requireLevel: 'Requires level {level}',
    requireThieves: 'Requires catching {count} thieves',
    requireCollections: 'Requires {count} collections',
    cost: 'Upgrade Cost',
    upgrade: 'Upgrade',
    locked: 'Locked',
    upgradeSuccess: 'Upgrade successful!',
    upgradeFailed: 'Upgrade failed'
  },

  // Reward System
  rewards: {
    staminaBoost: 'Stamina Boost',
    treasureFinder: 'Treasure Finder',
    masterCollector: 'Master Collector',
    grandmasterTitle: 'Grandmaster Title',
    goldBonus: 'Gold Bonus',
    diamondBonus: 'Diamond Bonus'
  },
}