export default {
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    close: 'Close',
    loading: 'Loading...',
    tips: 'Tips',
    warning: 'Warning',
    success: 'Success',
    error: 'Error'
  },
  ranking: {
    title: 'Ranking',
    rank: 'Rank',
    name: 'Name',
    gold: 'Gold',
    times: 'Times',
  },
  home: {
    title: 'City Treasure Hunt',
    subtitle: 'Explore beautiful scenery around the world and find hidden treasures',
    startGame: 'Start Game',
    quickLogin: 'Quick Login',
    loginWith: {
      google: 'Login with Google',
      facebook: 'Login with Facebook',
      apple: 'Login with Apple',
      wechat: 'Login with WeChat'
    },
    loginDeveloping: '{platform} login is under development...'
  },
  Tasks: 'Tasks',
  game: {
    attack: 'Attack',
    level: 'Lv.{level}',
    coins: 'Coins',
    time: 'Time',
    map: 'Map',
    ranking: 'Ranking',
    exp: 'EXP',
    lottery: {
      title: 'Spin',
    },
    tools: {
      radar: 'Radar',
      magnifier: 'Zoom in',
      cooldown: '{seconds}s',
      watchAdForUse: 'Watch Ad to Use',
      radarCooldownTip: 'Radar is cooling down or out of uses',
      magnifierCooldownTip: 'Magnifier is cooling down or out of uses',
      radarScanned: 'Radar has detected a target!',
      magnifierUsed: 'Nearby thieves have been magnified!',
      noThievesNearby: 'No thieves nearby',
      loadingAd: 'Loading advertisement...',
      gotRadar: 'Got Radar use +1',
      gotMagnifier: 'Got Magnifier use +1'
    },
    
    rewards: {
      caughtThief: 'Caught a thief!',
      cleanedGarbage: 'Cleaned garbage!',
      foundTreasure: 'Found treasure!',
      watchAdForDouble: 'Watch Ad for Double Reward',
      gotDoubleReward: 'Got double reward +{amount}'
    },
    
    levelMap: {
      title: 'Level Map',
      locked: 'This level is locked',
      currentLevel: 'Already in this level',
      switchToLevel: 'Switch to {name} level'
    },
    
    ranking: {
      title: 'Leaderboard',
      rank: 'Rank',
      name: 'Name',
      gold: 'Gold',
      times: 'Times',
      you: 'You'
    },
    attackFailed: 'Attack failed',
    attackSuccess: 'Attack success',
    notEnoughAmmo: 'Not enough ammo'
  },
  
  cities: {
    rome: 'Rome',
    paris: 'Paris',
    newYork: 'New York',
    shanghai: 'Shanghai',
    prague: 'Prague',
    zurich: 'Zurich',
    iceland: 'Iceland',
    venice: 'Venice'
  },
  
  settings: {
    title: 'Game Settings',
    language: 'Language',
    sound: 'Sound',
    music: 'Music',
    vibration: 'Vibration',
    quality: 'Quality',
    high: 'High',
    medium: 'Medium',
    low: 'Low'
  },
  
  guide: {
    skip: 'Skip',
    prev: 'Previous',
    next: 'Next',
    start: 'Start Adventure!',
    step1: {
      title: 'Find the Naughty Thieves!',
      description: 'Use your finger to zoom and drag to find them!'
    },
    step2: {
      title: 'Space Trash King Invades!',
      description: 'Become a city guardian and save the world!'
    },
    step3: {
      title: 'Collect Ammo, Fire!',
      description: 'Collect thieves and trash to attack the King!'
    },
    step4: {
      title: 'Upgrade Equipment, Get Stronger!',
      description: 'Are you ready?'
    }
  },
  
  // 游戏菜单
  gameMenu: {
    title: 'Game Menu',
    cityGuide: 'City Guide',
    ranking: 'Ranking',
    contactUs: 'Contact Us',
    // 设置项
    music: 'Music',
    soundEffects: 'Sound Effects',
    vibration: 'Vibration',
    language: 'Language',
    // 提示信息
    musicEnabled: 'Music enabled',
    musicDisabled: 'Music disabled',
    sfxEnabled: 'Sound effects enabled',
    sfxDisabled: 'Sound effects disabled',
    vibrationEnabled: 'Vibration enabled',
    vibrationDisabled: 'Vibration disabled',
    contactDev: 'Contact Us feature is under development...',
    showUI: 'Show UI',
    hideUI: 'Hide UI'
  },
  Collections: 'Cards',
  cityCollections: {
    title: 'City Collections',
    vatican: {
      title: 'Vatican Cities',
      card1: 'Vatican Card 1',
      card2: 'Vatican Card 2'
    },
  },
  lottery: {
    title: 'Lucky Spin',
    start: 'Spin',
    start_ing: 'Spinning',
    notice: 'Click to start spinning and get random rewards!',
    remaining_times: 'Remaining times:'
  },
} 