<template>
  <Teleport to="body">
    <transition-group name="reward" tag="div">
      <div
        v-for="(item, index) in rewards"
        :key="item.id"
        class="reward-item"
        :class="{ 
          'simple-reward': !item.fromTreasure,
          'treasure-reward': item.fromTreasure,
          'treasure-top': item.fromTreasure && item.position === 'top',
          'treasure-bottom': item.fromTreasure && item.position === 'bottom'
        }"
        :style="{
          '--index': index,
          '--target-x': item.targetX + 'px',
          '--target-y': item.targetY + 'px',
          '--delay': item.delay + 'ms'
        }"
        @animationend="onAnimationEnd(item.id)"
              >

        
        <!-- 奖励内容盒子 -->
        <div class="reward-content">
            <!-- 文明经验值图标 -->
            <img 
              v-if="item.type === 'civilization_exp'" 
              class="reward-icon" 
              src="/img/page_icons/Coin.png" 
              alt="文明经验值"
            />
            <!-- 体力值图标 -->
            <img 
              v-else-if="item.type === 'stamina'" 
              class="reward-icon" 
              src="/img/page_icons/Gem.png" 
              alt="体力值"
            />
            <!-- 金币图标 -->
            <img 
              v-else-if="item.type === 'gold'" 
              class="reward-icon" 
              src="/img/page_icons/Coin.png" 
              alt="金币"
            />
            <!-- 钻石图标 -->
            <img 
              v-else-if="item.type === 'diamond'" 
              class="reward-icon" 
              src="/img/page_icons/diamond.png" 
              alt="钻石"
            />
          
          <!-- 奖励数量显示 -->
          <span class="reward-amount">+{{ item.amount }}</span>
        </div>
      </div>
    </transition-group>
  </Teleport>
</template>

<script setup>
import { ref, nextTick } from 'vue'

const rewards = ref([])
let rewardIdCounter = 0

// 计算目标位置
const getTargetPosition = (type) => {
  let selector = '.civilization-exp-display' // 默认飞向文明经验值位置
  
  if (type === 'civilization_exp') {
    selector = '.civilization-exp-display'
  } else if (type === 'stamina') {
    selector = '.stamina-display'
  } else if (type === 'gold') {
    selector = '.coin-display'
  } else if (type === 'diamond') {
    selector = '.diamond-display'
  } else if (type === 'treasure_box') {
    // 宝箱先飞向文明经验值位置，后续会创建多个飞向不同位置的动画
    selector = '.civilization-exp-display'
  }
  
  const element = document.querySelector(selector)
  
  if (element) {
    const rect = element.getBoundingClientRect()
    return {
      x: rect.left + rect.width / 2,
      y: rect.top + rect.height / 2
    }
  }
  
  // 默认位置
  return { x: window.innerWidth - 100, y: 50 }
}

// 显示奖励
const show = (reward) => {
  if (reward.type === 'treasure_box') {
    // 宝箱奖励：先显示宝箱，然后分别飞向不同位置
    showTreasureBox(reward)
  } else {
    // 单一奖励
    showSingleReward(reward)
  }
}

// 显示单一奖励
const showSingleReward = (reward) => {
  // 只显示大于0的奖励
  if (!reward.amount || reward.amount <= 0) {
    // 如果有回调，仍然执行（比如更新UI状态）
    if (reward.onComplete) {
      reward.onComplete()
    }
    return
  }
  
  const target = getTargetPosition(reward.type)
  const id = ++rewardIdCounter
  
  rewards.value.push({
    id,
    type: reward.type,
    amount: reward.amount,
    targetX: target.x,
    targetY: target.y,
    onComplete: reward.onComplete
  })
  
  // 自动清理超时的奖励
  setTimeout(() => {
    const index = rewards.value.findIndex(r => r.id === id)
    if (index !== -1) {
      rewards.value.splice(index, 1)
    }
  }, 3000)
}

// 显示宝箱奖励
const showTreasureBox = (reward) => {
  const contents = reward.contents || {}
  const goldTarget = getTargetPosition('gold')
  const diamondTarget = getTargetPosition('diamond')
  
  let hasValidReward = false
  
  // 同时创建金币和钻石动画
  if (contents.diamond && contents.diamond > 0) {
    hasValidReward = true
    const diamondId = ++rewardIdCounter
    rewards.value.push({
      id: diamondId,
      type: 'diamond',
      amount: contents.diamond,
      targetX: diamondTarget.x,
      targetY: diamondTarget.y,
      fromTreasure: true,
      position: 'top', // 钻石在上方
      delay: 0,
      onComplete: reward.onComplete
    })
  }
  
  if (contents.gold && contents.gold > 0) {
    hasValidReward = true
    const goldId = ++rewardIdCounter
    rewards.value.push({
      id: goldId,
      type: 'gold',
      amount: contents.gold,
      targetX: goldTarget.x,
      targetY: goldTarget.y,
      fromTreasure: true,
      position: 'bottom', // 金币在下方
      delay: 0,
      onComplete: contents.diamond && contents.diamond > 0 ? undefined : reward.onComplete // 避免重复回调
    })
  }
  
  // 如果没有有效奖励但有回调，仍然执行回调
  if (!hasValidReward && reward.onComplete) {
    reward.onComplete()
  }
}

// 动画结束处理
const onAnimationEnd = (id) => {
  const index = rewards.value.findIndex(r => r.id === id)
  if (index !== -1) {
    const reward = rewards.value[index]
    
    // 触发完成回调（更新资源）
    if (reward.onComplete) {
      reward.onComplete()
    }
    
    // 移除奖励
    rewards.value.splice(index, 1)
  }
}

// 暴露方法给父组件
defineExpose({
  show
})
</script>

<style lang="scss" scoped>


.reward-item {
  position: fixed;
  left: 50%;
  top: 40%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  pointer-events: none;
  
  // 多个奖励时的偏移
  &:nth-child(n+2) {
    animation-delay: calc(var(--index) * 0.2s);
  }
  

  
  // 单个奖励简化动画 - 直接飞行，不翻转
  &.simple-reward {
    animation: simpleRewardSequence 1.5s ease-in-out forwards;
  }
  
  // 来自宝箱的奖励 - 上下位置，停留翻转然后同时飞走
  &.treasure-reward {
    animation: treasureRewardSequence 3s ease-in-out forwards;
    animation-delay: var(--delay, 0ms);
  }
  
  // 钻石在上方位置
  &.treasure-top {
    top: 35%;
  }
  
  // 金币在下方位置  
  &.treasure-bottom {
    top: 40%;
  }
}

.reward-content {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 20px;
  padding: 10px 20px;
  box-shadow: 0 4px 20px rgba(255, 215, 0, 0.5);
  border: 2px solid #fff;
  transform-style: preserve-3d;
  position: relative;

  
  // 添加背面效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #FF6B35, #FF8C42);
    border-radius: 18px;
    transform: rotateY(180deg);
    backface-visibility: hidden;
    z-index: -1;
  }
  
  // 正面内容
  > * {
    backface-visibility: hidden;
  }
}

.reward-icon {
  width: 40px;
  height: 40px;
  margin-right: 10px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}


.reward-amount {
  font-size: 24px;
  font-weight: bold;
  color: #fff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}



// 单个奖励简化动画序列 - 直接飞向目标位置
@keyframes simpleRewardSequence {
  // 弹出阶段 (0% - 20%)
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }
  10% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }
  20% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  
  // 短暂停留 (20% - 30%)
  30% {
    transform: translate(-50%, -50%) scale(1.05);
    opacity: 1;
  }
  // 80% {
  //   transform: translate(calc(var(--target-x) - 50vw), calc(var(--target-y) - 40vh)) scale(1.1);
  //   opacity: 1;
  // }
  // 直接飞行 (30% - 100%)
  100% {
    transform: translate(
      calc(var(--target-x) - 50vw), 
      calc(var(--target-y) - 40vh)
    ) scale(0.2);
    opacity: 0;
  }
}

// 来自宝箱的奖励动画序列 - 停留翻转同时飞走
@keyframes treasureRewardSequence {
  // 弹出阶段 (0% - 15%)
  0% {
    transform: translate(-50%, -50%) scale(0) rotateY(0deg);
    opacity: 0;
  }
  10% {
    transform: translate(-50%, -50%) scale(1.2) rotateY(0deg);
    opacity: 1;
  }
  15% {
    transform: translate(-50%, -50%) scale(1) rotateY(0deg);
    opacity: 1;
  }
  
  // 停留呼吸阶段 (15% - 50%)
  25% {
    transform: translate(-50%, -50%) scale(1.05) rotateY(0deg);
  }
  35% {
    transform: translate(-50%, -50%) scale(1) rotateY(0deg);
  }
  45% {
    transform: translate(-50%, -50%) scale(1.08) rotateY(0deg);
  }
  50% {
    transform: translate(-50%, -50%) scale(1) rotateY(0deg);
  }
  
  // 翻转阶段 (50% - 70%)
  55% {
    transform: translate(-50%, -50%) scale(1.1) rotateY(60deg);
    box-shadow: 0 6px 25px rgba(255, 215, 0, 0.7);
  }
  60% {
    transform: translate(-50%, -50%) scale(1.15) rotateY(180deg);
    box-shadow: 0 8px 30px rgba(255, 107, 53, 0.8);
  }
  65% {
    transform: translate(-50%, -50%) scale(1.1) rotateY(300deg);
    box-shadow: 0 6px 25px rgba(255, 215, 0, 0.7);
  }
  70% {
    transform: translate(-50%, -50%) scale(1) rotateY(360deg);
    box-shadow: 0 4px 20px rgba(255, 215, 0, 0.5);
  }
  
  // 飞行阶段 (70% - 100%)
  100% {
    transform: translate(
      calc(var(--target-x) - 50vw), 
      calc(var(--target-y) - 40vh)
    ) scale(0.2) rotateY(360deg);
    opacity: 0;
  }
}

// 过渡效果
.reward-enter-active {
  transition: all 0.3s ease-out;
}

.reward-leave-active {
  transition: all 0.3s ease-in;
}

.reward-enter-from {
  opacity: 0;
  transform: scale(0);
}

.reward-leave-to {
  opacity: 0;
  transform: scale(0);
}

</style> 