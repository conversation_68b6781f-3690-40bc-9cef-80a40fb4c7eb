<template>
  <van-popup 
    v-model:show="localShow" 
    position="center" 
    class="passive-income-popup"
  >
    <div class="passive-income-content">
      <div class="header">
        <h3>{{ t('passiveIncome.title') }}</h3>
        <van-icon name="cross" @click="localShow = false" class="close-icon" />
      </div>

      <!-- 收益状态 -->
      <div class="income-status" v-if="incomeStatus">
        <div class="offline-time">
          <span class="label">{{ t('passiveIncome.offlineTime') }}:</span>
          <span class="value">{{ formatTime(incomeStatus.offline_hours) }}</span>
        </div>
        
        <div class="income-preview">
          <div class="income-item">
            <img src="/img/page_icons/Coin.png" class="icon" />
            <span class="amount">{{ incomeStatus.civilization_exp_earned || 0 }}</span>
          </div>
          <div class="income-item">
            <img src="/img/page_icons/Gem.png" class="icon" />
            <span class="amount">{{ incomeStatus.stamina_earned || 0 }}</span>
          </div>
        </div>

        <div class="income-rate">
          <span>{{ t('passiveIncome.hourlyRate') }}: {{ incomeStatus.civilization_exp_per_hour || 0 }} 文明经验值/{{ t('common.hour') }}, {{ incomeStatus.stamina_per_hour || 0 }} 体力值/{{ t('common.hour') }}</span>
        </div>

        <!-- 守护等级加成 -->
        <div class="guardian-bonus" v-if="incomeStatus.guardian_level > 0">
          <span>{{ t('passiveIncome.guardianBonus') }}: +{{ Math.round((incomeStatus.bonus_multiplier - 1) * 100) }}%</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <van-button 
          type="primary" 
          :disabled="!canCollect"
          @click="collectIncome(false)"
          :loading="isCollecting"
          block
        >
          {{ t('passiveIncome.collect') }}
        </van-button>
        
        <van-button 
          type="warning" 
          :disabled="!canCollect"
          @click="collectIncome(true)"
          :loading="isCollecting"
          block
          class="ad-button"
        >
          {{ t('passiveIncome.collectWithAd') }} (x2)
        </van-button>
      </div>

      <!-- 守护等级信息 -->
      <div class="guardian-info" v-if="guardianInfo">
        <div class="section-title">{{ t('guardian.title') }}</div>
        <div class="guardian-level">
          <span>{{ t('guardian.level') }}: {{ guardianInfo.level }}</span>
          <van-button size="mini" @click="showGuardianDetail = true">{{ t('common.details') }}</van-button>
        </div>
        <div class="guardian-exp">
          <van-progress 
            :percentage="guardianExpPercentage" 
            :stroke-width="6"
            color="#4fc08d"
          />
          <span class="exp-text">{{ guardianInfo.experience }} / {{ guardianInfo.next_level_experience }}</span>
        </div>
      </div>
    </div>
  </van-popup>

  <!-- 守护等级详情弹窗 -->
  <GuardianDetail v-model:show="showGuardianDetail" />
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { showSuccessToast, showFailToast } from 'vant'
import api from '@/utils/api'
import GuardianDetail from './GuardianDetail.vue'

const { t } = useI18n()

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:show'])

// Local state
const localShow = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const incomeStatus = ref(null)
const guardianInfo = ref(null)
const isCollecting = ref(false)
const showGuardianDetail = ref(false)

// Computed
const canCollect = computed(() => {
  return incomeStatus.value && incomeStatus.value.can_collect
})

const guardianExpPercentage = computed(() => {
  if (!guardianInfo.value || guardianInfo.value.next_level_experience === 0) return 0
  return Math.round((guardianInfo.value.experience / guardianInfo.value.next_level_experience) * 100)
})

// Methods
const formatTime = (hours) => {
  if (hours < 1) {
    const minutes = Math.round(hours * 60)
    return `${minutes} ${t('common.minutes')}`
  } else {
    const wholeHours = Math.floor(hours)
    const minutes = Math.round((hours - wholeHours) * 60)
    return minutes > 0 ? `${wholeHours}${t('common.hours')} ${minutes}${t('common.minutes')}` : `${wholeHours}${t('common.hours')}`
  }
}

const loadIncomeStatus = async () => {
  try {
    const response = await api.passiveIncome.getStatus()
    if (response.data.success) {
      incomeStatus.value = response.data.data
    }
  } catch (error) {
    console.error('加载被动收益状态失败:', error)
  }
}

const loadGuardianInfo = async () => {
  try {
    const response = await api.passiveIncome.getGuardianInfo()
    if (response.data.success) {
      guardianInfo.value = response.data.data
    }
  } catch (error) {
    console.error('加载守护等级信息失败:', error)
  }
}

const collectIncome = async (watchAd = false) => {
  if (!canCollect.value) return
  
  isCollecting.value = true
  try {
    const response = await api.passiveIncome.claim(watchAd)
    if (response.data.success) {
      const data = response.data.data
      const goldEarned = data.gold_earned || 0
      const diamondEarned = data.diamond_earned || 0
      
      showSuccessToast(`获得 ${goldEarned} 金币${diamondEarned > 0 ? `, ${diamondEarned} 钻石` : ''}`)
      
      // 刷新状态
      await loadIncomeStatus()
      
      // 通知父组件更新资源
      emit('resource-updated', {
        gold: data.new_total_gold,
        diamond: data.new_total_diamond
      })
    } else {
      showFailToast(response.data.message || '领取失败')
    }
  } catch (error) {
    console.error('领取被动收益失败:', error)
    showFailToast('领取失败')
  } finally {
    isCollecting.value = false
  }
}

// Watch for show changes
watch(() => props.show, (newShow) => {
  if (newShow) {
    loadIncomeStatus()
    loadGuardianInfo()
  }
})

onMounted(() => {
  if (props.show) {
    loadIncomeStatus()
    loadGuardianInfo()
  }
})
</script>

<style scoped>
.passive-income-popup {
  border-radius: 12px;
  overflow: hidden;
}

.passive-income-content {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}

.close-icon {
  font-size: 20px;
  cursor: pointer;
}

.income-status {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.offline-time {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.income-preview {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 15px 0;
}

.income-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 12px;
  border-radius: 6px;
}

.income-item .icon {
  width: 24px;
  height: 24px;
}

.income-item .amount {
  font-weight: bold;
  font-size: 22px;
}

.income-rate, .guardian-bonus {
  text-align: center;
  font-size: 18px;
  opacity: 0.8;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.ad-button {
  background: linear-gradient(45deg, #ff6b6b, #ffa500) !important;
  border: none !important;
}

.guardian-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 15px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
}

.guardian-level {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.guardian-exp {
  display: flex;
  align-items: center;
  gap: 10px;
}

.exp-text {
  font-size: 18px;
  min-width: 180px;
  text-align: right;
}

.van-progress {
  flex: 1;
}
</style>