<template>
  <van-popup
    v-model:show="visible"
    position="center"
    :style="{ background: 'transparent' }"
    :duration="0.3"
    @update:show="$emit('update:show', $event)"
  >
    <div class="treasure-box-container" v-if="treasureBox">
      <!-- 宝箱动画 -->
      <div class="treasure-box-animation">
        <img 
          class="treasure-box-icon" 
          src="/img/frame_ani/chest_ani_256x256.png" 
          alt="宝箱"
        />
        <div class="sparkle-effect"></div>
      </div>
      
      <!-- 宝箱内容 -->
      <div class="treasure-content">
        <div class="content-header">
          <h3>发现宝箱！</h3>
          <p>从{{ treasureBox.sourceType === 'thief' ? '小偷' : '垃圾' }}中掉落</p>
        </div>
        
        <div class="treasure-item">
          <img 
            :src="getTreasureIcon(treasureBox.type)" 
            :alt="treasureBox.name"
            class="treasure-item-icon"
          />
          <div class="treasure-item-info">
            <div class="treasure-name">{{ treasureBox.name }}</div>
            <div class="treasure-amount" v-if="treasureBox.amount">
              x{{ treasureBox.amount }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="treasure-actions">
        <van-button 
          type="primary" 
          size="large" 
          round
          class="claim-btn"
          @click="claimNormal"
        >
          领取奖励
        </van-button>
        
        <van-button 
          type="warning" 
          size="large" 
          round
          class="double-btn"
          @click="claimDouble"
        >
          观看广告 获得双倍
        </van-button>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { computed } from 'vue'
import { showLoadingToast } from 'vant'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  treasureBox: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show', 'claim'])

const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const getTreasureIcon = (type) => {
  const iconMap = {
    'civilization_exp': '/img/page_icons/Coin.png',
    'stamina': '/img/page_icons/Gem.png',
    'cultural_atlas': '/img/page_icons/collection_item.png',
    'tools': '/img/page_icons/radar-2.png',
    'repentance_letter': '/img/page_icons/collections_icon.png',
    'avatar_position': '/img/page_icons/user_avatar.png'
  }
  return iconMap[type] || '/img/page_icons/Coin.png'
}

const claimNormal = () => {
  emit('claim', false)
  visible.value = false
}

const claimDouble = async () => {
  showLoadingToast('观看广告中...')
  
  // 模拟广告观看
  setTimeout(() => {
    emit('claim', true)
    visible.value = false
  }, 2000)
}
</script>

<style lang="scss" scoped>
.treasure-box-container {
  width: 350px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
  border: 3px solid #FFD700;
}

.treasure-box-animation {
  position: relative;
  margin-bottom: 20px;
  
  .treasure-box-icon {
    width: 120px;
    height: 120px;
    animation: treasureFloat 2s ease-in-out infinite;
  }
  
  .sparkle-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
    border-radius: 50%;
    animation: sparkle 1.5s ease-in-out infinite;
  }
}

@keyframes treasureFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes sparkle {
  0%, 100% { opacity: 0; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.2); }
}

.treasure-content {
  margin-bottom: 25px;
  
  .content-header {
    margin-bottom: 15px;
    
    h3 {
      color: #8B4513;
      font-size: 24px;
      margin: 0 0 5px 0;
      font-weight: bold;
    }
    
    p {
      color: #A0522D;
      font-size: 14px;
      margin: 0;
    }
  }
}

.treasure-item {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 15px;
  margin: 10px 0;
  
  .treasure-item-icon {
    width: 50px;
    height: 50px;
    margin-right: 15px;
  }
  
  .treasure-item-info {
    flex: 1;
    text-align: left;
    
    .treasure-name {
      font-size: 18px;
      font-weight: bold;
      color: #8B4513;
      margin-bottom: 5px;
    }
    
    .treasure-amount {
      font-size: 16px;
      color: #A0522D;
    }
  }
}

.treasure-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  .claim-btn {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    border: none;
    font-weight: bold;
  }
  
  .double-btn {
    background: linear-gradient(135deg, #FF6B35, #FF4500);
    border: none;
    font-weight: bold;
  }
}

:deep(.van-button) {
  height: 45px;
  font-size: 16px;
}
</style>