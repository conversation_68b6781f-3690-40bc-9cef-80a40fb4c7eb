<template>
  <div class="collection-stats-card">
    <div class="card-header">
      <div class="header-icon">📊</div>
      <div class="header-text">
        <h3>{{ t('collection.statsTitle') }}</h3>
        <p>{{ t('collection.statsSubtitle') }}</p>
      </div>
    </div>

    <div class="card-body">
      <!-- 总体统计 -->
      <div class="overall-stats">
        <div class="stat-circle total">
          <div class="stat-number">{{ totalCollections }}</div>
          <div class="stat-label">{{ t('collection.total') }}</div>
        </div>
        <div class="stat-details">
          <div class="detail-item">
            <span class="detail-icon">🎯</span>
            <span class="detail-text">{{ t('collection.todayCollected', { count: todayCollections }) }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-icon">🏆</span>
            <span class="detail-text">{{ t('collection.bestDay', { count: bestDayCollections }) }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-icon">📈</span>
            <span class="detail-text">{{ t('collection.avgPerDay', { count: avgPerDay }) }}</span>
          </div>
        </div>
      </div>

      <!-- 分类统计 -->
      <div class="category-stats">
        <h4>{{ t('collection.byCategory') }}</h4>
        <div class="category-grid">
          <!-- 小偷统计 -->
          <div class="category-card thief" @click="showCategoryDetail('thief')">
            <div class="category-icon">🕵️</div>
            <div class="category-data">
              <div class="category-count">{{ thievesCount }}</div>
              <div class="category-label">{{ t('collection.thieves') }}</div>
              <div class="category-progress">
                <div class="progress-bar">
                  <div 
                    class="progress-fill thief-fill" 
                    :style="{ width: thievesProgress + '%' }"
                  ></div>
                </div>
                <span class="progress-text">{{ thievesProgress }}%</span>
              </div>
            </div>
          </div>

          <!-- 垃圾统计 -->
          <div class="category-card garbage" @click="showCategoryDetail('garbage')">
            <div class="category-icon">🗑️</div>
            <div class="category-data">
              <div class="category-count">{{ garbageCount }}</div>
              <div class="category-label">{{ t('collection.garbage') }}</div>
              <div class="category-progress">
                <div class="progress-bar">
                  <div 
                    class="progress-fill garbage-fill" 
                    :style="{ width: garbageProgress + '%' }"
                  ></div>
                </div>
                <span class="progress-text">{{ garbageProgress }}%</span>
              </div>
            </div>
          </div>

          <!-- 古迹统计 -->
          <div class="category-card monument" @click="showCategoryDetail('monument')">
            <div class="category-icon">🏛️</div>
            <div class="category-data">
              <div class="category-count">{{ monumentsCount }}</div>
              <div class="category-label">{{ t('collection.monuments') }}</div>
              <div class="category-progress">
                <div class="progress-bar">
                  <div 
                    class="progress-fill monument-fill" 
                    :style="{ width: monumentsProgress + '%' }"
                  ></div>
                </div>
                <span class="progress-text">{{ monumentsProgress }}%</span>
              </div>
            </div>
          </div>

          <!-- 宝箱统计 -->
          <div class="category-card treasure" @click="showCategoryDetail('treasure')">
            <div class="category-icon">📦</div>
            <div class="category-data">
              <div class="category-count">{{ treasureCount }}</div>
              <div class="category-label">{{ t('collection.treasures') }}</div>
              <div class="category-progress">
                <div class="progress-bar">
                  <div 
                    class="progress-fill treasure-fill" 
                    :style="{ width: treasureProgress + '%' }"
                  ></div>
                </div>
                <span class="progress-text">{{ treasureProgress }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 成就徽章 -->
      <div class="achievements-section">
        <h4>{{ t('collection.achievements') }}</h4>
        <div class="achievements-grid">
          <div 
            v-for="achievement in achievements" 
            :key="achievement.id"
            class="achievement-badge"
            :class="{ unlocked: achievement.unlocked, featured: achievement.featured }"
          >
            <div class="achievement-icon">{{ achievement.icon }}</div>
            <div class="achievement-name">{{ achievement.name }}</div>
            <div class="achievement-desc">{{ achievement.description }}</div>
            <div class="achievement-progress" v-if="!achievement.unlocked">
              {{ achievement.current }} / {{ achievement.target }}
            </div>
          </div>
        </div>
      </div>

      <!-- 收集效率 -->
      <div class="efficiency-section">
        <h4>{{ t('collection.efficiency') }}</h4>
        <div class="efficiency-stats">
          <div class="efficiency-item">
            <div class="efficiency-icon">⚡</div>
            <div class="efficiency-data">
              <div class="efficiency-value">{{ collectionRate }}</div>
              <div class="efficiency-label">{{ t('collection.perMinute') }}</div>
            </div>
          </div>
          <div class="efficiency-item">
            <div class="efficiency-icon">🎯</div>
            <div class="efficiency-data">
              <div class="efficiency-value">{{ accuracyRate }}%</div>
              <div class="efficiency-label">{{ t('collection.accuracy') }}</div>
            </div>
          </div>
          <div class="efficiency-item">
            <div class="efficiency-icon">🔥</div>
            <div class="efficiency-data">
              <div class="efficiency-value">{{ currentStreak }}</div>
              <div class="efficiency-label">{{ t('collection.streak') }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="card-footer">
      <van-button 
        type="primary" 
        size="small" 
        @click="showDetailedStats"
        icon="chart-trending-o"
      >
        {{ t('collection.viewDetails') }}
      </van-button>
      <van-button 
        type="success" 
        size="small" 
        @click="shareStats"
        icon="share-o"
      >
        {{ t('collection.share') }}
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useGameStore } from '@/stores/game'

const { t } = useI18n()
const gameStore = useGameStore()

// 计算属性
const thievesCount = computed(() => gameStore.userInfo?.thieves_captured || 0)
const garbageCount = computed(() => gameStore.userInfo?.garbage_cleaned || 0)
const monumentsCount = computed(() => gameStore.userInfo?.monuments_protected || 0)
const treasureCount = computed(() => gameStore.userInfo?.treasures_opened || 0)

const totalCollections = computed(() => 
  thievesCount.value + garbageCount.value + monumentsCount.value + treasureCount.value
)

// 根据PRD，每个地图约300个小偷、200个垃圾
const thievesProgress = computed(() => Math.min((thievesCount.value / 300) * 100, 100))
const garbageProgress = computed(() => Math.min((garbageCount.value / 200) * 100, 100))
const monumentsProgress = computed(() => Math.min((monumentsCount.value / 50) * 100, 100))
const treasureProgress = computed(() => Math.min((treasureCount.value / 100) * 100, 100))

// 模拟统计数据
const todayCollections = computed(() => Math.floor(totalCollections.value * 0.1))
const bestDayCollections = computed(() => Math.floor(totalCollections.value * 0.2))
const avgPerDay = computed(() => Math.floor(totalCollections.value / 7))

const collectionRate = computed(() => (totalCollections.value / 60).toFixed(1))
const accuracyRate = computed(() => Math.min(95, 80 + (totalCollections.value / 100)))
const currentStreak = computed(() => Math.floor(totalCollections.value / 10))

// 成就系统
const achievements = computed(() => [
  {
    id: 'first_thief',
    name: t('achievements.firstThief'),
    description: t('achievements.firstThiefDesc'),
    icon: '🎯',
    unlocked: thievesCount.value >= 1,
    current: thievesCount.value,
    target: 1,
    featured: false
  },
  {
    id: 'thief_hunter',
    name: t('achievements.thiefHunter'),
    description: t('achievements.thiefHunterDesc'),
    icon: '🏹',
    unlocked: thievesCount.value >= 100,
    current: thievesCount.value,
    target: 100,
    featured: thievesCount.value >= 100
  },
  {
    id: 'clean_city',
    name: t('achievements.cleanCity'),
    description: t('achievements.cleanCityDesc'),
    icon: '🧹',
    unlocked: garbageCount.value >= 50,
    current: garbageCount.value,
    target: 50,
    featured: false
  },
  {
    id: 'guardian_master',
    name: t('achievements.guardianMaster'),
    description: t('achievements.guardianMasterDesc'),
    icon: '👑',
    unlocked: totalCollections.value >= 1000,
    current: totalCollections.value,
    target: 1000,
    featured: totalCollections.value >= 1000
  }
])

// 方法
const showCategoryDetail = (category) => {
  console.log('Show category detail:', category)
  // 显示分类详细统计
}

const showDetailedStats = () => {
  console.log('Show detailed stats')
  // 显示详细统计页面
}

const shareStats = () => {
  console.log('Share stats')
  // 分享统计数据
}
</script>

<style scoped>
.collection-stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 20px;
  color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.header-icon {
  font-size: 32px;
  margin-right: 15px;
}

.header-text h3 {
  margin: 0;
  font-size: 20px;
}

.header-text p {
  margin: 5px 0 0 0;
  opacity: 0.8;
  font-size: 14px;
}

.overall-stats {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
}

.stat-circle {
  width: 100px;
  height: 100px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  background: rgba(255, 255, 255, 0.1);
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

.stat-details {
  flex: 1;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-icon {
  margin-right: 8px;
  font-size: 16px;
}

.category-stats {
  margin-bottom: 25px;
}

.category-stats h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
}

.category-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.category-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.category-card:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.category-icon {
  font-size: 24px;
  margin-right: 12px;
}

.category-data {
  flex: 1;
}

.category-count {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 2px;
}

.category-label {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 8px;
}

.category-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.5s ease;
}

.thief-fill { background: linear-gradient(90deg, #ff6b6b, #ee5a52); }
.garbage-fill { background: linear-gradient(90deg, #4ecdc4, #44a08d); }
.monument-fill { background: linear-gradient(90deg, #feca57, #ff9ff3); }
.treasure-fill { background: linear-gradient(90deg, #ff9a9e, #fecfef); }

.progress-text {
  font-size: 10px;
  font-weight: bold;
}

.achievements-section {
  margin-bottom: 25px;
}

.achievements-section h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
}

.achievements-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.achievement-badge {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 12px;
  text-align: center;
  transition: all 0.3s ease;
}

.achievement-badge.unlocked {
  background: rgba(255, 215, 0, 0.2);
  border: 2px solid rgba(255, 215, 0, 0.5);
}

.achievement-badge.featured {
  animation: glow 2s infinite;
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 10px rgba(255, 215, 0, 0.5); }
  50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
}

.achievement-icon {
  font-size: 20px;
  margin-bottom: 5px;
}

.achievement-name {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 3px;
}

.achievement-desc {
  font-size: 10px;
  opacity: 0.8;
  margin-bottom: 5px;
}

.achievement-progress {
  font-size: 10px;
  color: #ffd700;
}

.efficiency-section {
  margin-bottom: 20px;
}

.efficiency-section h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
}

.efficiency-stats {
  display: flex;
  justify-content: space-around;
}

.efficiency-item {
  text-align: center;
}

.efficiency-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

.efficiency-value {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 2px;
}

.efficiency-label {
  font-size: 12px;
  opacity: 0.8;
}

.card-footer {
  display: flex;
  gap: 10px;
  justify-content: center;
}
</style>
