<template>
  <van-popup
    v-model:show="showDialog"
    position="center"
    :style="{ background: 'transparent' }"
    :duration="0.3"
    @update:show="$emit('update:show', $event)"
  >
    <div class="reward-container" v-if="reward">
      <!-- 奖励动画 -->
      <div class="reward-animation">
        <img
          v-if="reward.type === 'experience'"
          class="reward-icon"
          src="/img/page_icons/Coin.png"
          alt="经验值"
        />
        <img
          v-else-if="reward.type === 'artifact'"
          class="reward-icon"
          src="/img/puzzle_ani_256x256.edc5a41f.png"
          alt="图鉴"
        />
      </div>
      
      <!-- 奖励信息 -->
      <div class="reward-info">
        <div class="reward-message">{{ getRewardMessage(reward) }}</div>
        <div class="reward-amount">+{{ reward.amount }}</div>
      </div>
      
      <!-- 双倍奖励按钮 -->
      <div v-if="reward.type === 'gold'" class="double-reward">
        <van-button 
          type="warning" 
          size="large" 
          round
          @click="watchAdForDouble"
        >
          {{ t('game.rewards.watchAdForDouble') }}
        </van-button>
      </div>
      
      <!-- 确认按钮 -->
      <van-button 
        type="primary" 
        size="large" 
        round
        @click="confirm"
      >
        {{ t('common.confirm') }}
      </van-button>
    </div>
  </van-popup>
</template>

<script setup>

import { useGameStore } from '@/stores/game'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  reward: {
    type: Object,
    default: null
  }
})
const showDialog = ref(props.modelValue)
const emit = defineEmits(['update:show'])

const gameStore = useGameStore()
const { t } = useI18n()

// 获取奖励消息
const getRewardMessage = (reward) => {
  if (!reward) return ''
  
  const messageMap = {
    '抓到小偷！': 'game.rewards.caughtThief',
    '清理垃圾！': 'game.rewards.cleanedGarbage',
    '发现宝箱！': 'game.rewards.foundTreasure'
  }
  
  return messageMap[reward.message] ? t(messageMap[reward.message]) : reward.message
}

// 看广告获得双倍奖励
const watchAdForDouble = () => {
  showToast(t('game.tools.loadingAd'))
  // TODO: 实际的广告逻辑
  setTimeout(() => {
    if (props.reward) {
      gameStore.addCivilizationExp(props.reward.amount)
      showToast(t('game.rewards.gotDoubleReward', { amount: props.reward.amount }))
    }
    emit('update:show', false)
  }, 2000)
}

// 确认
const confirm = () => {
  emit('update:show', false)
}
</script>

<style lang="scss" scoped>
.reward-container {
  background: linear-gradient(to bottom, #2a2a3e, #1a1a2e);
  border-radius: $radius-lg;
  padding: 40px;
  text-align: center;
  min-width: 400px;
  box-shadow: $shadow-lg;
  border: 2px solid $primary-color;
}

.reward-animation {
  margin-bottom: 15px;
  
  .reward-icon {
    width: 100px;
    height: 100px;
    animation: bounceIn 0.6s ease-out, float 2s ease-in-out infinite;
  }
}

.reward-info {
  margin-bottom: 15px;
  
  .reward-message {
    font-size: $font-xl;
    color: $text-white;
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  .reward-amount {
    font-size: 48px;
    color: $primary-color;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  }
}

.double-reward {
  margin-bottom: 10px;
}

// 动画
@keyframes bounceIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}
</style> 