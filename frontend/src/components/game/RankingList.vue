<template>
  <van-popup 
    v-model:show="visible" 
    position="center" 
    :style="{ overflow: 'visible' }"
    closeable
    :close-icon="'/img/page_icons/close-btn.png'"
    round
  >
    <div class="ranking-list" >
      <!-- 标题和排行类型切换 -->
      <div class="ranking-header">
        <div class="ranking-title">
          <span>Ranking</span>
        </div>
        <div class="ranking-tabs">
          <button 
            class="ranking-tab" 
            :class="{ active: rankingType === 'civilization_exp' }"
            @click="switchRankingType('civilization_exp')"
          >
            文明经验
          </button>
          <button 
            class="ranking-tab" 
            :class="{ active: rankingType === 'collections' }"
            @click="switchRankingType('collections')"
          >
            图鉴收集
          </button>
        </div>
      </div>
      
      <!-- 刷新时间 -->
      <div class="refresh-time" v-if="leaderboardData?.update_time">
        <span>Refresh timer: {{ formatRefreshTime(leaderboardData.update_time) }}</span>
      </div>
      
      <!-- 前三名特殊展示 -->
      <div class="top-three" v-if="!loading && topThree.length > 0">
        <!-- 第二名 -->
        <div class="top-player second" v-if="topThree[1]">
          <div class="medal-wrapper">
            <img src="/img/page_icons/2nd_medal.png" class="medal" alt="2nd" />
            <div class="avatar-frame">
              <div class="avatar-frame-bg">
                <img :src="topThree[1].avatar" class="avatar" alt="avatar" />
              </div>
              
            </div>
          </div>
          <div class="player-name">{{ topThree[1].nickname }}</div>
          <div class="player-score">
            <img :src="getScoreIcon()" class="score-icon" :alt="rankingType" />
            <span>{{ getPlayerScore(topThree[1]) }}</span>
          </div>
        </div>
        
        <!-- 第一名 -->
        <div class="top-player first" v-if="topThree[0]">
          <div class="medal-wrapper">
            <img src="/img/page_icons/1st_medal.png" class="medal" alt="1st" />
            <div class="avatar-frame first-frame">
              <div class="avatar-frame-bg">
                <img :src="topThree[0].avatar" class="avatar" alt="avatar" />
              </div>
            </div>
          </div>
          <div class="player-name">{{ topThree[0].nickname }}</div>
          <div class="player-score">
            <img :src="getScoreIcon()" class="score-icon" :alt="rankingType" />
            <span>{{ getPlayerScore(topThree[0]) }}</span>
          </div>
        </div>
        
        <!-- 第三名 -->
        <div class="top-player third" v-if="topThree[2]">
          <div class="medal-wrapper">
            <img src="/img/page_icons/3rd_medal.png" class="medal" alt="3rd" />
            <div class="avatar-frame">
              <div class="avatar-frame-bg">
                <img :src="topThree[2].avatar" class="avatar" alt="avatar" />
              </div>
            </div>
          </div>
          <div class="player-name">{{ topThree[2].nickname }}</div>
          <div class="player-score">
            <img :src="getScoreIcon()" class="score-icon" :alt="rankingType" />
            <span>{{ getPlayerScore(topThree[2]) }}</span>
          </div>
        </div>
      </div>
      
      <!-- 排行榜列表 -->
      <div class="ranking-table" v-if="!loading && otherRankings.length > 0">
        <div 
          v-for="player in otherRankings" 
          :key="player.user_id" 
          class="ranking-item"
          :class="{ 'current-player': isCurrentPlayer(player.user_id) }"
        >
          <div class="rank-number">{{ player.rank }}</div>
          <div class="player-avatar">
            <img :src="player.avatar || '/img/page_icons/user_avatar.png'" alt="avatar" />
          </div>
          <div class="player-name">{{ isCurrentPlayer(player.user_id) ? 'YOU' : player.nickname }}</div>
          <div class="player-score">
            <img :src="getScoreIcon()" class="score-icon" :alt="rankingType" />
            <span>{{ getPlayerScore(player) }}</span>
          </div>
        </div>
      </div>
      
      <!-- 当前玩家排名（如果不在显示列表中，悬浮在底部） -->
      <div 
        v-if="currentPlayerRank && !isInDisplayList && !isInTopThree"
        class="current-player-floating"
      >
        <div class="ranking-item current-player">
          <div class="rank-number is-you">{{currentPlayerRank.rank}}</div>
          <div class="player-avatar">
            <img :src="currentPlayerRank.avatar || '/img/page_icons/user_avatar.png'" alt="avatar" />
          </div>
          <div class="player-name">YOU</div>
          <div class="player-score">
            <img :src="getScoreIcon()" class="score-icon" :alt="rankingType" />
            <span>{{ getPlayerScore(currentPlayerRank) }}</span>
          </div>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <van-loading size="24px" color="#FFD700">Loading...</van-loading>
      </div>
      
      <!-- 空状态 -->
      <div v-if="!loading && displayRankings.length === 0" class="empty-state">
        No ranking data available
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useAuthStore } from '@/stores/auth'
import gameService from '@/services/gameService'
import { showToast } from 'vant'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:show'])

const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const authStore = useAuthStore()

// 状态
const loading = ref(false)
const leaderboardData = ref(null)
const currentPlayerData = ref(null)
const rankingType = ref('collections') // 默认显示图鉴收集排行


// 计算属性 - 确保正确处理异步数据
const displayRankings = computed(() => {
  // 添加更严格的数据验证
  if (!leaderboardData.value) {
    return []
  }
  
  if (!leaderboardData.value.rankings) {
    return []
  }
  
  if (!Array.isArray(leaderboardData.value.rankings)) {
    return []
  }
  
  const rankings = leaderboardData.value.rankings
  console.log('=== displayRankings计算属性触发 ===')
  console.log('有效rankings数组，长度:', rankings.length)
  
  return rankings
})

// 前三名
const topThree = computed(() => {
  const rankings = displayRankings.value
  if (!rankings || rankings.length === 0) {
    return []
  }
  const result = rankings.slice(0, 3)
  return result
})

// 其他排名（第4名开始，显示前30名）
const otherRankings = computed(() => {
  const rankings = displayRankings.value
  if (!rankings || rankings.length <= 3) {
    return []
  }
  const result = rankings.slice(3, 30)
  return result
})

// 当前玩家排名信息
const currentPlayerRank = computed(() => {
  if (!currentPlayerData.value) return null
  
  return {
    rank: leaderboardData.value?.my_rank,
    user_id: currentPlayerData.value.user_id || authStore.userInfo?.user_id,
    nickname: currentPlayerData.value.nickname || authStore.userInfo?.nickname || 'YOU',
    avatar: currentPlayerData.value.avatar || authStore.userInfo?.avatar || '/img/page_icons/user_avatar.png',
    artifacts_collected: currentPlayerData.value.artifacts_collected || 0
  }
})

// 检查当前玩家是否在显示列表中（前30名）
const isInDisplayList = computed(() => {
  if (!authStore.userInfo?.user_id) return false
  return displayRankings.value.slice(0, 30).some(p => p.user_id === authStore.userInfo.user_id)
})

// 检查当前玩家是否在前三名
const isInTopThree = computed(() => {
  if (!authStore.userInfo?.user_id) return false
  return topThree.value.some(p => p.user_id === authStore.userInfo.user_id)
})

// 检查是否是当前玩家
const isCurrentPlayer = (userId) => {
  return authStore.userInfo?.user_id === userId
}

// 获取当前玩家数据
const loadCurrentPlayerData = async () => {
  try {
    if (!authStore.userInfo?.user_id) {
      return
    }
    
    // 尝试从游戏服务获取当前玩家的排名和收集数据
    try {
      const result = await gameService.getPlayerProgress(authStore.userInfo.user_id)
      
      if (result.success && result.data) {
        currentPlayerData.value = {
          user_id: authStore.userInfo.user_id,
          nickname: authStore.userInfo.nickname || 'YOU',
          avatar: authStore.userInfo.avatar || '/img/page_icons/user_avatar.png',
          artifacts_collected: result.data.artifacts_collected || result.data.total_artifacts || 0,
          rank: result.data.rank || leaderboardData.value?.my_rank
        }
        return
      }
    } catch (apiError) {
    }
    
    // 如果API失败，使用store中的数据或默认值
    currentPlayerData.value = {
      user_id: authStore.userInfo.user_id,
      nickname: authStore.userInfo.nickname || 'YOU',
      avatar: authStore.userInfo.avatar || '/img/page_icons/user_avatar.png',
      artifacts_collected: authStore.userInfo?.artifacts_collected || 
                         authStore.userInfo?.game_progress?.artifacts_collected || 
                         authStore.userInfo?.total_artifacts ||
                         98, // 默认值
      rank: leaderboardData.value?.my_rank || 999
    }
  } catch (error) {
    // 使用默认值
    currentPlayerData.value = {
      user_id: authStore.userInfo?.user_id,
      nickname: authStore.userInfo?.nickname || 'YOU',
      avatar: authStore.userInfo?.avatar || '/img/page_icons/user_avatar.png',
      artifacts_collected: 98,
      rank: 999
    }
  }
}

// 标准化排行榜数据格式
const normalizeLeaderboardData = (apiData) => {
  
  const normalized = {
    rankings: apiData.rankings.map(player => ({
      user_id: parseInt(player.user_id) || player.user_id, // 确保user_id是数字（与mock一致）
      nickname: player.nickname || '未知玩家',
      avatar: player.avatar || '/img/page_icons/user_avatar.png', // 添加默认头像
      artifacts_collected: player.artifacts_collected || 0,
      rank: player.rank
    })),
    my_rank: apiData.my_rank,
    update_time: apiData.update_time || new Date().toISOString()
  }
  
  return normalized
}

// 格式化刷新时间
const formatRefreshTime = (timeString) => {
  try {
    const date = new Date(timeString)
    const now = new Date()
    const diff = Math.floor((now - date) / 1000 / 60 / 60) // 小时差
    
    if (diff < 1) {
      return 'Just now'
    } else if (diff < 24) {
      return `${diff} h`
    } else {
      const days = Math.floor(diff / 24)
      return `${days} d`
    }
  } catch (error) {
    return '24 h'
  }
}

// 排行榜类型切换
const switchRankingType = (type) => {
  rankingType.value = type
  loadLeaderboard() // 重新加载排行榜数据
}

// 获取分数图标
const getScoreIcon = () => {
  if (rankingType.value === 'civilization_exp') {
    return '/img/page_icons/START-iocn.png' // 文明经验图标
  } else {
    return '/img/page_icons/collection_item.png' // 图鉴收集图标
  }
}

// 获取玩家分数
const getPlayerScore = (player) => {
  if (rankingType.value === 'civilization_exp') {
    const score = player.civilization_exp || 0
    return score > 999 ? '999+' : score
  } else {
    const score = player.artifacts_collected || 0
    return score > 999 ? '999+' : score
  }
}

// 加载排行榜数据
const loadLeaderboard = async () => {
  if (loading.value) {
    return
  }
  
  loading.value = true
  
  try {
    // 并行加载排行榜数据和当前玩家数据
    await Promise.all([
      loadRankingsData(),
      loadCurrentPlayerData()
    ])
    
    // 等待下一个tick确保所有响应式更新完成
    await nextTick()
    
  } catch (error) {
    showToast('Failed to load data')
  } finally {
    loading.value = false
  }
}

// 加载排行榜数据
const loadRankingsData = async () => {
  try {
    // 优先尝试API调用
    const result = await gameService.getLeaderboard(50, 0)
  
    if (result.success && result.data && result.data.rankings && result.data.rankings.length > 0) {

      
      // 标准化API数据格式
      const normalizedApiData = normalizeLeaderboardData(result.data)
      leaderboardData.value = normalizedApiData
      
      // 使用nextTick确保DOM更新
      await nextTick()
      return
    } else {
      
    }
    
    // API失败时才使用mock数据
    const mockData = {
        rankings: [
          {
            user_id: 1,
            nickname: 'Zhourunfa.li',
            avatar: '/img/page_icons/user_avatar.png',
            artifacts_collected: 156,
            rank: 1
          },
          {
            user_id: 2,
            nickname: 'Emily',   
            avatar: '/img/page_icons/user_avatar.png',
            artifacts_collected: 98,
            rank: 2
          },
          {
            user_id: 3,   
            nickname: 'Liu tao',
            avatar: '/img/page_icons/user_avatar.png',
            artifacts_collected: 88,
            rank: 3
          },
          { 
            user_id: 4,
            nickname: 'Charlotte',
            avatar: '/img/page_icons/user_avatar.png',
            artifacts_collected: 98,
            rank: 4
          },
          { 
            user_id: 5,
            nickname: 'Charlotte',
            avatar: '/img/page_icons/user_avatar.png',
            artifacts_collected: 98,
            rank: 5
          },
          { 
            user_id: 6,
            nickname: 'Charlotte',
            avatar: '/img/page_icons/user_avatar.png',
            artifacts_collected: 98,
            rank: 6
          },
          { 
            user_id: 7,
            nickname: 'Charlotte',
            avatar: '/img/page_icons/user_avatar.png',
            artifacts_collected: 98,
            rank: 7
          },
          { 
            user_id: 8,
            nickname: 'Charlotte',
            avatar: '/img/page_icons/user_avatar.png',
            artifacts_collected: 98,
            rank: 8
          },
          { 
            user_id: 9,
            nickname: 'Charlotte',
            avatar: '/img/page_icons/user_avatar.png',
            artifacts_collected: 98,
            rank: 9
          },
          { 
            user_id: 10,
            nickname: 'Charlotte',
            avatar: '/img/page_icons/user_avatar.png',
            artifacts_collected: 98,
            rank: 10
          },
          { 
            user_id: 11,
            nickname: 'Charlotte',
            avatar: '/img/page_icons/user_avatar.png',
            artifacts_collected: 98,
            rank: 11
          },
          { 
            user_id: 12,
            nickname: 'Charlotte',
            avatar: '/img/page_icons/user_avatar.png',
            artifacts_collected: 98,
            rank: 12
          },
          { 
            user_id: 13,
            nickname: 'Charlotte',
            avatar: '/img/page_icons/user_avatar.png',
            artifacts_collected: 98,
            rank: 13
          }
        ],
        my_rank: 50,
        update_time: new Date().toISOString()
      }
    
    // 设置mock数据
    leaderboardData.value = mockData
    
    // 使用nextTick确保DOM更新
    await nextTick()
  } catch (error) {
    // 确保至少有空的数据结构
    leaderboardData.value = {
      rankings: [],
      my_rank: null,
      update_time: new Date().toISOString()
    }
  }
}

// 监听弹窗显示状态
watch(visible, (newVisible) => {
  if (newVisible) {
    loadLeaderboard()
  }
})

// 组件挂载时如果弹窗已显示则加载数据
onMounted(() => {
  if (visible.value) {
    loadLeaderboard()
  }
})
</script>

<style lang="scss" scoped>
.ranking-list {
  width: 682px;
  // height: 1242px;
  background: url('/img/page_icons/ranking_content_bg.png') no-repeat center center;
  background-size: 100% 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 40px;
}

:deep(.van-overlay) {
  background: rgba(0, 0, 0, 0.5) !important;
}

:deep(.van-popup) {
  overflow: visible;
  background: transparent;
}

:deep(.van-popup__close-icon--top-right) {
 
  top: -26px !important;
  right: -26px !important;
  z-index: 999;
  .van-icon__image{
    width: 66px;
    height: 70px;
  }
}

.ranking-title {
  width: 576px;
  height: 96px;
  background: url('/img/page_icons/ranking_title_bg.png') no-repeat center center;
  background-size: 100% 100%;
  margin-top: -20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  
  span {
    font-size: 32px;
    font-weight: bold;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    font-style: italic;
  }
}

// 排行榜头部样式
.ranking-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.ranking-tabs {
  display: flex;
  gap: 10px;
}

.ranking-tab {
  padding: 8px 16px;
  border: 2px solid #fff;
  background: transparent;
  color: #fff;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
  
  &.active {
    background: #fff;
    color: #333;
    font-weight: bold;
  }
}

.refresh-time {
  position: absolute;
  top: 90px;
  right: 60px;
  font-size: 12px;
  color: #fff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  
  span {
    opacity: 0.9;
  }
}

.top-three {
  display: flex;
  justify-content: space-evenly;
  align-items: flex-end;
  width: 654px;
  height: 436px;
  // border-radius: 10px;
  margin-top: -72px;
  // margin-bottom: 20px;
  padding-bottom: 20px;
  padding-top: 80px;
  // border-bottom: 1px solid #000;
  // background: linear-gradient(to bottom, #DDDBB2, #9A9618, #4B4802);
  background: url('/img/page_icons/ranking_top_bg.png') no-repeat center center;
  background-size: 100% 100%;
}


.top-player {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  .medal-wrapper {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    
    .medal {
      position: absolute;
      top: -50px;
      width: 32px;
      height: 44px;
      z-index: 2;
    }
    
    .avatar-frame {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      .avatar-frame-bg{
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
      }
      
    }
    
  }
  .player-name {
    font-size: 28px;
    font-weight: bold;
    color: #fff;
    // margin: 8px 0 4px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  &.first {
    order: 2;
    margin-bottom: 10px;
    z-index: 3;
    .avatar-frame {
      width: 200px;
      height: 160px;
      background: url('/img/page_icons/1st_user_bg.png') no-repeat center center;
      background-size: 100% 100%;
      .avatar-frame-bg{
        top: 28px;
        width: 72px;
        height: 72px;
        object-fit: cover;
        background: url('/img/page_icons/user_avatar_cicle.png') no-repeat center center;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        border-radius: 50%;
        .avatar{
          width: 60px;
          height: 60px;
          object-fit: cover;
        }
      }
    }
    .player-name{
      max-width: 200px;
    }
  }
  
  &.second {
    order: 1;
    z-index: 2;
    .avatar-frame {
      width: 140px;
      height: 111.7px;
      background: url('/img/page_icons/2st_user_bg.png') no-repeat center center;
      background-size: 100% 100%;
      
      .avatar-frame-bg{
        top: 18px;
        width: 52px;
        height: 52px;
        object-fit: cover;
        background: url('/img/page_icons/user_avatar_cicle.png') no-repeat center center;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        border-radius: 50%;
        .avatar{
          width: 40px;
          height: 40px;
          object-fit: cover;
        }
      }
    }
    .player-name{
      max-width: 140px;
    }
  }
  
  &.third {
    order: 3;
    z-index: 1;
    .avatar-frame {
      width: 150px;
      height: 93.4px;
      background: url('/img/page_icons/3st_user_bg.png') no-repeat center center;
      background-size: 100% 100%;
      .avatar-frame-bg{
        top: 10px;
        width: 48px;
        height: 48px;
        object-fit: cover;
        background: url('/img/page_icons/user_avatar_cicle.png') no-repeat center center;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        border-radius: 50%;
        .avatar{
          width: 36px;
          height: 36px;
          object-fit: cover;
        }
      }
    }
    .player-name{
      max-width: 150px;
    }
  }
  
  
  
  
  .player-score {
    display: flex;
    align-items: center;
    gap: 4px;
    
    .score-icon {
      width: 75px;
      height: 66px;
    }
    
    span {
      font-size: 48px;
      font-weight: bold;
      color: #fff;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    }
  }
}

.ranking-table {
  width: 100%;
  width: 654px;
  max-height: 560px;
  overflow-y: auto;
  padding: 10px 0;
  background: url('/img/page_icons/ranking_list_bg.png') no-repeat center center;
  background-size: 100% 100%;
  padding-bottom: 80px;
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.6);
    border-radius: 3px;
  }
}

.ranking-item {
  width: 638px;
  height: 96px;
  background: url('/img/page_icons/ranking_item_bg.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  padding: 0 36px 0 24px;
  margin: 0 auto;
  margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  
  &.current-player {
    background: url('/img/page_icons/rank_you_no_bg.png') no-repeat center center;
    background-size: 100% 100%;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
  }
  
  .rank-number {
    width: 61px;
    height: 62px;
    background: url('/img/page_icons/rank_no_bg.png') no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: #fff;
    margin-right: 20px;
    // box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);

    
    &.is-you {
      background: #3a6fa2;
      color: #fff;
    }
  }
  
  .player-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    border: 2px solid #F9D11F;
    border-radius: 20%;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 100%;
      height: 100%;
      // object-fit: cover;
    }
  }
  
  .player-name {
    flex: 1;
    font-size: 26px;
    font-weight: bold;
    color: #CFCFCF;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  }
  
  .player-score {
    display: flex;
    align-items: center;
    gap: 5px;
    
    .score-icon {
      width: 52px;
      height: 46px;
    }
    
    span {
      font-size: 36px;
      font-weight: bold;
      color: #fff;
      text-align: right;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    }
  }
}

.current-player-floating {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  
  .ranking-item {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    border: 2px solid #4682B4;
  }
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 30px;
  font-size: 14px;
  color: #FFD700;
}
</style> 