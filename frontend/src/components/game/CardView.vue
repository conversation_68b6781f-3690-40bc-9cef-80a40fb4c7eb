<template>
  <van-popup 
    v-model:show="localShow" 
    position="center" 
    class="card-view-popup"
    :style="{ width: '95%', maxWidth: '600px', height: '80%' }"
  >
    <div class="card-view-container">
      <div class="card-view-header">
        <h2>{{ t('cardView.title') }}</h2>
        <van-icon name="cross" @click="localShow = false" class="close-icon" />
      </div>

      <div class="card-view-content">
        <!-- 卡片导航 -->
        <div class="card-tabs">
          <div 
            v-for="tab in tabs" 
            :key="tab.id"
            class="card-tab"
            :class="{ active: activeTab === tab.id }"
            @click="activeTab = tab.id"
          >
            <div class="tab-icon">{{ tab.icon }}</div>
            <div class="tab-label">{{ tab.label }}</div>
          </div>
        </div>

        <!-- 卡片内容区域 -->
        <div class="card-content-area">
          <!-- 守护者卡片 -->
          <div v-if="activeTab === 'guardian'" class="card-section">
            <GuardianCard />
          </div>

          <!-- 经验卡片 -->
          <div v-if="activeTab === 'experience'" class="card-section">
            <ExperienceCard />
          </div>

          <!-- 收集统计卡片 -->
          <div v-if="activeTab === 'collection'" class="card-section">
            <CollectionStatsCard />
          </div>

          <!-- 成就卡片 -->
          <div v-if="activeTab === 'achievements'" class="card-section">
            <div class="achievements-grid">
              <div 
                v-for="achievement in achievements" 
                :key="achievement.id"
                class="achievement-card"
                :class="{ 
                  unlocked: achievement.unlocked, 
                  featured: achievement.featured,
                  flipped: flippedCards.includes(achievement.id)
                }"
                @click="toggleAchievementCard(achievement.id)"
              >
                <!-- 正面 -->
                <div class="achievement-front">
                  <div class="achievement-icon">{{ achievement.icon }}</div>
                  <div class="achievement-name">{{ achievement.name }}</div>
                  <div class="achievement-progress" v-if="!achievement.unlocked">
                    <div class="progress-bar">
                      <div 
                        class="progress-fill" 
                        :style="{ width: (achievement.current / achievement.target * 100) + '%' }"
                      ></div>
                    </div>
                    <div class="progress-text">{{ achievement.current }} / {{ achievement.target }}</div>
                  </div>
                  <div class="achievement-status" v-else>
                    <div class="status-icon">✅</div>
                    <div class="status-text">{{ t('achievements.completed') }}</div>
                  </div>
                </div>

                <!-- 背面 -->
                <div class="achievement-back">
                  <div class="achievement-description">{{ achievement.description }}</div>
                  <div class="achievement-reward" v-if="achievement.reward">
                    <div class="reward-label">{{ t('achievements.reward') }}:</div>
                    <div class="reward-items">
                      <div 
                        v-for="reward in achievement.reward" 
                        :key="reward.type"
                        class="reward-item"
                      >
                        <span class="reward-icon">{{ reward.icon }}</span>
                        <span class="reward-amount">{{ reward.amount }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="achievement-date" v-if="achievement.unlocked">
                    {{ t('achievements.unlockedOn') }}: {{ formatDate(achievement.unlockedDate) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 升级需求卡片 -->
          <div v-if="activeTab === 'upgrades'" class="card-section">
            <div class="upgrade-requirements-grid">
              <div 
                v-for="upgrade in upgradeRequirements" 
                :key="upgrade.id"
                class="upgrade-card"
                :class="{ 
                  available: upgrade.available, 
                  locked: !upgrade.available 
                }"
              >
                <div class="upgrade-header">
                  <div class="upgrade-icon">{{ upgrade.icon }}</div>
                  <div class="upgrade-title">{{ upgrade.title }}</div>
                </div>
                <div class="upgrade-body">
                  <div class="upgrade-description">{{ upgrade.description }}</div>
                  <div class="upgrade-requirements">
                    <div class="requirements-label">{{ t('upgrades.requirements') }}:</div>
                    <div class="requirements-list">
                      <div 
                        v-for="req in upgrade.requirements" 
                        :key="req.type"
                        class="requirement-item"
                        :class="{ met: req.met }"
                      >
                        <span class="req-icon">{{ req.icon }}</span>
                        <span class="req-text">{{ req.text }}</span>
                        <span class="req-status">{{ req.met ? '✅' : '❌' }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="upgrade-cost" v-if="upgrade.cost">
                    <div class="cost-label">{{ t('upgrades.cost') }}:</div>
                    <div class="cost-items">
                      <div 
                        v-for="cost in upgrade.cost" 
                        :key="cost.type"
                        class="cost-item"
                      >
                        <span class="cost-icon">{{ cost.icon }}</span>
                        <span class="cost-amount">{{ cost.amount }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="upgrade-footer">
                  <van-button 
                    v-if="upgrade.available"
                    type="primary" 
                    size="small"
                    @click="performUpgrade(upgrade.id)"
                    :loading="upgradingItems.includes(upgrade.id)"
                  >
                    {{ t('upgrades.upgrade') }}
                  </van-button>
                  <div v-else class="locked-message">
                    {{ t('upgrades.locked') }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useGameStore } from '@/stores/game'
import { showSuccessToast, showFailToast } from 'vant'
import GuardianCard from './GuardianCard.vue'
import ExperienceCard from './ExperienceCard.vue'
import CollectionStatsCard from './CollectionStatsCard.vue'

const { t } = useI18n()
const gameStore = useGameStore()

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:show'])

// Local state
const localShow = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const activeTab = ref('guardian')
const flippedCards = ref([])
const upgradingItems = ref([])

// 标签页配置
const tabs = [
  { id: 'guardian', icon: '👤', label: t('cardView.guardian') },
  { id: 'experience', icon: '⭐', label: t('cardView.experience') },
  { id: 'collection', icon: '📊', label: t('cardView.collection') },
  { id: 'achievements', icon: '🏆', label: t('cardView.achievements') },
  { id: 'upgrades', icon: '⬆️', label: t('cardView.upgrades') }
]

// 成就数据
const achievements = computed(() => [
  {
    id: 'first_thief',
    name: t('achievements.firstThief'),
    description: t('achievements.firstThiefDesc'),
    icon: '🎯',
    unlocked: gameStore.userInfo?.thieves_captured >= 1,
    current: gameStore.userInfo?.thieves_captured || 0,
    target: 1,
    featured: false,
    reward: [{ type: 'exp', icon: '⭐', amount: 50 }],
    unlockedDate: '2024-01-01'
  },
  {
    id: 'thief_hunter',
    name: t('achievements.thiefHunter'),
    description: t('achievements.thiefHunterDesc'),
    icon: '🏹',
    unlocked: gameStore.userInfo?.thieves_captured >= 100,
    current: gameStore.userInfo?.thieves_captured || 0,
    target: 100,
    featured: gameStore.userInfo?.thieves_captured >= 100,
    reward: [
      { type: 'gold', icon: '💰', amount: 1000 },
      { type: 'diamond', icon: '💎', amount: 10 }
    ]
  },
  {
    id: 'clean_city',
    name: t('achievements.cleanCity'),
    description: t('achievements.cleanCityDesc'),
    icon: '🧹',
    unlocked: gameStore.userInfo?.garbage_cleaned >= 50,
    current: gameStore.userInfo?.garbage_cleaned || 0,
    target: 50,
    featured: false,
    reward: [{ type: 'exp', icon: '⭐', amount: 200 }]
  }
])

// 升级需求数据
const upgradeRequirements = computed(() => [
  {
    id: 'stamina_boost',
    title: t('upgrades.staminaBoost'),
    description: t('upgrades.staminaBoostDesc'),
    icon: '⚡',
    available: gameStore.userInfo?.guardian_level >= 2,
    requirements: [
      {
        type: 'level',
        icon: '📊',
        text: t('upgrades.requireLevel', { level: 2 }),
        met: gameStore.userInfo?.guardian_level >= 2
      },
      {
        type: 'thieves',
        icon: '🕵️',
        text: t('upgrades.requireThieves', { count: 10 }),
        met: gameStore.userInfo?.thieves_captured >= 10
      }
    ],
    cost: [
      { type: 'gold', icon: '💰', amount: 500 },
      { type: 'diamond', icon: '💎', amount: 5 }
    ]
  },
  {
    id: 'treasure_finder',
    title: t('upgrades.treasureFinder'),
    description: t('upgrades.treasureFinderDesc'),
    icon: '🔍',
    available: gameStore.userInfo?.guardian_level >= 5,
    requirements: [
      {
        type: 'level',
        icon: '📊',
        text: t('upgrades.requireLevel', { level: 5 }),
        met: gameStore.userInfo?.guardian_level >= 5
      },
      {
        type: 'collection',
        icon: '📦',
        text: t('upgrades.requireCollections', { count: 100 }),
        met: (gameStore.userInfo?.thieves_captured + gameStore.userInfo?.garbage_cleaned) >= 100
      }
    ],
    cost: [
      { type: 'gold', icon: '💰', amount: 2000 },
      { type: 'diamond', icon: '💎', amount: 20 }
    ]
  }
])

// 方法
const toggleAchievementCard = (achievementId) => {
  const index = flippedCards.value.indexOf(achievementId)
  if (index > -1) {
    flippedCards.value.splice(index, 1)
  } else {
    flippedCards.value.push(achievementId)
  }
}

const performUpgrade = async (upgradeId) => {
  if (upgradingItems.value.includes(upgradeId)) return
  
  upgradingItems.value.push(upgradeId)
  try {
    // 模拟升级API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    showSuccessToast(t('upgrades.upgradeSuccess'))
  } catch (error) {
    showFailToast(t('upgrades.upgradeFailed'))
  } finally {
    const index = upgradingItems.value.indexOf(upgradeId)
    if (index > -1) {
      upgradingItems.value.splice(index, 1)
    }
  }
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString()
}
</script>

<style scoped>
.card-view-popup {
  border-radius: 20px;
  overflow: hidden;
}

.card-view-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.card-view-header h2 {
  margin: 0;
  font-size: 24px;
}

.close-icon {
  font-size: 24px;
  cursor: pointer;
  color: white;
}

.card-view-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.card-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  padding: 10px;
  gap: 5px;
}

.card-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 5px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.7);
}

.card-tab.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.tab-icon {
  font-size: 20px;
  margin-bottom: 5px;
}

.tab-label {
  font-size: 12px;
  text-align: center;
}

.card-content-area {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.card-section {
  height: 100%;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.achievement-card {
  position: relative;
  height: 200px;
  perspective: 1000px;
  cursor: pointer;
}

.achievement-card.flipped .achievement-front {
  transform: rotateY(-180deg);
}

.achievement-card.flipped .achievement-back {
  transform: rotateY(0deg);
}

.achievement-front,
.achievement-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 15px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  transition: transform 0.6s;
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.achievement-back {
  transform: rotateY(180deg);
  background: rgba(255, 255, 255, 0.15);
}

.achievement-card.unlocked .achievement-front {
  background: rgba(255, 215, 0, 0.2);
  border: 2px solid rgba(255, 215, 0, 0.5);
}

.achievement-card.featured {
  animation: glow 2s infinite;
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 10px rgba(255, 215, 0, 0.5); }
  50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
}

.achievement-icon {
  font-size: 40px;
  margin-bottom: 10px;
}

.achievement-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4fc08d, #43a047);
  transition: width 0.5s ease;
}

.progress-text {
  font-size: 12px;
}

.achievement-status {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-icon {
  font-size: 20px;
}

.upgrade-requirements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.upgrade-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  color: white;
}

.upgrade-card.locked {
  opacity: 0.6;
}

.upgrade-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.upgrade-icon {
  font-size: 24px;
  margin-right: 10px;
}

.upgrade-title {
  font-size: 18px;
  font-weight: bold;
}

.upgrade-description {
  margin-bottom: 15px;
  opacity: 0.9;
}

.requirements-label,
.cost-label {
  font-weight: bold;
  margin-bottom: 8px;
}

.requirement-item,
.cost-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
}

.requirement-item.met {
  color: #4fc08d;
}

.upgrade-footer {
  margin-top: 15px;
  text-align: center;
}

.locked-message {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
}
</style>
