<template>
  <div v-if="show" class="boss-dialogue-overlay">
    <div class="boss-dialogue-container">
      <!-- BOSS头像 -->
      <div class="boss-avatar">
        <img src="" alt="垃圾大王" class="boss-image" />
        <div class="boss-name">垃圾大王</div>
        <div class="boss-health-bar">
          <div class="health-bar-bg">
            <div 
              class="health-bar-fill" 
              :style="{ width: `${healthPercentage}%` }"
            ></div>
          </div>
          <div class="health-text">{{ healthPercentage }}%</div>
        </div>
      </div>

      <!-- 对话框 -->
      <div class="dialogue-box">
        <div class="dialogue-content">
          <p class="dialogue-text">{{ currentDialogue }}</p>
        </div>
        
        <!-- 操作按钮 -->
        <div class="dialogue-actions">
          <button 
            v-if="showRewardChoice" 
            class="reward-btn normal-reward"
            @click="claimReward(false)"
          >
            获得奖励
          </button>
          <button 
            v-if="showRewardChoice" 
            class="reward-btn double-reward"
            @click="claimReward(true)"
          >
            看广告获得双倍
          </button>
          <button 
            v-else
            class="continue-btn"
            @click="continueGame"
          >
            继续战斗
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useGameStore } from '@/stores/game'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  stage: {
    type: Number,
    default: 1
  },
  healthPercentage: {
    type: Number,
    default: 100
  }
})

const emit = defineEmits(['close', 'continue', 'rewardClaimed'])

const gameStore = useGameStore()

// 对话状态
const showRewardChoice = ref(false)
const rewardAmount = ref(0)

// 根据阶段获取对话内容
const currentDialogue = computed(() => {
  const dialogues = {
    1: { // 80%血量
      text: "哼！你这个小虫子，居然敢挑战我？我可是垃圾大王！不过看在你这么努力的份上，我给你点小奖励吧，哈哈哈！",
      reward: 20
    },
    2: { // 60%血量  
      text: "什么？！你竟然能伤到我这么多！不可能！我的垃圾军团呢？算了算了，你赢了这一回合，这些经验值拿去吧！",
      reward: 30
    },
    3: { // 40%血量
      text: "可恶！你真的很强！但是...但是我还有绝招！不过在此之前，让我'贿赂'你一下，这些丰厚的奖励你要不要考虑一下呢？",
      reward: 40
    },
    4: { // 20%血量
      text: "求求你了！手下留情吧！我错了，我不该污染地球！这是我珍藏的最后宝藏，都给你！只要你放过我！呜呜呜...",
      reward: 50
    }
  }
  
  const dialogue = dialogues[props.stage] || dialogues[1]
  rewardAmount.value = dialogue.reward
  return dialogue.text
})

// 监听对话显示状态
watch(() => props.show, (newShow) => {
  if (newShow) {
    // 显示对话时，自动显示奖励选择
    setTimeout(() => {
      showRewardChoice.value = true
    }, 2000) // 2秒后显示奖励选择
  } else {
    showRewardChoice.value = false
  }
})

// 继续游戏
const continueGame = () => {
  gameStore.resumeGame()
  emit('continue')
  emit('close')
}

// 领取奖励
const claimReward = (isDouble) => {
  const finalReward = isDouble ? rewardAmount.value * 2 : rewardAmount.value
  
  if (isDouble) {
    // 播放广告逻辑
    playRewardAd(() => {
      gameStore.addCivilizationExp(finalReward)
      emit('rewardClaimed', { amount: finalReward, isDouble: true })
      continueGame()
    })
  } else {
    gameStore.addCivilizationExp(finalReward)
    emit('rewardClaimed', { amount: finalReward, isDouble: false })
    continueGame()
  }
}

// 播放奖励广告
const playRewardAd = (callback) => {
  if (window.CrazyGames && window.CrazyGames.SDK) {
    const adCallbacks = {
      adFinished: () => {
        console.log("广告播放完成")
        callback()
      },
      adError: (error) => {
        console.log("广告播放失败", error)
        // 即使广告失败也给予奖励
        callback()
      },
      adStarted: () => console.log("开始播放广告")
    }
    window.CrazyGames.SDK.ad.requestAd("rewarded", adCallbacks)
  } else {
    // 非CrazyGames环境，直接给予奖励
    setTimeout(callback, 1000)
  }
}
</script>

<style lang="scss" scoped>
.boss-dialogue-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-in-out;
}

.boss-dialogue-container {
  display: flex;
  align-items: center;
  gap: 40px;
  max-width: 90%;
  max-height: 90%;
}

.boss-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  
  .boss-image {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    border: 4px solid #ff4444;
    animation: pulse 2s infinite;
  }
  
  .boss-name {
    font-size: 24px;
    font-weight: bold;
    color: #ff4444;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  }
  
  .boss-health-bar {
    width: 200px;
    
    .health-bar-bg {
      width: 100%;
      height: 20px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      overflow: hidden;
      border: 2px solid #fff;
      
      .health-bar-fill {
        height: 100%;
        background: linear-gradient(90deg, #ff4444, #ff6666);
        transition: width 0.5s ease;
      }
    }
    
    .health-text {
      text-align: center;
      color: #fff;
      font-weight: bold;
      margin-top: 5px;
    }
  }
}

.dialogue-box {
  background: linear-gradient(135deg, #2a1810, #4a2820);
  border: 3px solid #8b4513;
  border-radius: 20px;
  padding: 30px;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  
  .dialogue-content {
    margin-bottom: 25px;
    
    .dialogue-text {
      font-size: 18px;
      line-height: 1.6;
      color: #fff;
      margin: 0;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    }
  }
  
  .dialogue-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    
    .reward-btn, .continue-btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }
    }
    
    .normal-reward {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
      
      &:hover {
        background: linear-gradient(135deg, #45a049, #3d8b40);
      }
    }
    
    .double-reward {
      background: linear-gradient(135deg, #ff9800, #f57c00);
      color: white;
      position: relative;
      
      &:hover {
        background: linear-gradient(135deg, #f57c00, #ef6c00);
      }
      
      &:after {
        content: "🎬";
        margin-left: 8px;
      }
    }
    
    .continue-btn {
      background: linear-gradient(135deg, #2196F3, #1976D2);
      color: white;
      
      &:hover {
        background: linear-gradient(135deg, #1976D2, #1565C0);
      }
    }
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@media (max-width: 768px) {
  .boss-dialogue-container {
    flex-direction: column;
    gap: 20px;
  }
  
  .boss-avatar {
    .boss-image {
      width: 150px;
      height: 150px;
    }
    
    .boss-health-bar {
      width: 150px;
    }
  }
  
  .dialogue-box {
    padding: 20px;
    max-width: 90%;
    
    .dialogue-text {
      font-size: 16px;
    }
    
    .dialogue-actions {
      flex-direction: column;
      
      .reward-btn, .continue-btn {
        width: 100%;
      }
    }
  }
}
</style>