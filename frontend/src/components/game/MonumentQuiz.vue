<template>
  <div v-if="show" class="monument-quiz-overlay">
    <div class="monument-quiz-container">
      <!-- 古迹图片 -->
      <div class="monument-header">
        <div class="monument-image-container">
          <img 
            :src="currentMonument.pollutedImage" 
            v-if="!isRestored"
            alt="被污染的古迹" 
            class="monument-image polluted"
          />
          <img 
            :src="currentMonument.cleanImage" 
            v-else
            alt="恢复的古迹" 
            class="monument-image clean"
          />
          <div v-if="!isRestored" class="pollution-overlay">
            <div class="pollution-effect"></div>
          </div>
        </div>
        <h2 class="monument-title">{{ currentMonument.name }}</h2>
      </div>

      <!-- 垃圾头目对话 -->
      <div class="boss-dialogue" v-if="!isRestored">
        <div class="boss-avatar">
          <img src="" alt="垃圾头目" class="boss-image" />
        </div>
        <div class="dialogue-bubble">
          <p class="boss-text">
            "失败者，这里已经被我们占领了！如果你想夺回，回答几个题目，答对了我们就撤退！哼哼！"
          </p>
        </div>
      </div>

      <!-- 成功恢复对话 -->
      <div class="boss-dialogue success" v-else>
        <div class="boss-avatar">
          <img src="" alt="沮丧的垃圾头目" class="boss-image" />
        </div>
        <div class="dialogue-bubble success">
          <p class="boss-text">
            "算你厉害，我们先撤，但我还会回来的！！！"
          </p>
        </div>
      </div>

      <!-- 问答区域 -->
      <div class="quiz-section" v-if="!isRestored && !showResult">
        <div class="question-container">
          <div class="question-header">
            <span class="question-number">问题 {{ currentQuestionIndex + 1 }} / {{ currentMonument.questions.length }}</span>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: `${(currentQuestionIndex / currentMonument.questions.length) * 100}%` }"
              ></div>
            </div>
          </div>
          
          <h3 class="question-text">{{ currentQuestion.question }}</h3>
          
          <div class="options-container">
            <button
              v-for="(option, index) in currentQuestion.options"
              :key="index"
              class="option-btn"
              :class="{ 
                'selected': selectedAnswer === index,
                'correct': showAnswerResult && index === currentQuestion.correctAnswer,
                'wrong': showAnswerResult && selectedAnswer === index && index !== currentQuestion.correctAnswer
              }"
              @click="selectAnswer(index)"
              :disabled="showAnswerResult"
            >
              {{ option }}
            </button>
          </div>
          
          <div class="quiz-actions" v-if="selectedAnswer !== null">
            <button 
              class="submit-btn"
              @click="submitAnswer"
              :disabled="showAnswerResult"
            >
              {{ showAnswerResult ? '已提交' : '提交答案' }}
            </button>
            <button 
              v-if="showAnswerResult"
              class="next-btn"
              @click="nextQuestion"
            >
              {{ currentQuestionIndex < currentMonument.questions.length - 1 ? '下一题' : '完成' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 结果展示 -->
      <div class="result-section" v-if="showResult">
        <div class="result-container" :class="{ 'success': isSuccess, 'failure': !isSuccess }">
          <div class="result-icon">
            <span v-if="isSuccess">🎉</span>
            <span v-else>😞</span>
          </div>
          
          <h3 class="result-title">
            {{ isSuccess ? '恭喜！古迹已恢复！' : '很遗憾，保护失败...' }}
          </h3>
          
          <div class="result-stats">
            <p>正确答案: {{ correctAnswers }} / {{ currentMonument.questions.length }}</p>
            <p class="reward-text" v-if="isSuccess">
              获得 {{ successReward }} 文明经验值！
            </p>
            <p class="penalty-text" v-else>
              失去 {{ failurePenalty }} 文明经验值...
            </p>
          </div>
          
          <div class="result-actions">
            <button 
              v-if="isSuccess" 
              class="reward-btn normal-reward"
              @click="claimReward(false)"
            >
              获得奖励
            </button>
            <button 
              v-if="isSuccess" 
              class="reward-btn double-reward"
              @click="claimReward(true)"
            >
              看广告获得双倍
            </button>
            <button 
              v-else
              class="retry-btn"
              @click="retryQuiz"
            >
              重新挑战
            </button>
            <button 
              class="close-btn"
              @click="closeQuiz"
            >
              {{ isSuccess ? '继续游戏' : '离开' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useGameStore } from '@/stores/game'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  monumentId: {
    type: String,
    default: 'monument_1'
  }
})

const emit = defineEmits(['close', 'success', 'failure'])

const gameStore = useGameStore()

// 古迹数据
const monuments = ref({
  monument_1: {
    name: "卢浮宫",
    pollutedImage: "",
    cleanImage: "",
    questions: [
      {
        question: "卢浮宫最著名的藏品是什么？",
        options: ["蒙娜丽莎", "维纳斯雕像", "自由女神像", "大卫雕像"],
        correctAnswer: 0
      },
      {
        question: "卢浮宫位于哪个城市？",
        options: ["伦敦", "巴黎", "罗马", "柏林"],
        correctAnswer: 1
      },
      {
        question: "卢浮宫的玻璃金字塔是哪位建筑师设计的？",
        options: ["贝聿铭", "高迪", "柯布西耶", "赖特"],
        correctAnswer: 0
      }
    ]
  },
  monument_2: {
    name: "紫禁城",
    pollutedImage: "",
    cleanImage: "",
    questions: [
      {
        question: "紫禁城建于哪个朝代？",
        options: ["唐朝", "宋朝", "明朝", "清朝"],
        correctAnswer: 2
      },
      {
        question: "紫禁城有多少间房屋？",
        options: ["6666间", "7777间", "8888间", "9999间"],
        correctAnswer: 3
      },
      {
        question: "紫禁城的正门叫什么？",
        options: ["天安门", "午门", "神武门", "太和门"],
        correctAnswer: 1
      }
    ]
  }
})

// 游戏状态
const currentQuestionIndex = ref(0)
const selectedAnswer = ref(null)
const showAnswerResult = ref(false)
const showResult = ref(false)
const isRestored = ref(false)
const correctAnswers = ref(0)
const successReward = ref(15)
const failurePenalty = ref(10)

// 计算属性
const currentMonument = computed(() => {
  return monuments.value[props.monumentId] || monuments.value.monument_1
})

const currentQuestion = computed(() => {
  return currentMonument.value.questions[currentQuestionIndex.value]
})

const isSuccess = computed(() => {
  return correctAnswers.value >= Math.ceil(currentMonument.value.questions.length * 0.6) // 60%正确率算成功
})

// 监听显示状态
watch(() => props.show, (newShow) => {
  if (newShow) {
    resetQuiz()
  }
})

// 重置问答
const resetQuiz = () => {
  currentQuestionIndex.value = 0
  selectedAnswer.value = null
  showAnswerResult.value = false
  showResult.value = false
  isRestored.value = false
  correctAnswers.value = 0
}

// 选择答案
const selectAnswer = (index) => {
  if (showAnswerResult.value) return
  selectedAnswer.value = index
}

// 提交答案
const submitAnswer = () => {
  showAnswerResult.value = true
  
  if (selectedAnswer.value === currentQuestion.value.correctAnswer) {
    correctAnswers.value++
  }
  
  // 2秒后允许进入下一题
  setTimeout(() => {
    if (currentQuestionIndex.value < currentMonument.value.questions.length - 1) {
      // 还有题目，等待用户点击下一题
    } else {
      // 所有题目完成，显示结果
      finishQuiz()
    }
  }, 1500)
}

// 下一题
const nextQuestion = () => {
  if (currentQuestionIndex.value < currentMonument.value.questions.length - 1) {
    currentQuestionIndex.value++
    selectedAnswer.value = null
    showAnswerResult.value = false
  } else {
    finishQuiz()
  }
}

// 完成问答
const finishQuiz = () => {
  showResult.value = true
  
  if (isSuccess.value) {
    isRestored.value = true
    gameStore.protectMonument(true)
    emit('success', {
      monumentId: props.monumentId,
      score: correctAnswers.value,
      total: currentMonument.value.questions.length
    })
  } else {
    gameStore.protectMonument(false)
    emit('failure', {
      monumentId: props.monumentId,
      score: correctAnswers.value,
      total: currentMonument.value.questions.length
    })
  }
}

// 领取奖励
const claimReward = (isDouble) => {
  const finalReward = isDouble ? successReward.value * 2 : successReward.value
  
  if (isDouble) {
    playRewardAd(() => {
      gameStore.addCivilizationExp(finalReward)
      closeQuiz()
    })
  } else {
    // 已经在protectMonument中给过奖励了，这里不重复给
    closeQuiz()
  }
}

// 重新挑战
const retryQuiz = () => {
  resetQuiz()
}

// 关闭问答
const closeQuiz = () => {
  emit('close')
}

// 播放奖励广告
const playRewardAd = (callback) => {
  if (window.CrazyGames && window.CrazyGames.SDK) {
    const adCallbacks = {
      adFinished: () => {
        console.log("广告播放完成")
        callback()
      },
      adError: (error) => {
        console.log("广告播放失败", error)
        callback()
      },
      adStarted: () => console.log("开始播放广告")
    }
    window.CrazyGames.SDK.ad.requestAd("rewarded", adCallbacks)
  } else {
    setTimeout(callback, 1000)
  }
}
</script>

<style lang="scss" scoped>
.monument-quiz-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.5s ease-in-out;
}

.monument-quiz-container {
  background: linear-gradient(135deg, #2c1810, #4a2820);
  border-radius: 20px;
  padding: 30px;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.monument-header {
  text-align: center;
  margin-bottom: 30px;
  
  .monument-image-container {
    position: relative;
    display: inline-block;
    margin-bottom: 15px;
    
    .monument-image {
      width: 300px;
      height: 200px;
      object-fit: cover;
      border-radius: 15px;
      border: 3px solid #8b4513;
      
      &.polluted {
        filter: hue-rotate(120deg) saturate(0.5) brightness(0.7);
      }
      
      &.clean {
        animation: restore 1s ease-in-out;
      }
    }
    
    .pollution-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 15px;
      pointer-events: none;
      
      .pollution-effect {
        width: 100%;
        height: 100%;
        background: rgba(139, 69, 19, 0.3);
        animation: pollutionPulse 2s infinite;
      }
    }
  }
  
  .monument-title {
    font-size: 28px;
    color: #fff;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  }
}

.boss-dialogue {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  border: 2px solid #8b4513;
  
  &.success {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
  }
  
  .boss-avatar {
    .boss-image {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      border: 3px solid #ff4444;
    }
  }
  
  .dialogue-bubble {
    flex: 1;
    background: linear-gradient(135deg, #3a1810, #5a2820);
    border-radius: 15px;
    padding: 15px;
    position: relative;
    
    &:before {
      content: '';
      position: absolute;
      left: -10px;
      top: 50%;
      transform: translateY(-50%);
      border: 10px solid transparent;
      border-right-color: #3a1810;
    }
    
    &.success {
      background: linear-gradient(135deg, #1a3a18, #2a5a28);
      
      &:before {
        border-right-color: #1a3a18;
      }
    }
    
    .boss-text {
      color: #fff;
      font-size: 16px;
      margin: 0;
      line-height: 1.4;
    }
  }
}

.quiz-section {
  margin-bottom: 30px;
  
  .question-container {
    .question-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      
      .question-number {
        font-size: 14px;
        color: #ccc;
      }
      
      .progress-bar {
        flex: 1;
        height: 8px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        margin-left: 20px;
        overflow: hidden;
        
        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #4CAF50, #45a049);
          transition: width 0.3s ease;
        }
      }
    }
    
    .question-text {
      font-size: 20px;
      color: #fff;
      margin-bottom: 25px;
      text-align: center;
      line-height: 1.4;
    }
    
    .options-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin-bottom: 30px;
      
      .option-btn {
        padding: 15px 20px;
        background: linear-gradient(135deg, #3a3a3a, #2a2a2a);
        border: 2px solid #555;
        border-radius: 10px;
        color: #fff;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: left;
        
        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #4a4a4a, #3a3a3a);
          border-color: #777;
          transform: translateY(-2px);
        }
        
        &.selected {
          border-color: #2196F3;
          background: linear-gradient(135deg, #1976D2, #1565C0);
        }
        
        &.correct {
          border-color: #4CAF50;
          background: linear-gradient(135deg, #4CAF50, #45a049);
        }
        
        &.wrong {
          border-color: #f44336;
          background: linear-gradient(135deg, #f44336, #d32f2f);
        }
        
        &:disabled {
          cursor: not-allowed;
        }
      }
    }
    
    .quiz-actions {
      display: flex;
      gap: 15px;
      justify-content: center;
      
      .submit-btn, .next-btn {
        padding: 12px 30px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
      
      .submit-btn {
        background: linear-gradient(135deg, #ff9800, #f57c00);
        color: white;
        
        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #f57c00, #ef6c00);
          transform: translateY(-2px);
        }
      }
      
      .next-btn {
        background: linear-gradient(135deg, #4CAF50, #45a049);
        color: white;
        
        &:hover {
          background: linear-gradient(135deg, #45a049, #3d8b40);
          transform: translateY(-2px);
        }
      }
    }
  }
}

.result-section {
  text-align: center;
  
  .result-container {
    padding: 30px;
    border-radius: 20px;
    
    &.success {
      background: linear-gradient(135deg, #1a4a1a, #2a6a2a);
      border: 3px solid #4CAF50;
    }
    
    &.failure {
      background: linear-gradient(135deg, #4a1a1a, #6a2a2a);
      border: 3px solid #f44336;
    }
    
    .result-icon {
      font-size: 60px;
      margin-bottom: 20px;
    }
    
    .result-title {
      font-size: 24px;
      color: #fff;
      margin-bottom: 20px;
    }
    
    .result-stats {
      margin-bottom: 30px;
      
      p {
        color: #ccc;
        font-size: 16px;
        margin: 10px 0;
      }
      
      .reward-text {
        color: #4CAF50;
        font-weight: bold;
      }
      
      .penalty-text {
        color: #f44336;
        font-weight: bold;
      }
    }
    
    .result-actions {
      display: flex;
      gap: 15px;
      justify-content: center;
      flex-wrap: wrap;
      
      .reward-btn, .retry-btn, .close-btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
      }
      
      .normal-reward {
        background: linear-gradient(135deg, #4CAF50, #45a049);
        color: white;
      }
      
      .double-reward {
        background: linear-gradient(135deg, #ff9800, #f57c00);
        color: white;
        
        &:after {
          content: "🎬";
          margin-left: 8px;
        }
      }
      
      .retry-btn {
        background: linear-gradient(135deg, #ff9800, #f57c00);
        color: white;
      }
      
      .close-btn {
        background: linear-gradient(135deg, #607D8B, #455A64);
        color: white;
      }
    }
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes restore {
  from { 
    filter: hue-rotate(120deg) saturate(0.5) brightness(0.7);
    transform: scale(0.9);
  }
  to { 
    filter: none;
    transform: scale(1);
  }
}

@keyframes pollutionPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

@media (max-width: 768px) {
  .monument-quiz-container {
    padding: 20px;
    margin: 10px;
  }
  
  .monument-header {
    .monument-image {
      width: 250px !important;
      height: 160px !important;
    }
    
    .monument-title {
      font-size: 24px;
    }
  }
  
  .boss-dialogue {
    flex-direction: column;
    text-align: center;
    
    .dialogue-bubble:before {
      display: none;
    }
  }
  
  .options-container {
    grid-template-columns: 1fr !important;
  }
  
  .result-actions {
    flex-direction: column;
    
    .reward-btn, .retry-btn, .close-btn {
      width: 100%;
    }
  }
}
</style>