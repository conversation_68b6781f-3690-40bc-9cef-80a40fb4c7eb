<template>
  <div class="experience-card">
    <div class="card-header">
      <div class="header-icon">⭐</div>
      <div class="header-text">
        <h3>{{ t('experience.title') }}</h3>
        <p>{{ t('experience.subtitle') }}</p>
      </div>
    </div>

    <div class="card-body">
      <!-- 当前等级显示 -->
      <div class="level-display">
        <div class="level-circle">
          <div class="level-number">{{ currentLevel }}</div>
          <div class="level-label">{{ t('experience.level') }}</div>
        </div>
        <div class="level-info">
          <div class="level-name">{{ levelName }}</div>
          <div class="level-description">{{ levelDescription }}</div>
        </div>
      </div>

      <!-- 经验进度条 -->
      <div class="progress-section">
        <div class="progress-header">
          <span class="progress-label">{{ t('experience.progress') }}</span>
          <span class="progress-numbers">{{ currentExp }} / {{ nextLevelExp }}</span>
        </div>
        <div class="progress-container">
          <div class="progress-track">
            <div 
              class="progress-fill" 
              :style="{ width: progressPercentage + '%' }"
            >
              <div class="progress-glow"></div>
            </div>
          </div>
          <div class="progress-percentage">{{ Math.round(progressPercentage) }}%</div>
        </div>
        <div class="progress-footer">
          <span class="exp-needed">{{ t('experience.expNeeded', { exp: expNeeded }) }}</span>
        </div>
      </div>

      <!-- 经验来源统计 -->
      <div class="exp-sources">
        <h4>{{ t('experience.sources') }}</h4>
        <div class="sources-grid">
          <div class="source-item thief">
            <div class="source-icon">🕵️</div>
            <div class="source-data">
              <div class="source-count">{{ thievesExp }}</div>
              <div class="source-label">{{ t('experience.fromThieves') }}</div>
            </div>
          </div>
          <div class="source-item garbage">
            <div class="source-icon">🗑️</div>
            <div class="source-data">
              <div class="source-count">{{ garbageExp }}</div>
              <div class="source-label">{{ t('experience.fromGarbage') }}</div>
            </div>
          </div>
          <div class="source-item monument">
            <div class="source-icon">🏛️</div>
            <div class="source-data">
              <div class="source-count">{{ monumentExp }}</div>
              <div class="source-label">{{ t('experience.fromMonuments') }}</div>
            </div>
          </div>
          <div class="source-item bonus">
            <div class="source-icon">🎁</div>
            <div class="source-data">
              <div class="source-count">{{ bonusExp }}</div>
              <div class="source-label">{{ t('experience.fromBonus') }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 下一等级预览 -->
      <div class="next-level-preview" v-if="!isMaxLevel">
        <h4>{{ t('experience.nextLevel') }}</h4>
        <div class="next-level-card">
          <div class="next-level-number">{{ nextLevel }}</div>
          <div class="next-level-info">
            <div class="next-level-name">{{ nextLevelName }}</div>
            <div class="next-level-rewards">
              <div class="reward-item" v-for="reward in nextLevelRewards" :key="reward.type">
                <span class="reward-icon">{{ reward.icon }}</span>
                <span class="reward-text">{{ reward.text }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最大等级提示 -->
      <div class="max-level-notice" v-else>
        <div class="max-level-icon">👑</div>
        <div class="max-level-text">
          <h4>{{ t('experience.maxLevelReached') }}</h4>
          <p>{{ t('experience.maxLevelDescription') }}</p>
        </div>
      </div>
    </div>

    <!-- 卡片底部操作 -->
    <div class="card-footer">
      <van-button 
        type="primary" 
        size="small" 
        @click="showExpHistory"
        icon="clock-o"
      >
        {{ t('experience.viewHistory') }}
      </van-button>
      <van-button 
        type="success" 
        size="small" 
        @click="showExpTips"
        icon="question-o"
      >
        {{ t('experience.tips') }}
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useGameStore } from '@/stores/game'

const { t } = useI18n()
const gameStore = useGameStore()

// 计算属性
const currentLevel = computed(() => gameStore.userInfo?.guardian_level || 1)
const currentExp = computed(() => gameStore.userInfo?.guardian_experience || 0)
const nextLevelExp = computed(() => gameStore.userInfo?.guardian_next_level_exp || 100)
const nextLevel = computed(() => currentLevel.value + 1)
const isMaxLevel = computed(() => currentLevel.value >= 14) // PRD中最高14级

const progressPercentage = computed(() => {
  if (isMaxLevel.value) return 100
  return Math.min((currentExp.value / nextLevelExp.value) * 100, 100)
})

const expNeeded = computed(() => {
  if (isMaxLevel.value) return 0
  return Math.max(nextLevelExp.value - currentExp.value, 0)
})

const levelName = computed(() => {
  const level = currentLevel.value
  if (level >= 14) return t('guardian.levels.grandmaster')
  if (level >= 12) return t('guardian.levels.master')
  if (level >= 10) return t('guardian.levels.expert')
  if (level >= 8) return t('guardian.levels.veteran')
  if (level >= 6) return t('guardian.levels.skilled')
  if (level >= 4) return t('guardian.levels.experienced')
  if (level >= 2) return t('guardian.levels.apprentice')
  return t('guardian.levels.novice')
})

const levelDescription = computed(() => {
  return t(`guardian.descriptions.level${currentLevel.value}`)
})

const nextLevelName = computed(() => {
  const level = nextLevel.value
  if (level >= 14) return t('guardian.levels.grandmaster')
  if (level >= 12) return t('guardian.levels.master')
  if (level >= 10) return t('guardian.levels.expert')
  if (level >= 8) return t('guardian.levels.veteran')
  if (level >= 6) return t('guardian.levels.skilled')
  if (level >= 4) return t('guardian.levels.experienced')
  if (level >= 2) return t('guardian.levels.apprentice')
  return t('guardian.levels.novice')
})

const nextLevelRewards = computed(() => {
  const level = nextLevel.value
  const rewards = []
  
  if (level === 2) {
    rewards.push({ icon: '⚡', text: t('rewards.staminaBoost') })
  } else if (level === 5) {
    rewards.push({ icon: '🔍', text: t('rewards.treasureFinder') })
  } else if (level === 10) {
    rewards.push({ icon: '👑', text: t('rewards.masterCollector') })
  } else if (level === 14) {
    rewards.push({ icon: '🌟', text: t('rewards.grandmasterTitle') })
  }
  
  // 通用奖励
  rewards.push({ icon: '💰', text: t('rewards.goldBonus') })
  rewards.push({ icon: '💎', text: t('rewards.diamondBonus') })
  
  return rewards
})

// 经验来源统计（基于收集行为计算）
const thievesExp = computed(() => (gameStore.userInfo?.thieves_captured || 0) * 5)
const garbageExp = computed(() => (gameStore.userInfo?.garbage_cleaned || 0) * 3)
const monumentExp = computed(() => (gameStore.userInfo?.monuments_protected || 0) * 20)
const bonusExp = computed(() => gameStore.userInfo?.bonus_exp || 0)

// 方法
const showExpHistory = () => {
  // 显示经验历史记录
  console.log('Show experience history')
}

const showExpTips = () => {
  // 显示经验获取技巧
  console.log('Show experience tips')
}
</script>

<style scoped>
.experience-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 20px;
  color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.header-icon {
  font-size: 32px;
  margin-right: 15px;
}

.header-text h3 {
  margin: 0;
  font-size: 20px;
}

.header-text p {
  margin: 5px 0 0 0;
  opacity: 0.8;
  font-size: 14px;
}

.level-display {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
}

.level-circle {
  width: 80px;
  height: 80px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  background: rgba(255, 255, 255, 0.1);
}

.level-number {
  font-size: 24px;
  font-weight: bold;
}

.level-label {
  font-size: 12px;
  opacity: 0.8;
}

.level-info {
  flex: 1;
}

.level-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.level-description {
  font-size: 14px;
  opacity: 0.8;
  line-height: 1.4;
}

.progress-section {
  margin-bottom: 25px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
}

.progress-track {
  flex: 1;
  height: 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4fc08d, #43a047);
  border-radius: 6px;
  position: relative;
  transition: width 0.5s ease;
}

.progress-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4));
  animation: glow 2s infinite;
}

@keyframes glow {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

.progress-percentage {
  font-size: 12px;
  font-weight: bold;
  min-width: 35px;
  text-align: right;
}

.progress-footer {
  text-align: center;
  font-size: 12px;
  opacity: 0.8;
}

.exp-sources {
  margin-bottom: 25px;
}

.exp-sources h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
}

.sources-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.source-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 12px;
  display: flex;
  align-items: center;
}

.source-icon {
  font-size: 20px;
  margin-right: 10px;
}

.source-count {
  font-size: 16px;
  font-weight: bold;
}

.source-label {
  font-size: 12px;
  opacity: 0.8;
}

.next-level-preview {
  margin-bottom: 20px;
}

.next-level-preview h4 {
  margin: 0 0 10px 0;
  font-size: 16px;
}

.next-level-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: center;
}

.next-level-number {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  margin-right: 15px;
}

.next-level-info {
  flex: 1;
}

.next-level-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.next-level-rewards {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.reward-item {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.reward-icon {
  margin-right: 6px;
}

.max-level-notice {
  background: rgba(255, 215, 0, 0.2);
  border: 2px solid rgba(255, 215, 0, 0.5);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  margin-bottom: 20px;
}

.max-level-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.max-level-text h4 {
  margin: 0 0 10px 0;
  color: #ffd700;
}

.max-level-text p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.card-footer {
  display: flex;
  gap: 10px;
  justify-content: center;
}
</style>
