<template>
  <div class="guardian-card-container">
    <!-- 守护者等级卡片 -->
    <div 
      class="guardian-card" 
      :class="{ 'flipped': isFlipped }"
      @click="toggleCard"
    >
      <!-- 正面：基础信息 -->
      <div class="card-front">
        <div class="card-header">
          <div class="guardian-avatar">
            <img :src="guardianAvatar" alt="Guardian" class="avatar-img" />
            <div class="level-badge">Lv.{{ guardianLevel }}</div>
          </div>
          <div class="guardian-title">
            <h3>{{ t('guardian.title') }}</h3>
            <p class="guardian-name">{{ guardianName }}</p>
          </div>
        </div>

        <div class="card-body">
          <!-- 经验进度条 -->
          <div class="exp-section">
            <div class="exp-label">
              <span>{{ t('guardian.experience') }}</span>
              <span class="exp-numbers">{{ currentExp }} / {{ nextLevelExp }}</span>
            </div>
            <div class="exp-bar-container">
              <div class="exp-bar">
                <div 
                  class="exp-fill" 
                  :style="{ width: expPercentage + '%' }"
                ></div>
              </div>
              <div class="exp-percentage">{{ Math.round(expPercentage) }}%</div>
            </div>
          </div>

          <!-- 快速统计 -->
          <div class="quick-stats">
            <div class="stat-item">
              <div class="stat-icon">🏆</div>
              <div class="stat-value">{{ totalArtifacts }}</div>
              <div class="stat-label">{{ t('guardian.artifacts') }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">⚡</div>
              <div class="stat-value">{{ unlockedAbilities }}</div>
              <div class="stat-label">{{ t('guardian.abilities') }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">🎯</div>
              <div class="stat-value">{{ totalActions }}</div>
              <div class="stat-label">{{ t('guardian.actions') }}</div>
            </div>
          </div>
        </div>

        <div class="card-footer">
          <div class="flip-hint">
            <van-icon name="info-o" />
            <span>{{ t('guardian.clickForDetails') }}</span>
          </div>
        </div>
      </div>

      <!-- 背面：详细信息 -->
      <div class="card-back">
        <div class="card-header">
          <h3>{{ t('guardian.detailedInfo') }}</h3>
          <van-icon name="cross" @click.stop="toggleCard" class="close-icon" />
        </div>

        <div class="card-body">
          <!-- 图鉴收集详情 -->
          <div class="artifacts-detail">
            <h4>{{ t('guardian.artifactCollection') }}</h4>
            <div class="artifact-grid">
              <div class="artifact-card bronze">
                <div class="artifact-icon">🥉</div>
                <div class="artifact-count">{{ artifacts.bronze }}</div>
                <div class="artifact-label">{{ t('artifacts.bronze') }}</div>
              </div>
              <div class="artifact-card silver">
                <div class="artifact-icon">🥈</div>
                <div class="artifact-count">{{ artifacts.silver }}</div>
                <div class="artifact-label">{{ t('artifacts.silver') }}</div>
              </div>
              <div class="artifact-card gold">
                <div class="artifact-icon">🥇</div>
                <div class="artifact-count">{{ artifacts.gold }}</div>
                <div class="artifact-label">{{ t('artifacts.gold') }}</div>
              </div>
            </div>
          </div>

          <!-- 已解锁能力 -->
          <div class="abilities-detail" v-if="guardianAbilities.length > 0">
            <h4>{{ t('guardian.unlockedAbilities') }}</h4>
            <div class="abilities-grid">
              <div 
                v-for="ability in guardianAbilities" 
                :key="ability.id"
                class="ability-card"
                :class="ability.rarity"
              >
                <div class="ability-icon">{{ ability.icon }}</div>
                <div class="ability-name">{{ ability.name }}</div>
                <div class="ability-desc">{{ ability.description }}</div>
              </div>
            </div>
          </div>

          <!-- 升级选项 -->
          <div class="upgrade-section">
            <h4>{{ t('guardian.upgradeOptions') }}</h4>
            <div class="upgrade-cards">
              <div class="upgrade-card gold" :class="{ disabled: !canUpgradeWithGold }">
                <div class="upgrade-icon">💰</div>
                <div class="upgrade-cost">{{ nextLevelCost.gold }}</div>
                <van-button 
                  type="primary" 
                  size="small"
                  :disabled="!canUpgradeWithGold"
                  @click.stop="upgradeGuardian('gold')"
                  :loading="isUpgrading"
                >
                  {{ t('guardian.upgradeWithGold') }}
                </van-button>
              </div>
              <div class="upgrade-card diamond" :class="{ disabled: !canUpgradeWithDiamond }">
                <div class="upgrade-icon">💎</div>
                <div class="upgrade-cost">{{ nextLevelCost.diamond }}</div>
                <van-button 
                  type="warning" 
                  size="small"
                  :disabled="!canUpgradeWithDiamond"
                  @click.stop="upgradeGuardian('diamond')"
                  :loading="isUpgrading"
                >
                  {{ t('guardian.upgradeWithDiamond') }}
                </van-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useGameStore } from '@/stores/game'
import { showSuccessToast, showFailToast } from 'vant'
import api from '@/utils/api'

const { t } = useI18n()
const gameStore = useGameStore()

// 响应式数据
const isFlipped = ref(false)
const isUpgrading = ref(false)
const guardianData = ref(null)

// 计算属性
const guardianLevel = computed(() => gameStore.userInfo?.guardian_level || 1)
const currentExp = computed(() => gameStore.userInfo?.guardian_experience || 0)
const nextLevelExp = computed(() => gameStore.userInfo?.guardian_next_level_exp || 100)
const expPercentage = computed(() => (currentExp.value / nextLevelExp.value) * 100)

const guardianAvatar = computed(() => {
  const level = guardianLevel.value
  if (level >= 10) return '/img/guardian/guardian_master.png'
  if (level >= 5) return '/img/guardian/guardian_advanced.png'
  return '/img/guardian/guardian_basic.png'
})

const guardianName = computed(() => {
  const level = guardianLevel.value
  if (level >= 10) return t('guardian.names.master')
  if (level >= 5) return t('guardian.names.advanced')
  return t('guardian.names.novice')
})

const artifacts = computed(() => ({
  bronze: gameStore.userInfo?.artifacts_bronze || 0,
  silver: gameStore.userInfo?.artifacts_silver || 0,
  gold: gameStore.userInfo?.artifacts_gold || 0
}))

const totalArtifacts = computed(() => 
  artifacts.value.bronze + artifacts.value.silver + artifacts.value.gold
)

const unlockedAbilities = computed(() => 
  gameStore.userInfo?.unlocked_abilities?.length || 0
)

const totalActions = computed(() => 
  (gameStore.userInfo?.thieves_captured || 0) + 
  (gameStore.userInfo?.garbage_cleaned || 0) + 
  (gameStore.userInfo?.monuments_protected || 0)
)

const guardianAbilities = computed(() => {
  // 根据等级返回已解锁的能力
  const abilities = []
  const level = guardianLevel.value
  
  if (level >= 2) abilities.push({
    id: 'stamina_boost',
    name: t('guardian.abilities.staminaBoost'),
    description: t('guardian.abilities.staminaBoostDesc'),
    icon: '⚡',
    rarity: 'common'
  })
  
  if (level >= 5) abilities.push({
    id: 'treasure_finder',
    name: t('guardian.abilities.treasureFinder'),
    description: t('guardian.abilities.treasureFinderDesc'),
    icon: '🔍',
    rarity: 'rare'
  })
  
  if (level >= 10) abilities.push({
    id: 'master_collector',
    name: t('guardian.abilities.masterCollector'),
    description: t('guardian.abilities.masterCollectorDesc'),
    icon: '👑',
    rarity: 'legendary'
  })
  
  return abilities
})

const nextLevelCost = computed(() => ({
  gold: Math.floor(guardianLevel.value * 100 * 1.5),
  diamond: Math.floor(guardianLevel.value * 10)
}))

const canUpgradeWithGold = computed(() => 
  gameStore.userInfo?.gold >= nextLevelCost.value.gold
)

const canUpgradeWithDiamond = computed(() => 
  gameStore.userInfo?.diamond >= nextLevelCost.value.diamond
)

// 方法
const toggleCard = () => {
  isFlipped.value = !isFlipped.value
}

const upgradeGuardian = async (currency) => {
  if (isUpgrading.value) return
  
  isUpgrading.value = true
  try {
    const response = await api.user.upgradeGuardian(currency)
    if (response.data.success) {
      showSuccessToast(t('guardian.upgradeSuccess'))
      // 更新用户信息
      await gameStore.fetchUserInfo()
    } else {
      showFailToast(response.data.message || t('guardian.upgradeFailed'))
    }
  } catch (error) {
    console.error('Guardian upgrade failed:', error)
    showFailToast(t('guardian.upgradeFailed'))
  } finally {
    isUpgrading.value = false
  }
}

// 生命周期
onMounted(() => {
  // 初始化守护者数据
})
</script>

<style scoped>
.guardian-card-container {
  perspective: 1000px;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.guardian-card {
  position: relative;
  width: 100%;
  height: 500px;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
  cursor: pointer;
}

.guardian-card.flipped {
  transform: rotateY(180deg);
}

.card-front,
.card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.card-back {
  transform: rotateY(180deg);
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.guardian-avatar {
  position: relative;
  width: 80px;
  height: 80px;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.level-badge {
  position: absolute;
  bottom: -5px;
  right: -5px;
  background: #ffd700;
  color: #333;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.guardian-title h3 {
  color: white;
  margin: 0;
  font-size: 24px;
}

.guardian-name {
  color: rgba(255, 255, 255, 0.8);
  margin: 5px 0 0 0;
  font-size: 14px;
}

.exp-section {
  margin-bottom: 20px;
}

.exp-label {
  display: flex;
  justify-content: space-between;
  color: white;
  font-size: 14px;
  margin-bottom: 8px;
}

.exp-bar-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.exp-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.exp-fill {
  height: 100%;
  background: linear-gradient(90deg, #4fc08d, #43a047);
  transition: width 0.3s ease;
}

.exp-percentage {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.quick-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  color: white;
}

.stat-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

.card-footer {
  margin-top: auto;
  text-align: center;
}

.flip-hint {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

/* 背面样式 */
.artifacts-detail,
.abilities-detail,
.upgrade-section {
  margin-bottom: 20px;
}

.artifacts-detail h4,
.abilities-detail h4,
.upgrade-section h4 {
  color: white;
  margin: 0 0 10px 0;
  font-size: 16px;
}

.artifact-grid {
  display: flex;
  gap: 10px;
}

.artifact-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 10px;
  text-align: center;
  color: white;
}

.artifact-icon {
  font-size: 20px;
  margin-bottom: 5px;
}

.artifact-count {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 2px;
}

.artifact-label {
  font-size: 12px;
  opacity: 0.8;
}

.abilities-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ability-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
}

.ability-icon {
  font-size: 20px;
}

.ability-name {
  font-weight: bold;
  font-size: 14px;
}

.ability-desc {
  font-size: 12px;
  opacity: 0.8;
  margin-left: auto;
}

.upgrade-cards {
  display: flex;
  gap: 10px;
}

.upgrade-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 15px;
  text-align: center;
  color: white;
}

.upgrade-card.disabled {
  opacity: 0.5;
}

.upgrade-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.upgrade-cost {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.close-icon {
  color: white;
  font-size: 20px;
  cursor: pointer;
}
</style>
