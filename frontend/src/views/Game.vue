<template>
  <div class="game-container">
    <!-- krpano全景图容器 -->
    <div id="krpano-container" :class="{ hidden: isUIHidden }"></div>
    
    <!-- 游戏UI界面 -->
    <div v-if="!isUIHidden" class="game-ui">
      <!-- 顶部状态栏 -->
      <div class="top-status-bar">

        <!-- 右侧按钮组 -->
        <div class="top-right-buttons">
          <!-- 菜单按钮 -->
          <div class="top-button" @click="showGameMenu = true">
            <img src="/img/page_icons/meun-btn.png" class="button-icon" />
          </div>
          
        </div>

        <!-- 小偷图标和计数 -->
        <div class="status-item thief-remaining">
            <img src="/img/page_icons/thieficon-2.png" class="thief-icon" />
            <span class="counter">{{ gameStore.thieves.length }}</span>
        </div>

         <!-- 垃圾图标和计数 -->
         <div class="status-item garbage-remaining">
            <img src="/img/page_icons/rubbish-1.png" class="garbage-icon" />
            <span class="counter">{{ gameStore.garbages.length }}</span>
        </div>

         <!-- 宝箱图标和计数 -->
         <!-- <div class="status-item treasure-remaining">
            <img src="/img/gold.png" class="treasure-icon" />
            <span class="counter">{{ gameStore.treasures.length }}</span>
        </div> -->
        <!-- 文明经验值 -->
        <div class="status-item civilization-exp-display">
            <img src="/img/page_icons/START-iocn.png" class="civilization-exp-icon" />
            <span class="civilization-exp-value">{{ gameStore.civilizationExp }}</span>
        </div>
        
        <!-- 体力值 -->
        <div class="status-item stamina-display">
            <img src="https://hunyuan-prod-1258344703.cos.ap-guangzhou.myqcloud.com/%2Ftext2img/611fae0b-bf28-4e5f-825b-704a8746c879.jpg?q-sign-algorithm=sha1&q-ak=AKIDRl074nOsGdJ9zjMsCRWP3ShmgS3VtX4S&q-sign-time=1753154021%3B1763954021&q-key-time=1753154021%3B1763954021&q-header-list=host&q-url-param-list=&q-signature=434391524fefaf9b79fbb9fb9cff4755e1c9bbe4&imageMogr2/strip/format/jpg/size-limit/1.9m!/ignore-error/1" class="stamina-icon" />
            <span class="stamina-value">{{ gameStore.stamina }}/{{ gameStore.maxStamina }}</span>
        </div>
        
        <!-- 守望者等级 -->
        <div class="status-item guardian-level-display">
            <img src="/img/page_icons/user_avatar_cicle.png" class="guardian-icon" />
            <span class="guardian-level-text">{{ gameStore.getGuardianLevelName(gameStore.guardianLevel) }}</span>
        </div>
        
        
      </div>
      
      <!-- BOSS区域 -->
      <div class="boss-area">
        <div class="boss-container">
          <div class="boss-label">垃圾大王</div>
          <div class="boss-health-bar">
            <div class="health-bar-bg">
              <div 
                class="health-bar-fill" 
                :style="{ width: `${gameStore.bossHealth}%` }"
              ></div>
            </div>
            <div class="health-text">{{ gameStore.bossHealth }}%</div>
          </div>
        </div>
      </div>
      
      <!-- 左侧工具栏 -->
      <LeftToolbar
        @openTasks="showTasks = true"
        @openRanking="showRanking = true"
        @openCities="showCities = true"
      />
      
      <!-- 右侧道具栏 -->
      <RightToolbar
        @radar-scan-result="onRadarScanResult"
        @magnifier-result="onMagnifierResult"
      />
      <!-- 🚫 PRD合规性清理：移除ammo-consumed事件，PRD中没有弹药概念 -->
      
    </div>
    
    <!-- 仅全景图模式 -->
    <div v-else class="panorama-only-ui">
      <!-- 显示游戏按钮 -->
      <div class="show-ui-button" >
        <div class="menu-text">{{ t('gameMenu.showUI') }}</div>
        <van-switch 
          v-model="isUIHidden" 
          @change="showUI"
          size="24px" 
        />
      </div>

    </div>

    <!-- 弹窗组件 -->
    <GameMenu v-model:show="showGameMenu" @openUpgrade="showUpgrade = true" @openRanking="showRanking = true"  @openTasks="showTasks = true" @hideUI="hideUI" @openCardView="showCardView = true" />
   
    <RankingList v-model:show="showRanking" />
    <PassiveIncome v-model:show="showPassiveIncome" @resource-updated="onResourceUpdated" />
    <TaskCenter
      v-model:show="showTasks"
      @task-completed="onTaskCompleted"
      @reward-claimed="onRewardClaimed"
    />
    <GameGuide v-model="showGuide" @complete="onGuideComplete" />
    <CityDialog v-model:show="showCities" @city-selected="onCitySelected" @openPassiveIncome="showPassiveIncome = true" />
    <CardView v-model:show="showCardView" />

    <!-- 新增的组件 -->
    <BossDialogue 
      v-model:show="showBossDialogue" 
      :stage="bossDialogueStage"
      :health-percentage="gameStore.bossHealth"
      @continue="onBossDialogueContinue"
      @reward-claimed="onBossRewardClaimed"
      @close="showBossDialogue = false"
    />
    
    <MonumentQuiz
      v-model:show="showMonumentQuiz"
      :monument-id="currentMonumentId"
      @success="onMonumentSuccess"
      @failure="onMonumentFailure"
      @close="showMonumentQuiz = false"
    />
    
    <TreasureBoxDialog
      v-model:show="showTreasureBox"
      :treasure-box="currentTreasureBox"
      @claim="claimTreasureBox"
    />

    <AutoReward ref="autoRewardRef" />

  </div>
 
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useGameStore } from '@/stores/game'
import { useAuthStore } from '@/stores/auth'
import krpanoManager from '@/utils/krpano'
import gameService from '@/services/gameService'
import dataService from '@/services/dataService'
import { api } from '@/utils/api'
import GameMenu from '@/components/game/GameMenu.vue'
import RankingList from '@/components/game/RankingList.vue'
import TaskCenter from '@/components/game/TaskCenter.vue'
import LeftToolbar from '@/components/game/LeftToolbar.vue'
import RightToolbar from '@/components/game/RightToolbar.vue'
import GameGuide from '@/components/game/GameGuide.vue'
import CityDialog from '@/components/game/CityDialog.vue'
import PassiveIncome from '@/components/game/PassiveIncome.vue'
import AutoReward from '@/components/game/AutoReward.vue'
import BossDialogue from '@/components/game/BossDialogue.vue'
import MonumentQuiz from '@/components/game/MonumentQuiz.vue'
import TreasureBoxDialog from '@/components/game/TreasureBoxDialog.vue'
import CardView from '@/components/game/CardView.vue'

// 移除冗余的增强组件导入
import { showToast, showSuccessToast, showFailToast, showLoadingToast } from 'vant'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import audioManager from '@/utils/audio'

// 导入热点服务
import krpanoHotspotManager from '@/services/krpanoHotspotManager'

const gameStore = useGameStore()
const authStore = useAuthStore()

// UI状态
const isUIHidden = ref(false)
const showGameMenu = ref(false)
const showUpgrade = ref(false)
const showRanking = ref(false)
const showCollection = ref(false)
const showCardView = ref(false)
const showTasks = ref(false)
const showGuide = ref(false)
const showCities = ref(false)
const showPassiveIncome = ref(false)
const showBossDialogue = ref(false)
const showMonumentQuiz = ref(false)
const showTreasureBox = ref(false)
const currentTreasureBox = ref(null)
const bossDialogueStage = ref(1)
const currentMonumentId = ref('monument_1')


// 添加自动奖励组件引用
const autoRewardRef = ref(null)

// 加载用户资源
const loadUserResources = async () => {
  try {
    const response = await api.collection.getUserResources()
    if (response.data) {
      const resources = response.data

      // 同步到新的文明经验值系统
      gameStore.syncResources(
        resources.civilization_exp || 0, 
        resources.stamina || 100
      )
      
      // 同步收集数据
      gameStore.updateSessionData({
        thievesCollected: resources.materials?.thieves_collected || 0,
        garbageCollected: resources.materials?.garbage_collected || 0,
        artifactsCollected: resources.materials?.artifacts_collected || 0,
        monumentsProtected: resources.materials?.monuments_protected || 0,
        civilizationExp: resources.civilization_exp || 0,
        stamina: resources.stamina || 100
      })

      console.log('用户资源加载成功:', {
        civilizationExp: resources.civilization_exp,
        stamina: resources.stamina,
        thieves: resources.materials?.thieves_collected,
        garbage: resources.materials?.garbage_collected,
        artifacts: resources.materials?.artifacts_collected,
        monuments: resources.materials?.monuments_protected
      })

    } else {
      console.error('获取用户资源失败: 响应数据为空')
    }
  } catch (error) {
    console.error('加载用户资源异常:', error)
  }
}

// 隐藏/显示UI
const hideUI = (value) => {
  isUIHidden.value = value
  // 隐藏krpano热点
  const hotspots = krpanoManager.get('hotspot')

  hotspots.forEach((_, hotspotId) => {
    krpanoManager.call(`set(hotspot[${hotspotId}].visible, false)`)
  })

}

const showUI = () => {
  isUIHidden.value = false
}

const adsCallbacks = {
  adFinished: () => console.log("End midgame ad"),
  adError: (error) => console.log("Error midgame ad", error),
  adStarted: () => console.log("Start midgame ad"),
};
// const showAds = () => {
//   console.log('showAds')
  
// // window.CrazyGames.SDK.ad.requestAd("midgame", adsCallbacks);
// // or
// window.CrazyGames.SDK.ad.requestAd("rewarded", adsCallbacks);
// }




// 初始化游戏
const initGame = async () => {
  try {
    console.log('开始初始化游戏...')
    
    authStore.loadFromStorage()
    
    // 1. 检查并加载持久化的游戏状态
    const hasGameState = gameStore.loadGameState()
    if (hasGameState) {
      console.log('已加载持久化的游戏状态')
    }
    
    console.log(authStore.isLoggedIn,'----')
    // 2. 检查用户登录状态
    if (authStore.isLoggedIn) {
      // 用户已登录，刷新用户信息
      console.log('用户已登录，刷新用户信息')
      await gameService.refreshUserInfo()
    } else if (!authStore.isLoggedIn) {
      // 未登录用户，初始化游客模式
      gameService.initOfflineMode()
      showToast('欢迎游客用户！以离线模式游戏')
    }

    // 3. 加载本地收集数据
    gameStore.loadCollections()

    // 4. 初始化krpano
    await krpanoManager.init('krpano-container')

    // 5. 加载第一个场景
    krpanoManager.loadScene('scene_level_1')

    // 6. 预初始化热点管理系统
    try {
      await krpanoHotspotManager.initialize('scene_level_1')
      console.log('krpano热点管理系统预初始化完成')
    } catch (error) {
      console.error('krpano热点管理系统初始化失败:', error)
    }
    
    // 6.5. 初始化增强热点管理器
    setTimeout(() => {
      initializeEnhancedWorker()
    }, 1500)

    // 7. 注册krpano事件
    setupKrpanoEvents()

    // 8. 等待场景完全加载
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 8.5. 直接获取热点数量
    await updateHotspotCounts()
    
    console.log('✅ 手动更新热点数量完成, 当前gameStore状态:', gameStore.hotspotCounts)

    // 9. 开始游戏会话（保留接口调用，但使用XML热点配置）
    const sessionResult = await gameService.startGameSession('beijing', 'scene_level_1')
    if (sessionResult.success) {
      gameStore.startGame()
      if (sessionResult.session.isOffline) {
        const isCrazyGames = !!(window.CrazyGames && window.CrazyGames.SDK)
        if (!isCrazyGames) {
          showToast('离线模式游戏开始！')
        }
      } else {
        const isCrazyGames = !!(window.CrazyGames && window.CrazyGames.SDK)
        if (!isCrazyGames) {
          showToast('在线模式游戏开始！')
        }
      }
    } else {
      // 即使会话启动失败，也要确保游戏能够进行
      gameStore.startGame()
      const isCrazyGames = !!(window.CrazyGames && window.CrazyGames.SDK)
      if (!isCrazyGames) {
        showToast('游戏会话启动失败，但仍可游戏')
      }
    }

    // 8. 暴露函数给krpano使用
    window.showToast = showToast


    // 10. 检查是否需要显示引导
    checkAndShowGuide()

    // 11. 播放背景音乐
    audioManager.playBgMusic('/audios/Monoman - Meditation Monoman.ogg')

    console.log('游戏初始化完成！')


  } catch (error) {
    console.error('初始化游戏失败:', error)
    showToast('游戏初始化失败: ' + error.message)
  }
}

// 初始化增强Worker系统
const initializeEnhancedWorker = () => {
  if (window.enhancedGameWorker) return

  console.log('🚀 初始化增强Worker系统...')
  
  // 创建Worker
  window.enhancedGameWorker = new Worker('/enhanced-game-worker.js')

  // 设置消息处理
  window.enhancedGameWorker.onmessage = function(e) {
    const { type, ...data } = e.data

    switch (type) {
      case 'hotspotsInitialized':
        console.log(`✅ Worker热点初始化完成: ${data.count} 个热点`)
        break

      case 'hotspotUpdates':
        console.log('🔄 收到Worker热点更新:', data.updates.length, '个更新')
        if (data.updates && data.updates.length > 0) {
          applyEnhancedHotspotUpdates(data.updates)
        }
        break

      case 'clickAnimation':
        executeClickAnimation(data.animation)
        break

      case 'imagePreloaded':
        console.log(`📷 图片预加载: ${data.url} (${data.cacheSize}/${data.cacheSize + data.remaining})`)
        break

      case 'preloadComplete':
        console.log(`✅ Worker图片预加载完成: ${data.totalImages} 张图片`)
        showToast(`图片预加载完成 (${data.totalImages}张)`)
        break

      case 'animationComplete':
        onAnimationComplete(data.hotspotName, data.animationType)
        break

      case 'error':
        console.error('❌ Worker错误:', data.message)
        showToast('Worker发生错误')
        break

      default:
        console.warn(`⚠️ 未处理的Worker消息类型: ${type}`, data)
        break
    }
  }

  console.log('✅ 增强Worker系统初始化完成')
  
  // 初始化热点数据到Worker
  setTimeout(() => {
    initializeHotspotsInWorker()
  }, 1000)
  
  // 设置视角变化监听
  setupViewChangeListener()
}

// 初始化热点数据到Worker
const initializeHotspotsInWorker = async () => {
  if (!window.enhancedGameWorker || !krpanoManager.isReady()) return

  try {
    const hotspotsFromKrpano = await gameService.getHotspotsFromKrpano()
    const allHotspots = [
      ...hotspotsFromKrpano.thieves.map(h => ({ ...h, type: 'thief', baseScale: 1.0 })),
      ...hotspotsFromKrpano.garbage.map(h => ({ ...h, type: 'garbage', baseScale: 1.0 })),
      ...hotspotsFromKrpano.treasure.map(h => ({ ...h, type: 'treasure', baseScale: 1.0 }))
    ]

    // 发送热点数据到Worker
    window.enhancedGameWorker.postMessage({
      type: 'initializeHotspots',
      data: allHotspots
    })

    console.log(`发送 ${allHotspots.length} 个热点数据到Worker`)
  } catch (error) {
    console.error('初始化热点数据到Worker失败:', error)
  }
}

// 设置视角变化监听
const setupViewChangeListener = () => {
  let lastView = { h: 0, v: 0, fov: 90 }
  
  // 定时器备用机制
  setInterval(() => {
    if (!krpanoManager.isReady() || !window.enhancedGameWorker) return

    const currentView = {
      h: krpanoManager.get('view.hlookat') || 0,
      v: krpanoManager.get('view.vlookat') || 0,
      fov: krpanoManager.get('view.fov') || 90
    }

    const hDiff = Math.abs(currentView.h - lastView.h)
    const vDiff = Math.abs(currentView.v - lastView.v)
    const fovDiff = Math.abs(currentView.fov - lastView.fov)

    if (hDiff > 1 || vDiff > 1 || fovDiff > 1) {
      console.log('🔍 视角变化:', { h: currentView.h.toFixed(1), v: currentView.v.toFixed(1), fov: currentView.fov.toFixed(1) })
      
      // 发送视角更新到Worker
      window.enhancedGameWorker.postMessage({
        type: 'updateView',
        data: {
          hlookat: currentView.h,
          vlookat: currentView.v,
          fov: currentView.fov
        }
      })
      lastView = { ...currentView }
    }
  }, 100)

  console.log('✅ 视角变化监听已设置')
}

// 应用增强热点更新
const applyEnhancedHotspotUpdates = (updates) => {
  if (!krpanoManager.isReady()) return

  console.log('🎯 应用热点更新:', updates.length, '个更新')

  updates.forEach(update => {
    const { name, shouldShow, shouldHide, shouldUpdateLOD, showAnimation, hideAnimation, scale } = update
    
    console.log(`热点 ${name}:`, { shouldShow, shouldHide, shouldUpdateLOD, scale })

    if (shouldShow && showAnimation) {
      // 执行显示动画
      console.log(`👁️ 显示热点: ${name}`)
      krpanoManager.call(`
        set(hotspot[${name}].visible, true);
        set(hotspot[${name}].enabled, true);
        set(hotspot[${name}].alpha, 0);
        set(hotspot[${name}].scale, ${scale * 0.3});
        tween(hotspot[${name}].alpha, 1.0, ${showAnimation.duration / 1000}, easeOutBack);
        tween(hotspot[${name}].scale, ${scale}, ${showAnimation.duration / 1000 * 0.6}, easeOutBack);
      `)
    } else if (shouldHide && hideAnimation) {
      // 执行隐藏动画
      console.log(`🙈 隐藏热点: ${name}`)
      krpanoManager.call(`
        tween(hotspot[${name}].alpha, 0, ${hideAnimation.duration / 1000}, easeInBack);
        tween(hotspot[${name}].scale, ${scale * 0.2}, ${hideAnimation.duration / 1000}, easeInBack,
          set(hotspot[${name}].visible, false);
          set(hotspot[${name}].enabled, false);
        );
      `)
    } else if (shouldUpdateLOD) {
      // 更新LOD
      console.log(`🔍 更新LOD: ${name}, scale: ${scale}`)
      krpanoManager.call(`
        tween(hotspot[${name}].scale, ${scale}, 0.3, easeOutQuart);
      `)
    }
  })
}

// 执行点击动画
const executeClickAnimation = (animation) => {
  if (!krpanoManager.isReady()) return

  const { name, phases } = animation
  let animationCode = `set(hotspot[${name}].enabled, false);`

  phases.forEach(phase => {
    const delay = phase.delay || 0
    const duration = phase.duration / 1000

    if (phase.keyframes) {
      // 关键帧动画
      phase.keyframes.forEach((value, index) => {
        const time = (duration / (phase.keyframes.length - 1)) * index
        animationCode += `
          delayedcall(${(delay + time * 1000) / 1000},
            set(hotspot[${name}].${phase.property}, ${value});
          );
        `
      })
    } else {
      // 简单补间动画
      animationCode += `
        delayedcall(${delay / 1000},
          tween(hotspot[${name}].${phase.property}, ${phase.to}, ${duration}, ${phase.easing});
        );
      `
    }
  })

  // 最终隐藏
  animationCode += `
    delayedcall(${animation.duration / 1000},
      set(hotspot[${name}].visible, false);
    );
  `

  krpanoManager.call(animationCode)
}

// 动画完成回调
const onAnimationComplete = (hotspotName, animationType) => {
  console.log(`动画完成: ${hotspotName} (${animationType})`)
}

// 触发点击动画
const triggerClickAnimation = (hotspotName, type) => {
  if (!window.enhancedGameWorker) return

  // 发送动画请求到Worker
  window.enhancedGameWorker.postMessage({
    type: 'triggerClickAnimation',
    data: {
      hotspotName,
      animationType: type
    }
  })
}

// 设置krpano事件监听
const setupKrpanoEvents = () => {
  // 小偷点击事件
  krpanoManager.on('thiefClicked', (name) => {
    handleThiefClick(name)
  })
  
  // 垃圾点击事件
  krpanoManager.on('garbageClicked', (name) => {
    handleGarbageClick(name)
  })
  
  // 宝箱点击事件
  krpanoManager.on('treasureClicked', (name) => {
    handleTreasureClick(name)
  })
  
  // 文明古迹点击事件
  krpanoManager.on('monumentClicked', (name) => {
    handleMonumentClick(name)
  })
  
  // 时空门点击事件
  krpanoManager.on('timegateClicked', () => {
    handleTimegateClick()
  })
  
  // 场景切换事件
  krpanoManager.on('sceneTransition', () => {
    console.log('场景切换事件')
  })
  
  // 进入下一关事件
  krpanoManager.on('enterNextLevel', (gateName) => {
    console.log('进入下一关事件:', gateName)
    handleTimegateClick()
  })
  
  // 新场景加载完成事件
  krpanoManager.on('newPanoLoaded', () => {
    console.log('新场景加载完成，初始化时空门状态')
    // 延迟一点时间确保热点都已加载
    setTimeout(() => {
      updateTimegateVisibility()
    }, 500)
  })
}

// 通用的奖励处理函数
const showRewardAnimation = (reward) => {
  if (reward.type === 'treasure_box') {
    // 宝箱奖励
    const contents = reward.contents || {}
    autoRewardRef.value?.show({
      type: 'treasure_box',
      contents: contents
    })
  } else {
    // 单一奖励 - 将所有奖励转换为文明经验值系统
    let rewardType = 'civilization_exp'
    let amount = reward.amount || 10
    
    // 根据奖励类型调整
    if (reward.type === 'civilization_exp') {
      rewardType = 'civilization_exp'
    } else if (reward.type === 'stamina') {
      rewardType = 'stamina'
      amount = (reward.amount || 1) * 5
    }
    
    autoRewardRef.value?.show({
      type: rewardType,
      amount: amount
    })
  }
}

// 防重复调用的集合
const processingHotspots = new Set()

// 处理小偷点击
const handleThiefClick = async (name) => {
  console.log('🔥 handleThiefClick 被调用:', name)

  // 防重复调用
  if (processingHotspots.has(name)) {
    console.log('⚠️ 热点正在处理中，忽略重复调用:', name)
    return
  }

  processingHotspots.add(name)

  try {
    // 触发Worker动画
    triggerClickAnimation(name, 'thief')
    
    // 移除小偷热点（延迟执行给动画时间）
    setTimeout(() => {
      krpanoManager.removeHotspot(name)
    }, 200)
  
  // 查找对应的热点数据以获取ID
  // 如果有活跃的游戏会话，调用后端API收集热点（直接使用热点名称）
  if (gameService.isSessionActive) {
    try {
      const result = await gameService.collectHotspot(name)
      if (result.success) {
        console.log('后端热点收集成功1:', result.result)
        // 只移除UI显示，不更新收集数量（收集数量由后端数据同步）
        gameStore.removeThiefFromUI(name)
        const reward = result.result.reward

        // 使用统一的奖励动画处理
        showRewardAnimation(reward)

        // 处理奖励
        if(result.result.reward){
          const reward = result.result.reward
          
          // 处理宝箱掉落（小偷10%概率）
          if (reward.type === 'treasure_box') {
            showTreasureBoxModal(reward.contents)
          } else {
            showRewardAnimation(reward)
          }
          
          console.log('收到后端奖励:', reward)
        }
        // 检查是否完成关卡/更新时空门状态
        checkLevelComplete()
      }
    } catch (error) {
      console.error('后端热点收集失败:', error)
    } finally {
      // 清理处理状态
      processingHotspots.delete(name)
    }
  } else {
    // 游客模式：使用新的文明经验值系统
    gameStore.removeThief(name)
    
    // 处理宝箱掉落（小偷10%概率）
    if (Math.random() < 0.1) {
      const treasureBox = generateTreasureBox('thief')
      showTreasureBoxModal(treasureBox)
    }

    // 检查是否完成关卡/更新时空门状态
    checkLevelComplete()

    // 清理处理状态
    processingHotspots.delete(name)
  }
  } catch (error) {
    console.error('处理小偷点击失败:', error)
    // 清理处理状态
    processingHotspots.delete(name)
  }
}

// 处理垃圾点击
const handleGarbageClick = async (name) => {
  // 触发Worker动画
  triggerClickAnimation(name, 'garbage')
  
  // 移除垃圾热点（延迟执行给动画时间）
  setTimeout(() => {
    krpanoManager.removeHotspot(name)
  }, 300)
  
  // 查找对应的热点数据以获取ID
  // 如果有活跃的游戏会话，调用后端API收集热点（直接使用热点名称）
  if (gameService.isSessionActive) {
    try {
      const result = await gameService.collectHotspot(name)
      if (result.success) {
        console.log('后端热点收集成功:', result.result)
        // 只移除UI显示，不更新收集数量（收集数量由后端数据同步）
        gameStore.removeGarbageFromUI(name)
        const reward = result.result.reward

        // 使用统一的奖励动画处理
        showRewardAnimation(reward)

        // 处理奖励
        if(result.result.reward){
          const reward = result.result.reward
          
          // 处理宝箱掉落（垃圾5%概率）
          if (reward.type === 'treasure_box') {
            showTreasureBoxModal(reward.contents)
          } else {
            showRewardAnimation(reward)
          }
          
          console.log('收到后端奖励:', reward)
        }

        // 检查是否完成关卡/更新时空门状态
        checkLevelComplete()
      }
    } catch (error) {
      console.error('后端热点收集失败:', error)
      // 如果后端调用失败，使用本地逻辑作为备用
      gameStore.removeGarbage(name)
    }
  } else {
    // 没有活跃会话时的备用逻辑
    gameStore.removeGarbage(name)
    
    // 处理宝箱掉落（垃圾5%概率）
    if (Math.random() < 0.05) {
      const treasureBox = generateTreasureBox('garbage')
      showTreasureBoxModal(treasureBox)
    }
  }
  
  // 检查是否完成关卡/更新时空门状态
  checkLevelComplete()
}

// 处理宝箱点击
const handleTreasureClick = async (name) => {
  // 触发Worker动画
  triggerClickAnimation(name, 'treasure')
  
  // 移除宝箱热点（延迟执行给动画时间）
  setTimeout(() => {
    krpanoManager.removeHotspot(name)
  }, 500)
  
  // 如果有活跃的游戏会话，调用后端API收集热点（直接使用热点名称）
  if (gameService.isSessionActive) {
    try {
      const result = await gameService.collectHotspot(name)
      if (result.success) {
        console.log('后端热点收集成功:', result.result)
        gameStore.removeTreasure(name)
        const reward = result.result.reward

        // 使用统一的奖励动画处理
        showRewardAnimation(reward)

        // 更新界面显示的资源数量（同步到gameStore）
        if (result.result.progress) {
          // 同步文明经验值等数据
          if (result.result.progress.civilization_exp) {
            gameStore.civilizationExp = result.result.progress.civilization_exp
          }
        }

        // 检查是否完成关卡/更新时空门状态
        checkLevelComplete()
      }
    } catch (error) {
      console.error('后端热点收集失败:', error)
      // 如果后端调用失败，使用本地逻辑作为备用  
      gameStore.removeTreasure(name)
      // 宝箱可能掉落文化图鉴，可在这里处理掉落逻辑
      if (Math.random() < 0.1) { // 10%概率掉落文化图鉴
        const atlas = {
          id: `atlas_${Date.now()}`,
          name: '神秘文化图鉴',
          description: '一份珍贵的文化遺产记录'
        }
        gameStore.addCulturalAtlas(atlas)
      }
    }
  } else {
    // 没有活跃会话时的备用逻辑
    gameStore.removeTreasure(name)
    // 游客模式下的掉落逻辑
    if (Math.random() < 0.1) {
      const atlas = {
        id: `atlas_${Date.now()}`,
        name: '神秘文化图鉴',
        description: '一份珍贵的文化遺产记录'
      }
      gameStore.addCulturalAtlas(atlas)
    }
  }
  
  // 检查是否完成关卡/更新时空门状态
  checkLevelComplete()
}

// 处理文明古迹点击
const handleMonumentClick = (name) => {
  // 显示古迹问答界面
  currentMonumentId.value = name
  showMonumentQuiz.value = true
}

// BOSS对话事件处理
const onBossDialogueContinue = () => {
  // 继续游戏
  gameStore.resumeGame()
  showBossDialogue.value = false
}

const onBossRewardClaimed = (reward) => {
  // 处理BOSS对话奖励
  if (reward.civilizationExp) {
    gameStore.addCivilizationExp(reward.civilizationExp)
  }
  if (reward.stamina) {
    gameStore.restoreStamina(reward.stamina)
  }
}

// 古迹问答事件处理
const onMonumentSuccess = (reward) => {
  // 古迹问答成功
  gameStore.protectMonument(true)
  if (reward.civilizationExp) {
    gameStore.addCivilizationExp(reward.civilizationExp)
  }
  showSuccessToast('古迹保护成功！')
}

const onMonumentFailure = () => {
  // 古迹问答失败
  gameStore.protectMonument(false)
  showFailToast('古迹保护失败！')
}

// 宝箱系统
const generateTreasureBox = (sourceType) => {
  const treasureTypes = [
    {
      type: 'civilization_exp',
      name: '文明经验值',
      amount: sourceType === 'thief' ? 50 : 30,
      probability: 0.25
    },
    {
      type: 'stamina',
      name: '体力值',
      amount: 20,
      probability: 0.25
    },
    {
      type: 'cultural_atlas',
      name: '文化图鉴',
      amount: 1,
      probability: 0.2
    },
    {
      type: 'tools',
      name: '道具',
      tools: ['radar', 'magnifier'],
      probability: 0.15
    },
    {
      type: 'repentance_letter',
      name: '悔过书',
      amount: 1,
      description: '小偷的悔过书，可以增加文明经验值',
      probability: 0.1
    },
    {
      type: 'avatar_position',
      name: '头像位置编辑',
      amount: 1,
      description: '允许编辑守望者头像在地图上的位置',
      probability: 0.05
    }
  ]
  
  const random = Math.random()
  let cumulativeProbability = 0
  
  for (const treasure of treasureTypes) {
    cumulativeProbability += treasure.probability
    if (random <= cumulativeProbability) {
      return {
        type: treasure.type,
        name: treasure.name,
        amount: treasure.amount,
        tools: treasure.tools,
        sourceType
      }
    }
  }
  
  // 默认返回文明经验值
  return treasureTypes[0]
}

const showTreasureBoxModal = (treasureBox) => {
  currentTreasureBox.value = treasureBox
  showTreasureBox.value = true
}

const claimTreasureBox = (useAd = false) => {
  const treasure = currentTreasureBox.value
  if (!treasure) return
  
  const multiplier = useAd ? 2 : 1
  
  switch (treasure.type) {
    case 'civilization_exp':
      gameStore.addCivilizationExp(treasure.amount * multiplier)
      showSuccessToast(`获得 ${treasure.amount * multiplier} 文明经验值！`)
      break
      
    case 'stamina':
      gameStore.restoreStamina(treasure.amount * multiplier)
      showSuccessToast(`恢复 ${treasure.amount * multiplier} 体力值！`)
      break
      
    case 'cultural_atlas':
      const atlasCount = treasure.amount * multiplier
      for (let i = 0; i < atlasCount; i++) {
        const atlas = {
          id: `atlas_${Date.now()}_${i}`,
          name: '珍贵文化图鉴',
          description: '从宝箱中获得的文化遗产记录'
        }
        gameStore.addCulturalAtlas(atlas)
      }
      showSuccessToast(`获得 ${atlasCount} 个文化图鉴！`)
      break
      
    case 'tools':
      if (treasure.tools) {
        treasure.tools.forEach(tool => {
          gameStore.tools[tool].count += multiplier
        })
        showSuccessToast(`获得道具奖励！`)
      }
      break
      
    case 'repentance_letter':
      // 悔过书给予额外文明经验值
      const expBonus = 30 * multiplier
      gameStore.addCivilizationExp(expBonus)
      showSuccessToast(`获得悔过书！额外获得 ${expBonus} 文明经验值！`)
      break
      
    case 'avatar_position':
      // 头像位置编辑权限（这里可以存储到用户偏好中）
      showSuccessToast(`获得头像位置编辑权限！可以自定义守望者头像位置`)
      // TODO: 实现头像位置编辑功能
      break
  }
  
  showTreasureBox.value = false
  currentTreasureBox.value = null
}

// 处理时空之门点击
const handleTimegateClick = () => {
  // 检查是否可以进入下一关
  if (!gameStore.canOpenTimegate) {
    const required = gameStore.currentLevelInfo?.requiredCollections || 0
    const current = gameStore.currentLevelCollections
    showToast(`还需要收集 ${required - current} 个图鉴才能开启时空门！`)
    return
  }
  
  // 检查是否有下一关
  if (gameStore.currentLevel >= 8) {
    showToast('恭喜！你已经完成了所有关卡！')
    return
  }
  
  // 进入下一关
  gameStore.currentLevel++
  gameStore.unlockNextLevel()
  
  // 加载新场景
  const sceneName = `scene_level_${gameStore.currentLevel}`
  
  // 如果场景不存在，显示提示
  if (gameStore.currentLevel > 2) {
    showToast('下一关卡正在开发中...')
    gameStore.currentLevel-- // 回退
    return
  }
  
  krpanoManager.loadScene(sceneName)
  
  // 重新初始化关卡数据
  initLevelData()
  
  showToast(`欢迎来到第 ${gameStore.currentLevel} 关！`)
}



// 初始化关卡数据 - 纯krpano内置管理
const initLevelData = async () => {
  // 重置当前关卡收集进度
  gameStore.resetCurrentLevelCollections()
  
  // 初始化时空门状态
  updateTimegateVisibility()
}


// 更新时空门可见性
const updateTimegateVisibility = () => {
  // const canOpen = gameStore.canOpenTimegate
  // const required = gameStore.currentLevelInfo?.requiredCollections || 0
  // const current = gameStore.currentLevelCollections
  
  // console.log(`时空门状态检查: canOpen=${canOpen}, 收集进度=${current}/${required}`)
  
  // if (canOpen) {
  //   // krpanoManager.set('hotspot[timegate].visible', true)
  //   // 显示开启的时空门（动画版）
  //   krpanoManager.set('hotspot[timegate_lock].visible', false)
    
  //   // 显示指向时空门的箭头
  //   krpanoManager.set('layer[connections].visible', true)
  //   krpanoManager.call('updateConnections')
    
  //   console.log('时空门已开启，显示动画门和箭头')
  //   showToast('时空门已开启！可以进入下一关了！')
  // } else {
  //   // 显示锁定的时空门
  //   // krpanoManager.set('hotspot[timegate].visible', false)
  //   krpanoManager.set('hotspot[timegate_lock].visible', true)
    
  //   // 隐藏箭头
  //   krpanoManager.set('layer[connections].visible', false)
    
  //   console.log('时空门锁定，显示锁定图标')
  // }
  
  
}

// 检查关卡是否完成
const checkLevelComplete = () => {
  // 更新时空门状态
  updateTimegateVisibility()
  
  // 如果所有小偷都被抓到，给予额外奖励
  if (gameStore.hotspotCounts.remainingThieves === 0) {
    const stars = calculateStars()
    gameStore.updateLevelStars(stars)
    
    showToast('关卡完成！所有小偷都被抓获！')
  }
}

// 计算星级
const calculateStars = () => {
  // 根据时间、收集情况等计算星级
  if (gameStore.gameTime > 240) return 3
  if (gameStore.gameTime > 180) return 2
  return 1
}

// 检查并显示引导
const checkAndShowGuide = () => {
  // 检查数据服务中是否已经显示过引导
  const hasShownGuide = dataService.getItem('gameGuideShown')
  
  if (!hasShownGuide) {
    // 延迟一秒后显示引导，让游戏先加载完成
    setTimeout(() => {
      showGuide.value = true
    }, 1000)
  }
}

// 引导完成处理
const onGuideComplete = () => {
  console.log('游戏引导已完成')
  // 可以在这里添加其他逻辑，比如给新手奖励等
}




// 定时器
let gameTimer = null

const startGameTimer = () => {
  gameTimer = setInterval(() => {
    if (!gameStore.isGamePaused && gameStore.gameTime > 0) {
      gameStore.gameTime--
      
      // 道具冷却时间
      if (gameStore.tools.radar.cooldown > 0) {
        gameStore.tools.radar.cooldown--
      }
      if (gameStore.tools.magnifier.cooldown > 0) {
        gameStore.tools.magnifier.cooldown--
      }
      
      // 时间到了
      if (gameStore.gameTime === 0) {
        // TODO: 游戏结束逻辑
      }
    }
  }, 1000)
}

const showUpgradeBadge = computed(() => {
  // 根据文明经验值可以升级守望者等级
  return gameStore.civilizationExp >= 100
})

// 任务相关事件处理
const onTaskCompleted = (data) => {
  showSuccessToast(`恭喜完成任务：${data.taskName}`)
}

const onRewardClaimed = (data) => {
  // 更新用户资源 - 使用新的文明经验值系统
  if (data.rewards?.civilizationExp) {
    gameStore.addCivilizationExp(data.rewards.civilizationExp)
  }
  if (data.rewards?.stamina) {
    gameStore.restoreStamina(data.rewards.stamina)
  }
}

// 被动收益资源更新处理
const onResourceUpdated = (data) => {
  if (data.civilizationExp !== undefined) {
    gameStore.addCivilizationExp(data.civilizationExp)
  }
  if (data.stamina !== undefined) {
    gameStore.restoreStamina(data.stamina)
  }

  const rewardText = []
  if (data.rewards?.civilizationExp) rewardText.push(`${data.rewards.civilizationExp} 文明经验值`)
  if (data.rewards?.stamina) rewardText.push(`${data.rewards.stamina} 体力值`)

  showSuccessToast(`获得 ${rewardText.join(', ')}${data.isDouble ? ' (双倍奖励)' : ''}`)
}

// 雷达扫描结果处理
const onRadarScanResult = (data) => {
  console.log('雷达扫描结果:', data)
  // 这里可以在地图上显示热点标记
}

// 放大镜结果处理
const onMagnifierResult = (data) => {
  console.log('放大镜搜索结果:', data)
  showSuccessToast(`发现 ${data.foundItems.length} 个隐藏物品`)
}

// 城市选择处理
const onCitySelected = (cityId) => {
  console.log('选择城市:', cityId)
  // 可以在这里切换到对应的城市场景
  if (cityId !== gameStore.currentCity) {
    gameStore.currentCity = cityId
    // showSuccessToast(`切换到 ${cityId}`)

    // 如果需要，可以加载对应城市的场景
    // krpanoManager.loadScene(`${cityId}_scene1`)
  }
}


// 更新热点数量
const updateHotspotCounts = async () => {
  try {
    const hotspotsFromXML = await gameService.getHotspotsFromKrpano()

    // 更新gameStore中的热点数组
    gameStore.thieves = hotspotsFromXML.thieves
    gameStore.garbages = hotspotsFromXML.garbage
    gameStore.treasures = hotspotsFromXML.treasure

    // 更新统计数据（虽然现在界面直接使用数组长度，但保留以备后用）
    const hotspotCounts = {
      totalThieves: hotspotsFromXML.thieves.length,
      totalGarbage: hotspotsFromXML.garbage.length,
      totalTreasure: hotspotsFromXML.treasure.length,
      totalBoss: hotspotsFromXML.boss.length,
      remainingThieves: hotspotsFromXML.thieves.length,
      remainingGarbage: hotspotsFromXML.garbage.length,
      remainingTreasure: hotspotsFromXML.treasure.length,
      remainingBoss: hotspotsFromXML.boss.length
    }

    gameStore.updateHotspotCounts(hotspotCounts)
  } catch (error) {
    console.error('更新热点数量失败:', error)
  }
}





onMounted(() => {
  initGame()
  startGameTimer()

  // 加载用户资源
  loadUserResources()

  // 监听页面卸载事件，保存游戏状态
  window.addEventListener('beforeunload', handleBeforeUnload)
  
  // 监听BOSS对话事件
  window.addEventListener('bossDialogue', (event) => {
    const { stage, healthPercent } = event.detail
    bossDialogueStage.value = stage
    showBossDialogue.value = true
    console.log(`BOSS对话触发: 阶段${stage}, 血量${healthPercent}%`)
  })
  
  // 监听BOSS被击败事件
  window.addEventListener('bossDefeated', () => {
    showSuccessToast('恭喜！垃圾大王被击败了！')
    // 可以在这里添加胜利动画或跳转到下一关
  })
})

onUnmounted(async () => {
  if (gameTimer) {
    clearInterval(gameTimer)
  }
  
  // 清理krpano热点管理器
  try {
    await krpanoHotspotManager.cleanup()
  } catch (error) {
    console.error('清理krpano热点管理器失败:', error)
  }
  
  // 清理Worker
  if (window.enhancedGameWorker) {
    window.enhancedGameWorker.postMessage({ type: 'cleanup' })
    window.enhancedGameWorker.terminate()
    delete window.enhancedGameWorker
    console.log('✅ 增强Worker已清理')
  }
  
  // 保存游戏状态
  gameStore.saveGameState()
  
  // 移除事件监听
  window.removeEventListener('beforeunload', handleBeforeUnload)
  window.removeEventListener('bossDialogue', () => {})
  window.removeEventListener('bossDefeated', () => {})
})

// 处理页面卸载
const handleBeforeUnload = () => {
  // 保存游戏状态
  gameStore.saveGameState()
  console.log('页面即将卸载，游戏状态已保存')
}

</script>

<style lang="scss" scoped>
.game-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: #000;
}

#krpano-container {
  width: 100%;
  height: 100%;
  
  // &.hidden {
  //   filter: blur(5px);
  // }
}

.game-ui {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
  
  > * {
    pointer-events: auto;
  }
}

// 顶部状态栏
.top-status-bar {
  position: absolute;
  top: 20px;
  width: 100%;
  padding-left: 20px;
  padding-right: 12px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 28px;
  z-index: 100;
}

.status-item {
  width: 120px;
  height: 48px;
  background-image: url('/img/page_icons/UI-Counter-BG.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  align-items: center;
  
}
.thief-remaining, .garbage-remaining, .treasure-remaining {
    
    .thief-icon{
      width: 70px;
      height: 68px;
      margin-left: -20px;
    }

    .garbage-icon {
      width: 60px;
      height: 58px;
    }

    .treasure-icon {
      width: 50px;
      height: 50px;
      margin-left: -20px;
    }
    
    .counter {
      font-size: 20px;
      color: #fff;
      font-weight: bold;
    }
}
// 新系统显示样式
.civilization-exp-display, .stamina-display, .guardian-level-display {
    
    .civilization-exp-icon, .stamina-icon, .guardian-icon {
      width: 50px;
      height: 50px;
      margin-left: -10px;
    }
    
    .civilization-exp-value, .stamina-value, .guardian-level-text {
      color: #fff;
      font-size: 16px;
      font-weight: bold;
      margin-left: 5px;
    }
    
    .guardian-level-text {
      font-size: 14px;
      max-width: 60px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
}


.top-right-buttons {
  display: flex;
  margin-right: auto;
}

.top-button {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  outline: none;
  transition: all 0.3s ease;
  
  .button-icon {
    width: 102px;
    height: 82px;
  }
  
  &:hover {
    transform: scale(1.1);
    // background: rgba(255, 255, 255, 1);
  }
  
  // &.active {
  //   background: rgba(52, 211, 153, 0.9);
  //   color: white;
    
  //   &:hover {
  //     background: rgba(52, 211, 153, 1);
  //   }
  // }
}

// BOSS区域
.boss-area {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 100px;
  background: rgba(255, 0, 0, 0.8);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.boss-label {
  font-size: $font-xxl;
  color: #fff;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}





// 仅全景图模式
.panorama-only-ui {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
  font-size: 24px;
  color: #4e2828;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  > * {
    pointer-events: auto;
  }
}
:deep(.van-switch) {
  --van-switch-on-background: linear-gradient(45deg, #4CAF50, #45a049);
}

.show-ui-button {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  outline: none;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.1);
  }
  .menu-text{
    margin-right: 20px;
  }
  
}

/* 🚫 PRD合规性清理：移除弹药显示样式
   PRD中没有弹药概念，只有简单的收集机制 */

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

@keyframes lowStaminaAlert {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 攻击按钮样式 */
.bottom-box-2-item.charge-attack {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #000;
  font-weight: bold;
  animation: chargeReady 1.5s infinite alternate;
}

@keyframes chargeReady {
  0% { transform: scale(1); }
  100% { transform: scale(1.05); }
}

.charge-bonus {
  font-size: 10px;
  margin-left: 5px;
  color: #ff6b35;
  font-weight: bold;
}




</style>