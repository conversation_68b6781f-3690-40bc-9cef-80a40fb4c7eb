"""
游戏数据模型 - 用于管理后台访问游戏数据库
"""
from sqlalchemy import Column, BigInteger, String, Integer, DateTime, Boolean, Enum, JSON, Index, Numeric
from sqlalchemy.sql import func
from datetime import datetime
import enum

from app.core.database import Base


class SessionStatus(str, enum.Enum):
    """游戏会话状态"""
    ACTIVE = "active"
    COMPLETED = "completed"
    ABANDONED = "abandoned"


class LoginType(str, enum.Enum):
    """登录类型"""
    GUEST = "guest"
    FACEBOOK = "facebook"
    GOOGLE = "google"
    APPLE = "apple"


class GameUser(Base):
    """游戏用户表（只读，用于管理后台）"""
    __tablename__ = "users"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(String(64), unique=True, nullable=False, comment="用户唯一标识")
    nickname = Column(String(50), comment="昵称")
    avatar_url = Column(String(255), comment="头像URL")
    level = Column(Integer, default=1, comment="用户等级")
    exp = Column(Integer, default=0, comment="经验值")
    vip_level = Column(Integer, default=0, comment="VIP等级")
    vip_expire_time = Column(DateTime, comment="VIP过期时间")
    gold = Column(Integer, default=0, comment="金币数量")
    diamond = Column(Integer, default=0, comment="钻石数量")
    total_play_time = Column(Integer, default=0, comment="总游戏时长(秒)")
    last_login_time = Column(DateTime, comment="最后登录时间")
    is_deleted = Column(Boolean, default=False, comment="软删除标记")
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "nickname": self.nickname,
            "avatar_url": self.avatar_url,
            "level": self.level,
            "exp": self.exp,
            "vip_level": self.vip_level,
            "vip_expire_time": self.vip_expire_time.isoformat() if self.vip_expire_time else None,
            "gold": self.gold,
            "diamond": self.diamond,
            "total_play_time": self.total_play_time,
            "last_login_time": self.last_login_time.isoformat() if self.last_login_time else None,
            "is_deleted": self.is_deleted,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class GameSession(Base):
    """游戏会话表"""
    __tablename__ = "game_sessions"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    session_id = Column(String(64), unique=True, nullable=False, comment="会话唯一标识")
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    scene_id = Column(String(50), nullable=False, comment="场景ID")
    city_id = Column(String(50), nullable=False, comment="城市ID")
    status = Column(Enum(SessionStatus), default=SessionStatus.ACTIVE)
    thieves_collected = Column(Integer, default=0, comment="收集的小偷数量")
    garbage_collected = Column(Integer, default=0, comment="收集的垃圾军团数量")
    gold_earned = Column(Integer, default=0, comment="获得的金币")
    diamond_earned = Column(Integer, default=0, comment="获得的钻石")
    started_at = Column(DateTime, server_default=func.now())
    ended_at = Column(DateTime)
    duration = Column(Integer, default=0, comment="持续时间(秒)")
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "user_id": self.user_id,
            "scene_id": self.scene_id,
            "city_id": self.city_id,
            "status": self.status.value if self.status else None,
            "thieves_collected": self.thieves_collected,
            "garbage_collected": self.garbage_collected,
            "gold_earned": self.gold_earned,
            "diamond_earned": self.diamond_earned,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "ended_at": self.ended_at.isoformat() if self.ended_at else None,
            "duration": self.duration
        }


class UserLoginLog(Base):
    """用户登录日志表"""
    __tablename__ = "user_login_logs"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False, comment="用户ID")
    user_uuid = Column(String(64), nullable=False, comment="用户UUID")
    login_time = Column(DateTime, server_default=func.now())
    login_ip = Column(String(45))
    device_info = Column(String(255))
    login_type = Column(Enum(LoginType), default=LoginType.GUEST)
    session_duration = Column(Integer, default=0, comment="会话时长(秒)")


class BossBattle(Base):
    """BOSS战斗记录表"""
    __tablename__ = "boss_battles"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False)
    session_id = Column(BigInteger, nullable=False)
    boss_id = Column(String(50), nullable=False, comment="BOSS ID")
    damage_dealt = Column(Integer, default=0, comment="造成的伤害")
    is_killed = Column(Boolean, default=False, comment="是否击杀")
    ammo_used = Column(Integer, default=0, comment="使用的弹药数量")
    gold_reward = Column(Integer, default=0)
    diamond_reward = Column(Integer, default=0)
    artifact_reward = Column(String(50), comment="掉落的文物ID")
    battle_time = Column(DateTime, server_default=func.now())


class AdWatchRecord(Base):
    """广告观看记录表"""
    __tablename__ = "ad_watch_records"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False)
    ad_type = Column(Enum("gold", "diamond", "double_reward", name="ad_type"), nullable=False)
    reward_type = Column(String(50), nullable=False)
    reward_amount = Column(Integer, nullable=False)
    date = Column(DateTime, nullable=False)
    watch_count = Column(Integer, default=1, comment="当日观看次数")
    watched_at = Column(DateTime, server_default=func.now())


class LeaderboardSnapshot(Base):
    """排行榜快照表"""
    __tablename__ = "leaderboard_snapshots"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False)
    leaderboard_type = Column(Enum("artifact_collector", "city_guardian", "weekly_active", name="leaderboard_type"), nullable=False)
    rank = Column(Integer, nullable=False)
    score = Column(Integer, nullable=False)
    extra_data = Column(JSON, comment="额外数据")
    snapshot_date = Column(DateTime, nullable=False)
    created_at = Column(DateTime, server_default=func.now())


class HotspotType(str, enum.Enum):
    """热点类型枚举"""
    THIEF = "thief"
    GARBAGE = "garbage"
    TREASURE = "treasure"
    BOSS_THIEF = "boss_thief"


class RewardType(str, enum.Enum):
    """奖励类型枚举"""
    EXPERIENCE = "experience"
    ARTIFACT = "artifact"
    TREASURE_BOX = "treasure_box"


class SceneHotspot(Base):
    """场景热点配置表"""
    __tablename__ = "scene_hotspots"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    scene_id = Column(String(50), nullable=False, comment="场景ID")
    city_id = Column(String(50), nullable=False, comment="城市ID")
    hotspot_name = Column(String(50), nullable=False, comment="热点名称")
    hotspot_type = Column(Enum("thief", "garbage", "treasure", "boss_thief", name="hotspot_type_enum"), nullable=False, comment="热点类型")

    # 位置信息
    position_x = Column(Numeric(10, 6), nullable=False, comment="X坐标(ath)")
    position_y = Column(Numeric(10, 6), nullable=False, comment="Y坐标(atv)")
    scale = Column(Numeric(5, 2), default=1.0, comment="缩放比例")

    # 显示信息
    image_url = Column(String(500), comment="热点图片URL")
    visible = Column(Boolean, default=True, comment="是否可见")
    enabled = Column(Boolean, default=True, comment="是否启用")

    # 奖励信息
    has_reward = Column(Boolean, default=True, comment="是否产生奖励")
    reward_type = Column(Enum("experience", "artifact", "treasure_box", name="reward_type_enum"), nullable=False, comment="奖励类型")
    reward_min = Column(Integer, default=0, comment="奖励最小值")
    reward_max = Column(Integer, default=0, comment="奖励最大值")
    reward_amount = Column(Integer, default=0, comment="奖励数量（兼容旧版本）")
    reward_data = Column(JSON, comment="完整奖励数据")

    # 交互信息
    onclick_action = Column(String(200), comment="点击事件")
    description = Column(String(500), comment="热点描述")

    # 元数据
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    created_by = Column(String(50), comment="创建者")

    # 索引
    __table_args__ = (
        Index("idx_scene_city", "scene_id", "city_id"),
        Index("idx_hotspot_type", "hotspot_type"),
        Index("idx_visible_enabled", "visible", "enabled"),
        Index("idx_has_reward", "has_reward"),
        Index("idx_reward_type", "reward_type"),
    )

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "scene_id": self.scene_id,
            "city_id": self.city_id,
            "hotspot_name": self.hotspot_name,
            "hotspot_type": self.hotspot_type if isinstance(self.hotspot_type, str) else (self.hotspot_type.value if self.hotspot_type else None),
            "position_x": float(self.position_x) if self.position_x else 0,
            "position_y": float(self.position_y) if self.position_y else 0,
            "scale": float(self.scale) if self.scale else 1.0,
            "image_url": self.image_url,
            "visible": self.visible,
            "enabled": self.enabled,
            "has_reward": self.has_reward,
            "reward_type": self.reward_type if isinstance(self.reward_type, str) else (self.reward_type.value if self.reward_type else None),
            "reward_min": self.reward_min,
            "reward_max": self.reward_max,
            "reward_amount": self.reward_amount,
            "reward_data": self.reward_data,
            "onclick_action": self.onclick_action,
            "description": self.description,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "created_by": self.created_by
        }


class HotspotRewardConfig(Base):
    """热点奖励配置表"""
    __tablename__ = "hotspot_reward_config"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    hotspot_type = Column(String(50), nullable=False, comment="热点类型：thief, garbage, treasure_box, boss")
    base_drop_rate = Column(Numeric(5,4), nullable=False, default=0.5000, comment="基础掉落概率(0-1)")
    reward_type = Column(String(20), nullable=False, comment="奖励类型：gold, diamond, artifact")
    min_amount = Column(Integer, nullable=False, default=1, comment="最小奖励数量")
    max_amount = Column(Integer, nullable=False, default=10, comment="最大奖励数量")
    weight = Column(Integer, nullable=False, default=100, comment="权重，用于多奖励随机")
    is_enabled = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "hotspot_type": self.hotspot_type,
            "base_drop_rate": float(self.base_drop_rate),
            "reward_type": self.reward_type,
            "min_amount": self.min_amount,
            "max_amount": self.max_amount,
            "weight": self.weight,
            "is_enabled": self.is_enabled,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class SessionRandomSeed(Base):
    """游戏会话随机种子表"""
    __tablename__ = "session_random_seed"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(36), nullable=False, unique=True, comment="会话ID")
    user_id = Column(Integer, nullable=False, comment="用户ID")
    random_seed = Column(BigInteger, nullable=False, comment="随机数种子")
    thief_collected_count = Column(Integer, default=0, comment="本会话已收集小偷数量")
    garbage_collected_count = Column(Integer, default=0, comment="本会话已收集垃圾数量")
    treasure_collected_count = Column(Integer, default=0, comment="本会话已收集宝箱数量")
    boss_defeated_count = Column(Integer, default=0, comment="本会话已击败BOSS数量")
    created_at = Column(DateTime, server_default=func.now())


class HotspotDropHistory(Base):
    """热点掉落历史记录表"""
    __tablename__ = "hotspot_drop_history"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(36), nullable=False, comment="会话ID")
    user_id = Column(Integer, nullable=False, comment="用户ID")
    hotspot_type = Column(String(50), nullable=False, comment="热点类型")
    hotspot_name = Column(String(100), nullable=False, comment="热点名称")
    reward_type = Column(String(20), comment="获得奖励类型，NULL表示无奖励")
    reward_amount = Column(Integer, default=0, comment="获得奖励数量")
    drop_probability = Column(Numeric(5,4), nullable=False, comment="实际掉落概率")
    random_value = Column(Numeric(10,8), nullable=False, comment="随机数值")
    dropped = Column(Boolean, nullable=False, default=False, comment="是否掉落奖励")
    collected_at = Column(DateTime, server_default=func.now())

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "user_id": self.user_id,
            "hotspot_type": self.hotspot_type,
            "hotspot_name": self.hotspot_name,
            "reward_type": self.reward_type,
            "reward_amount": self.reward_amount,
            "drop_probability": float(self.drop_probability) if self.drop_probability else 0,
            "random_value": float(self.random_value),
            "dropped": self.dropped,
            "collected_at": self.collected_at.isoformat() if self.collected_at else None
        }
