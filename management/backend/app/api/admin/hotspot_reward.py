"""
热点奖励配置管理API
"""
import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func

from app.core.database import get_game_db
from app.models.game_data import HotspotRewardConfig, HotspotDropHistory, SessionRandomSeed
from app.services.hotspot_reward_service import hotspot_reward_service

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/configs", summary="获取热点奖励配置列表")
async def get_reward_configs(
    hotspot_type: Optional[str] = Query(None, description="热点类型筛选"),
    reward_type: Optional[str] = Query(None, description="奖励类型筛选"),
    is_enabled: Optional[bool] = Query(None, description="启用状态筛选"),
    db: AsyncSession = Depends(get_game_db)
):
    """获取热点奖励配置列表"""
    try:
        query = select(HotspotRewardConfig).order_by(
            desc(HotspotRewardConfig.hotspot_type),
            desc(HotspotRewardConfig.weight)
        )
        
        # 添加筛选条件
        if hotspot_type:
            query = query.where(HotspotRewardConfig.hotspot_type == hotspot_type)
        if reward_type:
            query = query.where(HotspotRewardConfig.reward_type == reward_type)
        if is_enabled is not None:
            query = query.where(HotspotRewardConfig.is_enabled == is_enabled)
            
        result = await db.execute(query)
        configs = result.scalars().all()
        
        return {
            "success": True,
            "data": [config.to_dict() for config in configs]
        }
        
    except Exception as e:
        logger.error(f"获取奖励配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取奖励配置失败: {str(e)}")

@router.post("/configs", summary="创建热点奖励配置")
async def create_reward_config(
    config_data: Dict[str, Any],
    db: AsyncSession = Depends(get_game_db)
):
    """创建新的热点奖励配置"""
    try:
        # 验证必填字段
        required_fields = ["hotspot_type", "base_drop_rate", "reward_type", "min_amount", "max_amount"]
        for field in required_fields:
            if field not in config_data:
                raise HTTPException(status_code=400, detail=f"缺少必填字段: {field}")
        
        # 验证数值范围
        if not (0 <= config_data["base_drop_rate"] <= 1):
            raise HTTPException(status_code=400, detail="掉落概率必须在0-1之间")
        
        if config_data["min_amount"] > config_data["max_amount"]:
            raise HTTPException(status_code=400, detail="最小数量不能大于最大数量")
            
        config = await hotspot_reward_service.create_reward_config(db, config_data)
        
        if not config:
            raise HTTPException(status_code=500, detail="创建配置失败")
            
        return {
            "success": True,
            "message": "奖励配置创建成功",
            "data": config.to_dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建奖励配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建奖励配置失败: {str(e)}")

@router.put("/configs/{config_id}", summary="更新热点奖励配置")
async def update_reward_config(
    config_id: int,
    updates: Dict[str, Any],
    db: AsyncSession = Depends(get_game_db)
):
    """更新热点奖励配置"""
    try:
        # 验证数值范围
        if "base_drop_rate" in updates and not (0 <= updates["base_drop_rate"] <= 1):
            raise HTTPException(status_code=400, detail="掉落概率必须在0-1之间")
        
        if "min_amount" in updates and "max_amount" in updates:
            if updates["min_amount"] > updates["max_amount"]:
                raise HTTPException(status_code=400, detail="最小数量不能大于最大数量")
                
        success = await hotspot_reward_service.update_reward_config(db, config_id, updates)
        
        if not success:
            raise HTTPException(status_code=404, detail="配置不存在")
            
        return {
            "success": True,
            "message": "奖励配置更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新奖励配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新奖励配置失败: {str(e)}")

@router.delete("/configs/{config_id}", summary="删除热点奖励配置")
async def delete_reward_config(
    config_id: int,
    db: AsyncSession = Depends(get_game_db)
):
    """删除热点奖励配置"""
    try:
        success = await hotspot_reward_service.delete_reward_config(db, config_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="配置不存在")
            
        return {
            "success": True,
            "message": "奖励配置删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除奖励配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除奖励配置失败: {str(e)}")

@router.get("/statistics", summary="获取奖励掉落统计")
async def get_reward_statistics(
    user_id: Optional[int] = Query(None, description="用户ID筛选"),
    hotspot_type: Optional[str] = Query(None, description="热点类型筛选"),
    limit: int = Query(100, description="限制记录数"),
    db: AsyncSession = Depends(get_game_db)
):
    """获取奖励掉落统计"""
    try:
        stats = await hotspot_reward_service.get_hotspot_statistics(db, user_id)
        
        return {
            "success": True,
            "data": stats
        }
        
    except Exception as e:
        logger.error(f"获取奖励统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取奖励统计失败: {str(e)}")

@router.get("/history", summary="获取掉落历史记录")
async def get_drop_history(
    user_id: Optional[int] = Query(None, description="用户ID筛选"),
    session_id: Optional[str] = Query(None, description="会话ID筛选"),
    hotspot_type: Optional[str] = Query(None, description="热点类型筛选"),
    dropped_only: bool = Query(False, description="只显示掉落记录"),
    limit: int = Query(100, description="限制记录数"),
    offset: int = Query(0, description="偏移量"),
    db: AsyncSession = Depends(get_game_db)
):
    """获取掉落历史记录"""
    try:
        query = select(HotspotDropHistory).order_by(desc(HotspotDropHistory.collected_at))
        
        # 添加筛选条件
        if user_id:
            query = query.where(HotspotDropHistory.user_id == user_id)
        if session_id:
            query = query.where(HotspotDropHistory.session_id == session_id)
        if hotspot_type:
            query = query.where(HotspotDropHistory.hotspot_type == hotspot_type)
        if dropped_only:
            query = query.where(HotspotDropHistory.dropped == True)
            
        # 分页
        query = query.limit(limit).offset(offset)
        
        result = await db.execute(query)
        histories = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count(HotspotDropHistory.id))
        if user_id:
            count_query = count_query.where(HotspotDropHistory.user_id == user_id)
        if session_id:
            count_query = count_query.where(HotspotDropHistory.session_id == session_id)
        if hotspot_type:
            count_query = count_query.where(HotspotDropHistory.hotspot_type == hotspot_type)
        if dropped_only:
            count_query = count_query.where(HotspotDropHistory.dropped == True)
            
        count_result = await db.execute(count_query)
        total = count_result.scalar()
        
        return {
            "success": True,
            "data": {
                "records": [history.to_dict() for history in histories],
                "total": total,
                "limit": limit,
                "offset": offset
            }
        }
        
    except Exception as e:
        logger.error(f"获取掉落历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取掉落历史失败: {str(e)}")

@router.get("/types", summary="获取热点类型和奖励类型列表")
async def get_types():
    """获取热点类型和奖励类型列表"""
    try:
        return {
            "success": True,
            "data": {
                "hotspot_types": [
                    {"value": "thief", "label": "小偷"},
                    {"value": "garbage", "label": "垃圾"},
                    {"value": "treasure_box", "label": "宝箱"},
                    {"value": "boss", "label": "BOSS"}
                ],
                "reward_types": [
                    {"value": "experience", "label": "经验值"},
                    {"value": "artifact", "label": "图鉴"}
                ]
            }
        }
        
    except Exception as e:
        logger.error(f"获取类型列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取类型列表失败: {str(e)}")

@router.post("/batch-update", summary="批量更新奖励配置")
async def batch_update_configs(
    updates: List[Dict[str, Any]],
    db: AsyncSession = Depends(get_game_db)
):
    """批量更新奖励配置"""
    try:
        success_count = 0
        error_count = 0
        errors = []
        
        for update_item in updates:
            try:
                config_id = update_item.get("id")
                config_updates = update_item.get("updates", {})
                
                if not config_id:
                    errors.append("缺少配置ID")
                    error_count += 1
                    continue
                    
                success = await hotspot_reward_service.update_reward_config(db, config_id, config_updates)
                if success:
                    success_count += 1
                else:
                    errors.append(f"配置 {config_id} 不存在")
                    error_count += 1
                    
            except Exception as e:
                errors.append(f"配置 {config_id}: {str(e)}")
                error_count += 1
        
        return {
            "success": error_count == 0,
            "message": f"批量更新完成：成功 {success_count}，失败 {error_count}",
            "data": {
                "success_count": success_count,
                "error_count": error_count,
                "errors": errors
            }
        }
        
    except Exception as e:
        logger.error(f"批量更新失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量更新失败: {str(e)}")

@router.post("/test-probability", summary="测试概率配置")
async def test_probability(
    test_data: Dict[str, Any],
    db: AsyncSession = Depends(get_game_db)
):
    """测试概率配置效果"""
    try:
        hotspot_type = test_data.get("hotspot_type")
        test_count = test_data.get("test_count", 100)
        
        if not hotspot_type:
            raise HTTPException(status_code=400, detail="缺少热点类型")
            
        if test_count > 1000:
            raise HTTPException(status_code=400, detail="测试次数不能超过1000")
        
        # 获取配置
        configs = await hotspot_reward_service.get_hotspot_reward_configs(db, hotspot_type)
        
        if not configs:
            raise HTTPException(status_code=400, detail="该热点类型没有配置")
        
        # 模拟测试
        import random
        results = {
            "total_tests": test_count,
            "total_drops": 0,
            "drop_rate": 0.0,
            "reward_distribution": {},
            "config_performance": []
        }
        
        for config in configs:
            config_results = {
                "config_id": config.id,
                "reward_type": config.reward_type,
                "expected_rate": float(config.base_drop_rate),
                "actual_drops": 0,
                "actual_rate": 0.0
            }
            
            # 执行测试
            for _ in range(test_count):
                if random.random() <= config.base_drop_rate:
                    config_results["actual_drops"] += 1
                    
                    reward_type = config.reward_type
                    if reward_type not in results["reward_distribution"]:
                        results["reward_distribution"][reward_type] = 0
                    results["reward_distribution"][reward_type] += 1
                    results["total_drops"] += 1
            
            config_results["actual_rate"] = config_results["actual_drops"] / test_count
            results["config_performance"].append(config_results)
        
        results["drop_rate"] = results["total_drops"] / test_count if test_count > 0 else 0
        
        return {
            "success": True,
            "data": results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"概率测试失败: {e}")
        raise HTTPException(status_code=500, detail=f"概率测试失败: {str(e)}")

@router.get("/session-seeds", summary="获取会话随机种子信息")
async def get_session_seeds(
    user_id: Optional[int] = Query(None, description="用户ID筛选"),
    limit: int = Query(50, description="限制记录数"),
    db: AsyncSession = Depends(get_game_db)
):
    """获取会话随机种子信息"""
    try:
        query = select(SessionRandomSeed).order_by(desc(SessionRandomSeed.created_at))
        
        if user_id:
            query = query.where(SessionRandomSeed.user_id == user_id)
            
        query = query.limit(limit)
        
        result = await db.execute(query)
        seeds = result.scalars().all()
        
        return {
            "success": True,
            "data": [
                {
                    "session_id": seed.session_id,
                    "user_id": seed.user_id,
                    "random_seed": seed.random_seed,
                    "thief_collected_count": seed.thief_collected_count,
                    "garbage_collected_count": seed.garbage_collected_count,
                    "treasure_collected_count": seed.treasure_collected_count,
                    "boss_defeated_count": seed.boss_defeated_count,
                    "created_at": seed.created_at
                }
                for seed in seeds
            ]
        }
        
    except Exception as e:
        logger.error(f"获取会话种子失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话种子失败: {str(e)}")