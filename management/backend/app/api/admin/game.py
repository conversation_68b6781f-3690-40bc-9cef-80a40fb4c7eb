"""
游戏数据管理API端点
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc, update
from typing import List, Dict, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel

from app.core.database import get_game_db
from app.models.game_data import GameSession, GameUser, SessionStatus
from app.admin.deps import get_current_active_admin
from app.models.admin import AdminUser

router = APIRouter()

# Pydantic模型
class UpdateUserCurrencyRequest(BaseModel):
    user_id: str
    gold: Optional[int] = None
    diamond: Optional[int] = None
    operation: str = "set"  # set, add, subtract

class HotspotConfigRequest(BaseModel):
    city_id: str
    scene_id: str
    hotspot_data: Dict

class GameStatsResponse(BaseModel):
    total_sessions: int
    active_sessions: int
    total_users: int
    total_gold_earned: int
    total_diamond_earned: int
    avg_session_duration: float

@router.get("/sessions")
async def list_sessions(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    city_id: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_game_db),
    current_admin: AdminUser = Depends(get_current_active_admin)
):
    """获取游戏会话列表"""
    try:
        offset = (page - 1) * page_size
        
        # 构建查询条件
        conditions = []
        if city_id:
            conditions.append(GameSession.city_id == city_id)
        if status:
            conditions.append(GameSession.status == status)
        
        # 查询会话数据
        stmt = select(GameSession).order_by(GameSession.started_at.desc())
        if conditions:
            stmt = stmt.where(and_(*conditions))
        stmt = stmt.offset(offset).limit(page_size)
        
        result = await db.execute(stmt)
        sessions = result.scalars().all()
        
        # 查询总数
        count_stmt = select(func.count(GameSession.id))
        if conditions:
            count_stmt = count_stmt.where(and_(*conditions))
        count_result = await db.execute(count_stmt)
        total = count_result.scalar() or 0
        
        return {
            "sessions": [session.to_dict() for session in sessions],
            "total": total,
            "page": page,
            "page_size": page_size
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")

@router.get("/stats", response_model=GameStatsResponse)
async def get_game_stats(
    db: AsyncSession = Depends(get_game_db),
    current_admin: AdminUser = Depends(get_current_active_admin)
):
    """获取游戏统计数据"""
    try:
        # 总会话数
        total_sessions_stmt = select(func.count(GameSession.id))
        total_sessions_result = await db.execute(total_sessions_stmt)
        total_sessions = total_sessions_result.scalar() or 0
        
        # 活跃会话数
        active_sessions_stmt = select(func.count(GameSession.id)).where(
            GameSession.status == SessionStatus.ACTIVE
        )
        active_sessions_result = await db.execute(active_sessions_stmt)
        active_sessions = active_sessions_result.scalar() or 0
        
        # 总用户数
        total_users_stmt = select(func.count(GameUser.id)).where(
            GameUser.is_deleted == False
        )
        total_users_result = await db.execute(total_users_stmt)
        total_users = total_users_result.scalar() or 0
        
        # 总金币和钻石收益
        earnings_stmt = select(
            func.sum(GameSession.gold_earned).label("total_gold"),
            func.sum(GameSession.diamond_earned).label("total_diamond"),
            func.avg(GameSession.duration).label("avg_duration")
        )
        earnings_result = await db.execute(earnings_stmt)
        earnings = earnings_result.first()
        
        return GameStatsResponse(
            total_sessions=total_sessions,
            active_sessions=active_sessions,
            total_users=total_users,
            total_gold_earned=earnings.total_gold or 0,
            total_diamond_earned=earnings.total_diamond or 0,
            avg_session_duration=earnings.avg_duration or 0
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取游戏统计失败: {str(e)}")

@router.get("/users/{user_id}/collections")
async def get_user_collections(
    user_id: str,
    db: AsyncSession = Depends(get_game_db),
    current_admin: AdminUser = Depends(get_current_active_admin)
):
    """获取用户收集数据"""
    try:
        # 查询用户基本信息
        user_stmt = select(GameUser).where(GameUser.user_id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 查询用户游戏会话统计
        sessions_stmt = select(
            func.count(GameSession.id).label("total_sessions"),
            func.sum(GameSession.thieves_collected).label("total_thieves"),
            func.sum(GameSession.garbage_collected).label("total_garbage"),
            func.sum(GameSession.gold_earned).label("total_gold"),
            func.sum(GameSession.diamond_earned).label("total_diamond")
        ).where(GameSession.user_id == user.id)
        
        sessions_result = await db.execute(sessions_stmt)
        stats = sessions_result.first()
        
        return {
            "user_info": user.to_dict(),
            "collections": {
                "thieves": stats.total_thieves or 0,
                "garbage": stats.total_garbage or 0,
                "artifacts": 0,  # 待实现
                "treasures": 0   # 待实现
            },
            "stats": {
                "total_sessions": stats.total_sessions or 0,
                "total_gold": stats.total_gold or 0,
                "total_diamond": stats.total_diamond or 0
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户收集数据失败: {str(e)}")

# 🚫 PRD合规性清理：移除货币更新接口，PRD中没有金币钻石概念

@router.get("/cities")
async def get_cities(
    current_admin: AdminUser = Depends(get_current_active_admin)
):
    """获取城市列表"""
    cities = [
        {"id": "beijing", "name": "北京", "unlocked": True},
        {"id": "paris", "name": "巴黎", "unlocked": True},
        {"id": "newyork", "name": "纽约", "unlocked": True},
        {"id": "shanghai", "name": "上海", "unlocked": True},
        {"id": "prague", "name": "布拉格", "unlocked": True},
        {"id": "zurich", "name": "苏黎世", "unlocked": True},
        {"id": "iceland", "name": "冰岛", "unlocked": True},
        {"id": "venice", "name": "威尼斯", "unlocked": True}
    ]
    return {"cities": cities}

@router.get("/hotspots")
async def get_hotspots(
    city_id: str = Query("beijing"),
    scene_id: str = Query("scene_level_1"),
    current_admin: AdminUser = Depends(get_current_active_admin)
):
    """获取热点配置"""
    # 这里返回示例热点配置
    hotspots = [
        {
            "id": "thief_001",
            "type": "thief",
            "name": "小偷1",
            "position": {"x": 100, "y": 200},
            "reward": {"gold": 50, "exp": 10}
        },
        {
            "id": "garbage_001", 
            "type": "garbage",
            "name": "垃圾军团1",
            "position": {"x": 300, "y": 400},
            "reward": {"gold": 30, "exp": 5}
        }
    ]
    return {"hotspots": hotspots, "city_id": city_id, "scene_id": scene_id}

@router.post("/hotspots/update")
async def update_hotspots(
    request: HotspotConfigRequest,
    current_admin: AdminUser = Depends(get_current_active_admin)
):
    """更新热点配置"""
    # 这里应该实现热点配置的更新逻辑
    return {"success": True, "message": "热点配置更新成功"}

@router.delete("/sessions/{session_id}")
async def delete_session(
    session_id: str,
    db: AsyncSession = Depends(get_game_db),
    current_admin: AdminUser = Depends(get_current_active_admin)
):
    """删除游戏会话"""
    try:
        # 查询会话
        session_stmt = select(GameSession).where(GameSession.session_id == session_id)
        session_result = await db.execute(session_stmt)
        session = session_result.scalar_one_or_none()
        
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")
        
        # 删除会话
        await db.delete(session)
        await db.commit()
        
        return {"success": True, "message": "会话删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除会话失败: {str(e)}")

@router.get("/leaderboard")
async def get_leaderboard(
    limit: int = Query(50, ge=1, le=100),
    db: AsyncSession = Depends(get_game_db),
    current_admin: AdminUser = Depends(get_current_active_admin)
):
    """获取排行榜数据"""
    try:
        # 按等级和经验排序
        stmt = select(GameUser).where(
            GameUser.is_deleted == False
        ).order_by(
            desc(GameUser.level),
            desc(GameUser.exp)
        ).limit(limit)
        
        result = await db.execute(stmt)
        users = result.scalars().all()
        
        leaderboard = []
        for i, user in enumerate(users, 1):
            leaderboard.append({
                "rank": i,
                "user_id": user.user_id,
                "nickname": user.nickname,
                "level": user.level,
                "exp": user.exp,
                "gold": user.gold,
                "diamond": user.diamond,
                "total_play_time": user.total_play_time
            })
        
        return {"leaderboard": leaderboard, "total": len(leaderboard)}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取排行榜失败: {str(e)}") 