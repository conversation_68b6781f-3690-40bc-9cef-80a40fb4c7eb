<template>
  <div class="hotspot-management-enhanced">
    <div class="header-section">
      <h2>热点管理 - 增强版</h2>
      <div class="action-buttons">
        <el-button type="primary" @click="showUploadDialog = true">
          <el-icon><Upload /></el-icon>
          上传XML
        </el-button>
        <el-button type="success" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建热点
        </el-button>
        <el-button 
          type="warning" 
          :disabled="selectedHotspots.length === 0"
          @click="showBatchEditDialog = true"
        >
          <el-icon><Edit /></el-icon>
          批量编辑
        </el-button>
        <el-button 
          type="danger" 
          :disabled="selectedHotspots.length === 0"
          @click="handleBatchDelete"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
        <el-button type="info" @click="exportData">
          <el-icon><Download /></el-icon>
          导出XML
        </el-button>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="场景ID">
          <el-input 
            v-model="filters.scene_id" 
            placeholder="请输入场景ID"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="城市ID">
          <el-input 
            v-model="filters.city_id" 
            placeholder="请输入城市ID"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="热点类型">
          <el-select 
            v-model="filters.hotspot_type" 
            placeholder="选择热点类型"
            clearable
            style="width: 120px"
          >
            <el-option label="小偷" value="thief" />
            <el-option label="垃圾军团" value="garbage" />
            <el-option label="宝箱" value="treasure" />
            <el-option label="BOSS小偷" value="boss_thief" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否可见">
          <el-select 
            v-model="filters.visible" 
            placeholder="选择可见性"
            clearable
            style="width: 100px"
          >
            <el-option label="可见" :value="true" />
            <el-option label="隐藏" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否启用">
          <el-select 
            v-model="filters.enabled" 
            placeholder="选择启用状态"
            clearable
            style="width: 100px"
          >
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="有奖励">
          <el-select 
            v-model="filters.has_reward" 
            placeholder="选择奖励状态"
            clearable
            style="width: 100px"
          >
            <el-option label="有奖励" :value="true" />
            <el-option label="无奖励" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="奖励类型">
          <el-select 
            v-model="filters.reward_type" 
            placeholder="选择奖励类型"
            clearable
            style="width: 120px"
          >
            <el-option label="金币" value="gold" />
            <el-option label="钻石" value="diamond" />
            <el-option label="文物" value="artifact" />
            <el-option label="宝箱" value="treasure_box" />
          </el-select>
        </el-form-item>
        <el-form-item label="搜索">
          <el-input 
            v-model="filters.search" 
            placeholder="搜索热点名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadHotspots">搜索</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section" v-if="stats">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <span class="stat-label">总热点数</span>
              <span class="stat-value">{{ stats.total_count }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <span class="stat-label">可见热点</span>
              <span class="stat-value">{{ stats.visibility_stats?.visible || 0 }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <span class="stat-label">启用热点</span>
              <span class="stat-value">{{ stats.enabled_stats?.enabled || 0 }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <span class="stat-label">有奖励热点</span>
              <span class="stat-value">{{ getTotalRewardHotspots }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 热点列表 -->
    <div class="table-section">
      <el-table 
        :data="hotspots" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="scene_id" label="场景ID" width="120" />
        <el-table-column prop="city_id" label="城市ID" width="120" />
        <el-table-column prop="hotspot_name" label="热点名称" width="150" />
        <el-table-column prop="hotspot_type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.hotspot_type)">
              {{ getTypeName(row.hotspot_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="位置" width="150">
          <template #default="{ row }">
            {{ row.position_x }}, {{ row.position_y }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.visible" type="success" size="small">可见</el-tag>
            <el-tag v-else type="info" size="small">隐藏</el-tag>
            <el-tag v-if="row.enabled" type="success" size="small">启用</el-tag>
            <el-tag v-else type="danger" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="奖励配置" width="200">
          <template #default="{ row }">
            <div v-if="row.has_reward">
              <el-tag :type="getRewardTypeTagType(row.reward_type)" size="small">
                {{ getRewardTypeName(row.reward_type) }}
              </el-tag>
              <span class="reward-range">
                {{ row.reward_min === row.reward_max ? row.reward_min : `${row.reward_min}-${row.reward_max}` }}
              </span>
            </div>
            <el-tag v-else type="info" size="small">无奖励</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editHotspot(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteHotspot(row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadHotspots"
          @current-change="loadHotspots"
        />
      </div>
    </div>

    <!-- XML上传对话框 -->
    <el-dialog v-model="showUploadDialog" title="上传XML文件" width="600px">
      <el-form :model="uploadForm" label-width="100px">
        <el-form-item label="场景ID" required>
          <el-input v-model="uploadForm.scene_id" placeholder="请输入场景ID" />
        </el-form-item>
        <el-form-item label="城市ID" required>
          <el-input v-model="uploadForm.city_id" placeholder="请输入城市ID" />
        </el-form-item>
        <el-form-item label="热点类型" required>
          <el-select v-model="uploadForm.hotspot_type" placeholder="选择要上传的热点类型">
            <el-option label="小偷" value="thief">
              <span>小偷 - 收集获得金币</span>
            </el-option>
            <el-option label="垃圾军团" value="garbage">
              <span>垃圾军团 - 收集获得金币和弹药</span>
            </el-option>
            <el-option label="宝箱" value="treasure">
              <span>宝箱 - 收集获得金币和道具</span>
            </el-option>
            <el-option label="BOSS小偷" value="boss_thief">
              <span>BOSS小偷 - 攻击获得钻石奖励</span>
            </el-option>
            <el-option label="混合类型（自动检测）" value="auto">
              <span>混合类型 - 系统自动识别热点类型</span>
            </el-option>
          </el-select>
          <div class="form-tip">
            <el-text size="small" type="info">
              选择特定类型只会上传该类型的热点，混合类型会自动识别所有热点类型
            </el-text>
          </div>
        </el-form-item>
        <el-form-item label="上传模式">
          <el-radio-group v-model="uploadForm.incremental">
            <el-radio :label="true">增量上传（保留现有数据）</el-radio>
            <el-radio :label="false">全量上传（替换现有数据）</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="XML文件" required>
          <el-upload
            ref="uploadRef"
            :limit="1"
            :auto-upload="false"
            :on-change="handleFileChange"
            :file-list="fileList"
            accept=".xml"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">只能上传XML文件，支持拖拽上传</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showUploadDialog = false">取消</el-button>
          <el-button type="primary" @click="handleUpload" :loading="uploading">
            上传
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 创建/编辑热点对话框 -->
    <el-dialog 
      v-model="showCreateDialog" 
      :title="editingHotspot ? '编辑热点' : '创建热点'" 
      width="800px"
    >
      <el-form :model="hotspotForm" :rules="hotspotRules" ref="hotspotFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="场景ID" prop="scene_id">
              <el-input v-model="hotspotForm.scene_id" placeholder="请输入场景ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="城市ID" prop="city_id">
              <el-input v-model="hotspotForm.city_id" placeholder="请输入城市ID" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="热点名称" prop="hotspot_name">
              <el-input v-model="hotspotForm.hotspot_name" placeholder="请输入热点名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="热点类型" prop="hotspot_type">
              <el-select v-model="hotspotForm.hotspot_type" placeholder="选择热点类型">
                <el-option label="小偷" value="thief" />
                <el-option label="垃圾军团" value="garbage" />
                <el-option label="宝箱" value="treasure" />
                <el-option label="BOSS小偷" value="boss_thief" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="X坐标" prop="position_x">
              <el-input-number v-model="hotspotForm.position_x" :precision="6" :step="0.1" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Y坐标" prop="position_y">
              <el-input-number v-model="hotspotForm.position_y" :precision="6" :step="0.1" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="缩放比例" prop="scale">
              <el-input-number v-model="hotspotForm.scale" :precision="2" :step="0.1" :min="0.1" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="图片URL">
          <el-input v-model="hotspotForm.image_url" placeholder="请输入图片URL" />
        </el-form-item>
        <el-form-item label="点击事件">
          <el-input v-model="hotspotForm.onclick_action" placeholder="请输入点击事件" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否可见">
              <el-switch v-model="hotspotForm.visible" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否启用">
              <el-switch v-model="hotspotForm.enabled" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="是否有奖励">
          <el-switch v-model="hotspotForm.has_reward" />
        </el-form-item>
        <div v-if="hotspotForm.has_reward">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="奖励类型" prop="reward_type">
                <el-select v-model="hotspotForm.reward_type" placeholder="选择奖励类型">
                  <el-option label="金币" value="gold" />
                  <el-option label="钻石" value="diamond" />
                  <el-option label="文物" value="artifact" />
                  <el-option label="宝箱" value="treasure_box" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最小奖励" prop="reward_min">
                <el-input-number v-model="hotspotForm.reward_min" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最大奖励" prop="reward_max">
                <el-input-number v-model="hotspotForm.reward_max" :min="0" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <el-form-item label="描述">
          <el-input 
            v-model="hotspotForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入热点描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveHotspot" :loading="saving">
            {{ editingHotspot ? '保存' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量编辑对话框 -->
    <el-dialog v-model="showBatchEditDialog" title="批量编辑热点" width="600px">
      <el-form :model="batchEditForm" label-width="120px">
        <el-form-item label="是否可见">
          <el-select v-model="batchEditForm.visible" placeholder="选择可见性" clearable>
            <el-option label="可见" :value="true" />
            <el-option label="隐藏" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否启用">
          <el-select v-model="batchEditForm.enabled" placeholder="选择启用状态" clearable>
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否有奖励">
          <el-select v-model="batchEditForm.has_reward" placeholder="选择奖励状态" clearable>
            <el-option label="有奖励" :value="true" />
            <el-option label="无奖励" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="奖励类型">
          <el-select v-model="batchEditForm.reward_type" placeholder="选择奖励类型" clearable>
            <el-option label="金币" value="gold" />
            <el-option label="钻石" value="diamond" />
            <el-option label="文物" value="artifact" />
            <el-option label="宝箱" value="treasure_box" />
          </el-select>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="最小奖励">
              <el-input-number v-model="batchEditForm.reward_min" :min="0" placeholder="不修改请留空" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大奖励">
              <el-input-number v-model="batchEditForm.reward_max" :min="0" placeholder="不修改请留空" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showBatchEditDialog = false">取消</el-button>
          <el-button type="primary" @click="handleBatchEdit" :loading="batchEditing">
            应用修改 ({{ selectedHotspots.length }}个热点)
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Plus, Edit, Delete, Download } from '@element-plus/icons-vue'
import { hotspotsApi } from '@/api/hotspots'

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const saving = ref(false)
const batchEditing = ref(false)
const hotspots = ref([])
const selectedHotspots = ref([])
const stats = ref(null)

// 对话框状态
const showUploadDialog = ref(false)
const showCreateDialog = ref(false)
const showBatchEditDialog = ref(false)
const editingHotspot = ref(null)

// 筛选条件
const filters = reactive({
  scene_id: '',
  city_id: '',
  hotspot_type: '',
  visible: null,
  enabled: null,
  has_reward: null,
  reward_type: '',
  search: ''
})

// 分页参数
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0,
  total_pages: 0
})

// 上传表单
const uploadForm = reactive({
  scene_id: '',
  city_id: '',
  hotspot_type: 'auto',
  incremental: true
})

// 热点表单
const hotspotForm = reactive({
  scene_id: '',
  city_id: '',
  hotspot_name: '',
  hotspot_type: '',
  position_x: 0,
  position_y: 0,
  scale: 1.0,
  image_url: '',
  onclick_action: '',
  visible: true,
  enabled: true,
  has_reward: true,
  reward_type: 'gold',
  reward_min: 10,
  reward_max: 10,
  description: ''
})

// 批量编辑表单
const batchEditForm = reactive({
  visible: null,
  enabled: null,
  has_reward: null,
  reward_type: '',
  reward_min: null,
  reward_max: null
})

// 表单验证规则
const hotspotRules = {
  scene_id: [{ required: true, message: '请输入场景ID', trigger: 'blur' }],
  city_id: [{ required: true, message: '请输入城市ID', trigger: 'blur' }],
  hotspot_name: [{ required: true, message: '请输入热点名称', trigger: 'blur' }],
  hotspot_type: [{ required: true, message: '请选择热点类型', trigger: 'change' }],
  position_x: [{ required: true, message: '请输入X坐标', trigger: 'blur' }],
  position_y: [{ required: true, message: '请输入Y坐标', trigger: 'blur' }],
  reward_type: [{ required: true, message: '请选择奖励类型', trigger: 'change' }]
}

// 计算属性
const getTotalRewardHotspots = computed(() => {
  if (!stats.value?.reward_stats) return 0
  const rewardStats = stats.value.reward_stats
  return Object.keys(rewardStats).reduce((total, key) => {
    if (key !== 'no_reward') {
      return total + (rewardStats[key] || 0)
    }
    return total
  }, 0)
})

// 方法
const loadHotspots = async () => {
  loading.value = true
  try {
    const params = {
      ...filters,
      page: pagination.page,
      page_size: pagination.page_size
    }
    
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null) {
        delete params[key]
      }
    })

    const response = await hotspotsApi.getHotspots(params)
    if (response.data.success) {
      hotspots.value = response.data.data.hotspots
      Object.assign(pagination, response.data.data.pagination)
    }
  } catch (error) {
    ElMessage.error('加载热点列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const params = {}
    if (filters.scene_id) params.scene_id = filters.scene_id
    if (filters.city_id) params.city_id = filters.city_id

    const response = await hotspotsApi.getStats(params)
    if (response.data.success) {
      stats.value = response.data.data
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = key === 'visible' || key === 'enabled' || key === 'has_reward' ? null : ''
  })
  pagination.page = 1
  loadHotspots()
  loadStats()
}

const handleSelectionChange = (selection) => {
  selectedHotspots.value = selection
}

const getTypeTagType = (type) => {
  const typeMap = {
    'thief': 'warning',
    'garbage': 'danger',
    'treasure': 'success',
    'boss_thief': 'danger'
  }
  return typeMap[type] || 'info'
}

const getTypeName = (type) => {
  const nameMap = {
    'thief': '小偷',
    'garbage': '垃圾军团',
    'treasure': '宝箱',
    'boss_thief': 'BOSS小偷'
  }
  return nameMap[type] || type
}

const getRewardTypeTagType = (type) => {
  const typeMap = {
    'experience': 'warning',
    'artifact': 'success',
    'treasure_box': 'info'
  }
  return typeMap[type] || 'info'
}

const getRewardTypeName = (type) => {
  const nameMap = {
    'experience': '经验值',
    'artifact': '文物',
    'treasure_box': '宝箱'
  }
  return nameMap[type] || type
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

const handleUpload = async () => {
  console.log('Starting upload process') // 调试日志
  console.log('Current fileList:', fileList.value) // 调试日志
  
  if (fileList.value.length === 0) {
    ElMessage.warning('请选择要上传的XML文件')
    return
  }

  if (!uploadForm.scene_id || !uploadForm.city_id) {
    ElMessage.warning('请填写场景ID和城市ID')
    return
  }

  if (!uploadForm.hotspot_type) {
    ElMessage.warning('请选择热点类型')
    return
  }

  uploading.value = true
  try {
    // 获取文件，尝试不同的属性
    const fileItem = fileList.value[0]
    const file = fileItem.raw || fileItem.file || fileItem
    
    if (!file) {
      throw new Error('无法获取上传文件')
    }

    console.log('File to upload:', file) // 调试日志

    const formData = new FormData()
    formData.append('file', file)
    formData.append('scene_id', uploadForm.scene_id)
    formData.append('city_id', uploadForm.city_id)
    formData.append('hotspot_type', uploadForm.hotspot_type)
    formData.append('auto_save', 'true')
    formData.append('incremental', uploadForm.incremental.toString())

    const response = await hotspotsApi.uploadXML(formData)
    if (response.data.success) {
      const typeLabel = uploadForm.hotspot_type === 'auto' ? '混合类型' : getTypeName(uploadForm.hotspot_type)
      let message = `${typeLabel}热点XML上传成功！`
      
      if (response.data.data.filtered_type !== 'auto') {
        message += `筛选了 ${response.data.data.filtered_count} 个${typeLabel}热点，`
      }
      
      message += `${uploadForm.incremental ? '增量' : '全量'}保存了 ${response.data.data.saved_count} 个热点`
      
      if (response.data.data.updated_count > 0) {
        message += `，更新了 ${response.data.data.updated_count} 个现有热点`
      }
      
      ElMessage.success(message)
      showUploadDialog.value = false
      resetUploadForm()
      loadHotspots()
      loadStats()
    }
  } catch (error) {
    ElMessage.error(`上传失败: ${error.message || error}`)
    console.error('Upload error:', error)
  } finally {
    uploading.value = false
  }
}

const resetUploadForm = () => {
  uploadForm.scene_id = ''
  uploadForm.city_id = ''
  uploadForm.hotspot_type = 'auto'
  uploadForm.incremental = true
  
  // 清理响应式文件列表
  fileList.value = []
  
  // 安全地清理上传文件
  try {
    if (uploadRef.value && typeof uploadRef.value.clearFiles === 'function') {
      uploadRef.value.clearFiles()
    }
  } catch (error) {
    console.warn('清理上传文件失败:', error)
  }
}

const editHotspot = (hotspot) => {
  editingHotspot.value = hotspot
  Object.assign(hotspotForm, hotspot)
  showCreateDialog.value = true
}

const saveHotspot = async () => {
  const formRef = hotspotFormRef.value
  if (!formRef) return

  try {
    await formRef.validate()
  } catch (error) {
    return
  }

  saving.value = true
  try {
    const data = { ...hotspotForm }
    
    let response
    if (editingHotspot.value) {
      response = await hotspotsApi.updateHotspot(editingHotspot.value.id, data)
    } else {
      response = await hotspotsApi.createHotspot(data)
    }

    if (response.data.success) {
      ElMessage.success(editingHotspot.value ? '热点更新成功' : '热点创建成功')
      showCreateDialog.value = false
      resetHotspotForm()
      loadHotspots()
      loadStats()
    }
  } catch (error) {
    ElMessage.error(editingHotspot.value ? '更新失败' : '创建失败')
    console.error(error)
  } finally {
    saving.value = false
  }
}

const resetHotspotForm = () => {
  editingHotspot.value = null
  Object.assign(hotspotForm, {
    scene_id: '',
    city_id: '',
    hotspot_name: '',
    hotspot_type: '',
    position_x: 0,
    position_y: 0,
    scale: 1.0,
    image_url: '',
    onclick_action: '',
    visible: true,
    enabled: true,
    has_reward: true,
    reward_type: 'gold',
    reward_min: 10,
    reward_max: 10,
    description: ''
  })
}

const deleteHotspot = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这个热点吗？', '确认删除', {
      type: 'warning'
    })

    const response = await hotspotsApi.deleteHotspot(id)
    if (response.data.success) {
      ElMessage.success('删除成功')
      loadHotspots()
      loadStats()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  }
}

const handleBatchEdit = async () => {
  if (selectedHotspots.value.length === 0) {
    ElMessage.warning('请选择要编辑的热点')
    return
  }

  const updateData = {}
  Object.keys(batchEditForm).forEach(key => {
    if (batchEditForm[key] !== null && batchEditForm[key] !== '') {
      updateData[key] = batchEditForm[key]
    }
  })

  if (Object.keys(updateData).length === 0) {
    ElMessage.warning('请至少选择一个要修改的字段')
    return
  }

  batchEditing.value = true
  try {
    const hotspot_ids = selectedHotspots.value.map(h => h.id)
    const response = await hotspotsApi.batchUpdate({
      hotspot_ids,
      update_data: updateData
    })

    if (response.data.success) {
      ElMessage.success(`成功更新 ${response.data.data.updated_count} 个热点`)
      showBatchEditDialog.value = false
      resetBatchEditForm()
      loadHotspots()
      loadStats()
    }
  } catch (error) {
    ElMessage.error('批量编辑失败')
    console.error(error)
  } finally {
    batchEditing.value = false
  }
}

const resetBatchEditForm = () => {
  Object.keys(batchEditForm).forEach(key => {
    batchEditForm[key] = key === 'reward_min' || key === 'reward_max' ? null : (
      key === 'visible' || key === 'enabled' || key === 'has_reward' ? null : ''
    )
  })
}

const handleBatchDelete = async () => {
  if (selectedHotspots.value.length === 0) {
    ElMessage.warning('请选择要删除的热点')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedHotspots.value.length} 个热点吗？`,
      '确认批量删除',
      { type: 'warning' }
    )

    const hotspot_ids = selectedHotspots.value.map(h => h.id)
    const response = await hotspotsApi.batchDelete({ hotspot_ids })

    if (response.data.success) {
      ElMessage.success(`成功删除 ${response.data.data.deleted_count} 个热点`)
      loadHotspots()
      loadStats()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
      console.error(error)
    }
  }
}

const exportData = async () => {
  try {
    const params = { ...filters }
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null) {
        delete params[key]
      }
    })

    if (!params.scene_id) {
      ElMessage.warning('导出XML需要指定场景ID')
      return
    }

    const response = await hotspotsApi.exportXML(params.scene_id, params)
    if (response.data.success) {
      // 创建下载链接
      const blob = new Blob([response.data.xml_content], { type: 'text/xml' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `hotspots_${params.scene_id}_${Date.now()}.xml`
      link.click()
      window.URL.revokeObjectURL(url)
      
      ElMessage.success(`成功导出 ${response.data.hotspot_count} 个热点`)
    }
  } catch (error) {
    ElMessage.error('导出失败')
    console.error(error)
  }
}

// 生命周期
onMounted(() => {
  loadHotspots()
  loadStats()
})

// 上传文件列表
const fileList = ref([])

// 模板引用
const uploadRef = ref(null)
const hotspotFormRef = ref(null)

// 文件变化处理
const handleFileChange = (file, fileListParam) => {
  console.log('File changed:', file, fileListParam)
  fileList.value = fileListParam
}
</script>

<style scoped>
.hotspot-management-enhanced {
  padding: 20px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.table-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

.reward-range {
  margin-left: 8px;
  font-weight: bold;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.form-tip {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 4px;
}

.form-tip .el-text {
  line-height: 1.4;
}
</style>