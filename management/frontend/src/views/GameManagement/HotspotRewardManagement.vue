<template>
  <div class="hotspot-reward-management">
    <div class="header">
      <h2>热点奖励配置管理</h2>
      <p>管理游戏中各类热点的奖励概率和数量配置</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            添加配置
          </el-button>
          <el-button @click="loadConfigs">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="showTestDialog = true">
            <el-icon><DataAnalysis /></el-icon>
            概率测试
          </el-button>
        </el-col>
        <el-col :span="12">
          <div class="filter-group">
            <el-select v-model="filter.hotspot_type" placeholder="热点类型" clearable @change="loadConfigs">
              <el-option v-for="type in hotspotTypes" :key="type.value" :label="type.label" :value="type.value" />
            </el-select>
            <el-select v-model="filter.reward_type" placeholder="奖励类型" clearable @change="loadConfigs">
              <el-option v-for="type in rewardTypes" :key="type.value" :label="type.label" :value="type.value" />
            </el-select>
            <el-select v-model="filter.is_enabled" placeholder="启用状态" clearable @change="loadConfigs">
              <el-option label="启用" :value="true" />
              <el-option label="禁用" :value="false" />
            </el-select>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 配置列表 -->
    <el-card class="config-list">
      <el-table :data="configs" stripe style="width: 100%" v-loading="loading">
        <el-table-column prop="hotspot_type" label="热点类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getHotspotTypeTagType(row.hotspot_type)">
              {{ getHotspotTypeLabel(row.hotspot_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="reward_type" label="奖励类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getRewardTypeTagType(row.reward_type)">
              {{ getRewardTypeLabel(row.reward_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="base_drop_rate" label="掉落概率" width="150">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.base_drop_rate * 100" 
              :format="() => (row.base_drop_rate * 100).toFixed(1) + '%'"
              :stroke-width="16"
              text-inside
            />
          </template>
        </el-table-column>
        
        <el-table-column label="奖励数量" width="150">
          <template #default="{ row }">
            <span>{{ row.min_amount }} - {{ row.max_amount }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="weight" label="权重" width="80" />
        
        <el-table-column prop="is_enabled" label="状态" width="80">
          <template #default="{ row }">
            <el-switch 
              v-model="row.is_enabled" 
              @change="updateConfigStatus(row)"
              :loading="row.updating"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editConfig(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteConfig(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 统计数据 -->
    <el-row :gutter="20" class="statistics">
      <el-col :span="8">
        <el-card>
          <div class="stat-item">
            <h3>总配置数</h3>
            <div class="stat-value">{{ configs.length }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <div class="stat-item">
            <h3>启用配置</h3>
            <div class="stat-value">{{ configs.filter(c => c.is_enabled).length }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <div class="stat-item">
            <h3>平均掉落率</h3>
            <div class="stat-value">
              {{ configs.length > 0 ? (configs.reduce((sum, c) => sum + c.base_drop_rate, 0) / configs.length * 100).toFixed(1) + '%' : '0%' }}
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 创建/编辑对话框 -->
    <el-dialog 
      :title="editingConfig ? '编辑奖励配置' : '创建奖励配置'" 
      v-model="showCreateDialog"
      width="600px"
    >
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <el-form-item label="热点类型" prop="hotspot_type">
          <el-select v-model="formData.hotspot_type" placeholder="选择热点类型">
            <el-option v-for="type in hotspotTypes" :key="type.value" :label="type.label" :value="type.value" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="奖励类型" prop="reward_type">
          <el-select v-model="formData.reward_type" placeholder="选择奖励类型">
            <el-option v-for="type in rewardTypes" :key="type.value" :label="type.label" :value="type.value" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="掉落概率" prop="base_drop_rate">
          <el-slider 
            v-model="formData.base_drop_rate" 
            :min="0" 
            :max="1" 
            :step="0.01" 
            show-input
            :format-tooltip="(val) => (val * 100).toFixed(1) + '%'"
          />
        </el-form-item>
        
        <el-form-item label="最小数量" prop="min_amount">
          <el-input-number v-model="formData.min_amount" :min="1" :max="1000" />
        </el-form-item>
        
        <el-form-item label="最大数量" prop="max_amount">
          <el-input-number v-model="formData.max_amount" :min="1" :max="1000" />
        </el-form-item>
        
        <el-form-item label="权重" prop="weight">
          <el-input-number v-model="formData.weight" :min="1" :max="1000" />
        </el-form-item>
        
        <el-form-item label="启用状态">
          <el-switch v-model="formData.is_enabled" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveConfig" :loading="saving">
          {{ editingConfig ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 概率测试对话框 -->
    <el-dialog title="概率测试" v-model="showTestDialog" width="800px">
      <el-form inline>
        <el-form-item label="热点类型">
          <el-select v-model="testForm.hotspot_type" placeholder="选择热点类型">
            <el-option v-for="type in hotspotTypes" :key="type.value" :label="type.label" :value="type.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="测试次数">
          <el-input-number v-model="testForm.test_count" :min="100" :max="1000" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="runProbabilityTest" :loading="testing">
            运行测试
          </el-button>
        </el-form-item>
      </el-form>

      <div v-if="testResults" class="test-results">
        <h4>测试结果</h4>
        <el-descriptions border :column="2">
          <el-descriptions-item label="总测试次数">{{ testResults.total_tests }}</el-descriptions-item>
          <el-descriptions-item label="总掉落次数">{{ testResults.total_drops }}</el-descriptions-item>
          <el-descriptions-item label="总掉落率">{{ (testResults.drop_rate * 100).toFixed(2) }}%</el-descriptions-item>
        </el-descriptions>

        <h5>各配置表现</h5>
        <el-table :data="testResults.config_performance" size="small">
          <el-table-column prop="reward_type" label="奖励类型" />
          <el-table-column prop="expected_rate" label="期望概率">
            <template #default="{ row }">
              {{ (row.expected_rate * 100).toFixed(1) }}%
            </template>
          </el-table-column>
          <el-table-column prop="actual_rate" label="实际概率">
            <template #default="{ row }">
              {{ (row.actual_rate * 100).toFixed(1) }}%
            </template>
          </el-table-column>
          <el-table-column prop="actual_drops" label="实际掉落" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, DataAnalysis } from '@element-plus/icons-vue'
import api from '@/utils/request'

// 响应式数据
const configs = ref([])
const loading = ref(false)
const saving = ref(false)
const testing = ref(false)
const showCreateDialog = ref(false)
const showTestDialog = ref(false)
const editingConfig = ref(null)
const testResults = ref(null)

// 筛选条件
const filter = reactive({
  hotspot_type: '',
  reward_type: '',
  is_enabled: null
})

// 表单数据
const formData = reactive({
  hotspot_type: '',
  reward_type: '',
  base_drop_rate: 0.5,
  min_amount: 1,
  max_amount: 10,
  weight: 100,
  is_enabled: true
})

// 测试表单
const testForm = reactive({
  hotspot_type: '',
  test_count: 100
})

// 类型选项
const hotspotTypes = ref([])
const rewardTypes = ref([])

// 表单验证规则
const formRules = {
  hotspot_type: [{ required: true, message: '请选择热点类型', trigger: 'change' }],
  reward_type: [{ required: true, message: '请选择奖励类型', trigger: 'change' }],
  base_drop_rate: [{ required: true, message: '请设置掉落概率', trigger: 'blur' }],
  min_amount: [{ required: true, message: '请设置最小数量', trigger: 'blur' }],
  max_amount: [{ required: true, message: '请设置最大数量', trigger: 'blur' }],
  weight: [{ required: true, message: '请设置权重', trigger: 'blur' }]
}

const formRef = ref(null)

// 加载配置列表
const loadConfigs = async () => {
  try {
    loading.value = true
    const params = {}
    if (filter.hotspot_type) params.hotspot_type = filter.hotspot_type
    if (filter.reward_type) params.reward_type = filter.reward_type
    if (filter.is_enabled !== null) params.is_enabled = filter.is_enabled

    const response = await api.get('/hotspot-reward/configs', { params })
    configs.value = response.data.data
  } catch (error) {
    ElMessage.error('加载配置失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

// 加载类型选项
const loadTypes = async () => {
  try {
    const response = await api.get('/hotspot-reward/types')
    hotspotTypes.value = response.data.data.hotspot_types
    rewardTypes.value = response.data.data.reward_types
  } catch (error) {
    ElMessage.error('加载类型失败: ' + (error.response?.data?.detail || error.message))
  }
}

// 编辑配置
const editConfig = (config) => {
  editingConfig.value = config
  Object.assign(formData, config)
  showCreateDialog.value = true
}

// 保存配置
const saveConfig = async () => {
  try {
    await formRef.value.validate()
    saving.value = true

    if (editingConfig.value) {
      // 更新
      await api.put(`/hotspot-reward/configs/${editingConfig.value.id}`, formData)
      ElMessage.success('配置更新成功')
    } else {
      // 创建
      await api.post('/hotspot-reward/configs', formData)
      ElMessage.success('配置创建成功')
    }

    showCreateDialog.value = false
    resetForm()
    loadConfigs()
  } catch (error) {
    ElMessage.error('保存失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    saving.value = false
  }
}

// 更新配置状态
const updateConfigStatus = async (config) => {
  try {
    config.updating = true
    await api.put(`/hotspot-reward/configs/${config.id}`, {
      is_enabled: config.is_enabled
    })
    ElMessage.success('状态更新成功')
  } catch (error) {
    config.is_enabled = !config.is_enabled // 回滚状态
    ElMessage.error('更新失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    config.updating = false
  }
}

// 删除配置
const deleteConfig = async (config) => {
  try {
    await ElMessageBox.confirm('确定要删除这个配置吗？', '确认删除', {
      type: 'warning'
    })

    await api.delete(`/hotspot-reward/configs/${config.id}`)
    ElMessage.success('删除成功')
    loadConfigs()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

// 运行概率测试
const runProbabilityTest = async () => {
  if (!testForm.hotspot_type) {
    ElMessage.warning('请选择热点类型')
    return
  }

  try {
    testing.value = true
    const response = await api.post('/hotspot-reward/test-probability', testForm)
    testResults.value = response.data.data
    ElMessage.success('测试完成')
  } catch (error) {
    ElMessage.error('测试失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    testing.value = false
  }
}

// 重置表单
const resetForm = () => {
  editingConfig.value = null
  Object.assign(formData, {
    hotspot_type: '',
    reward_type: '',
    base_drop_rate: 0.5,
    min_amount: 1,
    max_amount: 10,
    weight: 100,
    is_enabled: true
  })
}

// 获取标签类型
const getHotspotTypeTagType = (type) => {
  const types = {
    thief: 'danger',
    garbage: 'warning',
    treasure_box: 'success',
    boss: 'danger'
  }
  return types[type] || 'info'
}

const getRewardTypeTagType = (type) => {
  const types = {
    experience: 'warning',
    artifact: 'success'
  }
  return types[type] || 'info'
}

const getHotspotTypeLabel = (type) => {
  const item = hotspotTypes.value.find(t => t.value === type)
  return item ? item.label : type
}

const getRewardTypeLabel = (type) => {
  const item = rewardTypes.value.find(t => t.value === type)
  return item ? item.label : type
}

// 生命周期
onMounted(() => {
  loadTypes()
  loadConfigs()
})
</script>

<style scoped>
.hotspot-reward-management {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}

.header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.toolbar {
  margin-bottom: 20px;
}

.filter-group {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.filter-group .el-select {
  width: 120px;
}

.config-list {
  margin-bottom: 20px;
}

.statistics {
  margin-top: 20px;
}

.stat-item {
  text-align: center;
}

.stat-item h3 {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
  font-weight: normal;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
}

.test-results {
  margin-top: 20px;
}

.test-results h4,
.test-results h5 {
  margin: 15px 0 10px 0;
  color: #303133;
}
</style>