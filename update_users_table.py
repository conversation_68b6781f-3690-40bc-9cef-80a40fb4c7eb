#!/usr/bin/env python3
"""
更新用户表结构，添加PRD系统字段
"""
import asyncio
import logging
import sys
from pathlib import Path

# 添加backend路径到Python path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

from app.core.database import db_manager
from sqlalchemy import text

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def update_users_table():
    """更新users表结构"""
    try:
        # 初始化数据库连接
        await db_manager.initialize()
        
        # 定义需要添加的字段的SQL语句（MySQL兼容格式）
        alter_sql_statements = [
            # 守护者系统字段
            "ALTER TABLE users ADD COLUMN guardian_level INT DEFAULT 1 COMMENT '守护者等级'",
            "ALTER TABLE users ADD COLUMN guardian_exp INT DEFAULT 0 COMMENT '守护者经验值'",
            "ALTER TABLE users ADD COLUMN total_exp INT DEFAULT 0 COMMENT '累计经验值'",
            
            # 体力系统字段
            "ALTER TABLE users ADD COLUMN stamina INT DEFAULT 120 COMMENT '当前体力值'",
            "ALTER TABLE users ADD COLUMN max_stamina INT DEFAULT 120 COMMENT '最大体力值'",
            "ALTER TABLE users ADD COLUMN last_stamina_update DATETIME DEFAULT NOW() COMMENT '最后体力更新时间'",
            "ALTER TABLE users ADD COLUMN stamina_ad_count INT DEFAULT 0 COMMENT '体力广告观看次数'",
            "ALTER TABLE users ADD COLUMN last_stamina_ad_time DATETIME COMMENT '最后观看体力广告时间'",
            
            # 道具系统字段
            "ALTER TABLE users ADD COLUMN magnifier_count INT DEFAULT 0 COMMENT '放大镜数量'",
            "ALTER TABLE users ADD COLUMN radar_count INT DEFAULT 0 COMMENT '雷达数量'",
            "ALTER TABLE users ADD COLUMN stamina_potion_count INT DEFAULT 0 COMMENT '体力药水数量'",
            
            # PRD统计字段
            "ALTER TABLE users ADD COLUMN total_thieves_caught INT DEFAULT 0 COMMENT '累积抓捕小偷数量'",
            "ALTER TABLE users ADD COLUMN total_rubbish_cleaned INT DEFAULT 0 COMMENT '累积清理垃圾数量'",
            "ALTER TABLE users ADD COLUMN total_monuments_restored INT DEFAULT 0 COMMENT '累积修复古迹数量'",
            "ALTER TABLE users ADD COLUMN total_quiz_answered INT DEFAULT 0 COMMENT '累积回答问题数量'",
            "ALTER TABLE users ADD COLUMN total_quiz_correct INT DEFAULT 0 COMMENT '累积正确回答数量'",
            
            # 宝箱统计字段
            "ALTER TABLE users ADD COLUMN treasure_boxes_opened INT DEFAULT 0 COMMENT '开启宝箱总数'",
            "ALTER TABLE users ADD COLUMN copper_boxes_opened INT DEFAULT 0 COMMENT '铜宝箱开启数'",
            "ALTER TABLE users ADD COLUMN silver_boxes_opened INT DEFAULT 0 COMMENT '银宝箱开启数'",
            "ALTER TABLE users ADD COLUMN gold_boxes_opened INT DEFAULT 0 COMMENT '金宝箱开启数'",
            
            # 广告统计字段
            "ALTER TABLE users ADD COLUMN total_ads_watched INT DEFAULT 0 COMMENT '观看广告总次数'",
            "ALTER TABLE users ADD COLUMN stamina_ads_watched INT DEFAULT 0 COMMENT '体力恢复广告次数'",
            "ALTER TABLE users ADD COLUMN reward_ads_watched INT DEFAULT 0 COMMENT '奖励翻倍广告次数'",
            
            # 被动收益字段
            "ALTER TABLE users ADD COLUMN offline_income_rate INT DEFAULT 2 COMMENT '离线收益速率(经验/分钟)'",
            "ALTER TABLE users ADD COLUMN last_offline_collection DATETIME COMMENT '最后收集离线收益时间'",
            
            # VIP系统字段
            "ALTER TABLE users ADD COLUMN vip_level INT DEFAULT 0 COMMENT 'VIP等级'",
            "ALTER TABLE users ADD COLUMN level INT DEFAULT 1 COMMENT '用户等级(兼容旧系统)'",
            "ALTER TABLE users ADD COLUMN exp INT DEFAULT 0 COMMENT '用户经验(兼容旧系统)'"
        ]
        
        # 创建索引的SQL语句
        index_sql_statements = [
            "CREATE INDEX idx_guardian_level ON users(guardian_level)",
            "CREATE INDEX idx_user_id_idx ON users(user_id)",
            "CREATE INDEX idx_login_type_idx ON users(login_type)"
        ]
        
        async with db_manager.session() as db:
            try:
                # 添加列（忽略已存在的列错误）
                for sql_statement in alter_sql_statements:
                    try:
                        logger.info(f"执行SQL: {sql_statement[:80]}...")
                        await db.execute(text(sql_statement))
                        logger.info("✅ 成功")
                    except Exception as e:
                        if "Duplicate column name" in str(e) or "already exists" in str(e):
                            logger.info(f"⚠️ 列已存在，跳过")
                        else:
                            logger.error(f"❌ 执行失败: {e}")
                            
                # 创建索引（忽略已存在的索引错误）
                for sql_statement in index_sql_statements:
                    try:
                        logger.info(f"创建索引: {sql_statement[:80]}...")
                        await db.execute(text(sql_statement))
                        logger.info("✅ 索引创建成功")
                    except Exception as e:
                        if "Duplicate key name" in str(e) or "already exists" in str(e):
                            logger.info(f"⚠️ 索引已存在，跳过")
                        else:
                            logger.error(f"❌ 索引创建失败: {e}")
                
                await db.commit()
                logger.info("🎉 用户表结构更新完成！")
                return True
                
            except Exception as e:
                logger.error(f"❌ 用户表更新失败: {e}")
                await db.rollback()
                return False
                
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False
    finally:
        await db_manager.close()

if __name__ == "__main__":
    success = asyncio.run(update_users_table())
    sys.exit(0 if success else 1) 