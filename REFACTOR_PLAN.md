# 《千亿像素城市寻宝》游戏改造计划

## 📋 基于PRD的系统改造方案

### 🎯 改造目标
将现有游戏完全按照PRD文档重新设计，删除不相关功能，实现完整的文化寻宝游戏体验。

---

## 🗂️ 一、后端模型改造

### ✅ **保留的模型文件**
1. **`user.py`** - 用户系统核心
2. **`game.py`** - 游戏会话管理  
3. **`artifact.py`** - 文物收集系统
4. **`advertisement.py`** - 广告和任务系统
5. **`ammo.py`** - 道具系统
6. **`hotspot_reward.py`** - 奖励掉落系统
7. **`config.py`** - 配置管理

### ❌ **删除的模型文件**
1. **`admin.py`** - 管理员系统（PRD未提及）

### ⚠️ **重构的模型文件**
1. **`passive_income.py`** - 简化为基础离线收益

### 🆕 **新增模型文件**
1. **`boss_health.py`** - BOSS血量系统
2. **`city_unlock.py`** - 城市解锁系统
3. **`cultural_quiz.py`** - 文化问答系统

---

## 🎮 二、核心游戏机制实现

### 🔸 **1. 寻宝游戏三大关卡**

#### **1.1 抓捕小偷关卡**
- 体力消耗：1点/次
- 基础经验：12点/次
- BOSS血量影响：-2%/个
- 宝箱掉落率：6%（铜宝箱）
- 特殊机制：10%概率遇到叛变小偷，恢复10点体力

#### **1.2 清理垃圾关卡**
- 体力消耗：1点/次
- 基础经验：8点/次
- BOSS血量影响：-1%/个
- 宝箱掉落率：3%（铜宝箱）

#### **1.3 保护遗迹关卡**
- 体力消耗：5点/次（古迹问答）
- 基础经验：35点/次
- BOSS血量影响：-10%/题
- 宝箱掉落率：15%（银宝箱）
- 额外奖励：文化图鉴收集

### 🔸 **2. BOSS血量系统**

#### **BOSS阶段设计**
| 血量范围 | BOSS状态 | 对话内容 | 玩家目标 |
|---------|---------|---------|---------|
| 100%-80% | 傲慢阶段 | "你这个失败者，永远无法阻止我们！" | 开始收集 |
| 80%-50% | 愤怒阶段 | "可恶！我要派出更多的手下！" | 继续削弱 |
| 50%-20% | 求饶阶段 | "求你手下留情，我们会改邪归正的！" | 最后冲刺 |
| 20%-0% | 失败阶段 | "算你厉害，我们先撤，但我还会回来的！" | 获得奖励 |

#### **血量关联机制**
- 抓捕小偷：每个小偷-2%血量
- 清理垃圾：每个垃圾-1%血量  
- 古迹问答：每题正确-10%血量
- 血量清零：关卡完成，城市恢复，金宝箱必掉+200经验

### 🔸 **3. 体力值系统**

#### **体力机制设计**
- **最大体力值**：120点
- **自然恢复**：1点/3分钟（6小时完全恢复）
- **广告恢复**：30点/次（每小时限3次）
- **经验效率**：体力<30时，经验获取减少25%

#### **特殊恢复方式**
- 叛变小偷：10%概率，恢复10点体力
- 升级奖励：升级时完全恢复体力
- 体力药水：道具恢复20点体力

---

## 📈 三、经验值与成长系统

### 🔸 **守望者等级系统**

| 等级 | 升级所需经验 | 累计经验 | 外观变化 | 被动收益 | 解锁内容 |
|------|-------------|---------|---------|---------|---------|
| 1→2级 | 100点 | 100点 | 初级守卫 | 2经验/分钟 | 基础功能 |
| 2→3级 | 150点 | 250点 | 初级守卫 | 3经验/分钟 | 基础任务 |
| 3→4级 | 250点 | 500点 | 初级守卫 | 5经验/分钟 | 更多任务 |
| 4→5级 | 500点 | 1000点 | 中级守护者 | 8经验/分钟 | 高级任务 |
| 5→6级 | 1000点 | 2000点 | 中级守护者 | 12经验/分钟 | 特殊道具 |
| 6→7级 | 2000点 | 4000点 | 中级守护者 | 15经验/分钟 | 装备系统 |
| 7→8级 | 4000点 | 8000点 | 高级执法者 | 20经验/分钟 | 技能系统 |
| 8→9级 | 7000点 | 15000点 | 高级执法者 | 30经验/分钟 | 专属特效 |
| 9→10级 | 10000点 | 25000点 | 高级执法者 | 35经验/分钟 | 竞技功能 |
| 10级+ | 15000点/级 | 递增 | 传奇守护者 | 40-60经验/分钟 | 全部权限 |

### 🔸 **经验值获取途径**

| 行为 | 基础经验值 | 体力消耗 | 经验/体力比 | 特殊加成 |
|------|-----------|---------|-------------|---------|
| 抓捕小偷 | 12点 | 1点 | 12:1 | 体力<30时减25% |
| 清理垃圾 | 8点 | 1点 | 8:1 | 体力<30时减25% |
| 古迹问答 | 35点 | 5点 | 7:1 | 广告可双倍 |
| 文化图鉴 | 25点 | 2点 | 12.5:1 | 广告可双倍 |
| 关卡通关 | 200点 | 0点 | 无限 | BOSS血量清零奖励 |
| 完成任务 | 50-800点 | 0点 | 无限 | 广告可双倍 |
| 被动收益 | 2-60点/分钟 | 0点 | 无限 | 广告可双倍 |

---

## 🎁 四、宝箱与道具系统

### 🔸 **宝箱掉落优化方案**

| 行为来源 | 掉落率 | 宝箱类型 | 免费奖励 | 广告翻倍奖励 |
|---------|-------|---------|---------|-------------|
| 抓捕小偷 | 6% | 铜宝箱 | 体力+5，道具×1 | 体力+10，道具×2 |
| 清理垃圾 | 3% | 铜宝箱 | 体力+5，道具×1 | 体力+10，道具×2 |
| 古迹问答 | 15% | 银宝箱 | 图鉴×1，体力+10 | 图鉴×2，体力+20 |
| 关卡通关 | 100% | 金宝箱 | 稀有图鉴×1，道具×1 | 稀有图鉴×2，道具×2 |

### 🔸 **道具系统价值表**

| 道具名称 | 功能效果 | 获得来源 | 使用限制 | 时间价值 |
|---------|---------|---------|---------|---------|
| **放大镜** | 高亮未发现目标3秒 | 铜/银宝箱 | 每关卡1次 | 节省10-30秒 |
| **雷达** | 显示最近目标方向指示 | 银/金宝箱 | 每关卡1次 | 节省30-60秒 |
| **体力药水** | 立即恢复20体力 | 所有宝箱 | 无限制 | 等价1小时等待 |

---

## 📝 五、任务系统设计

### 🔸 **每日任务**

| 任务类别 | 具体任务 | 目标数量 | 经验奖励 | 预估完成时间 |
|---------|---------|---------|---------|-------------|
| **抓捕任务** | 抓捕小偷 | 15/30/60个 | 50/120/300点 | 15/30/60分钟 |
| **清理任务** | 清除垃圾 | 20/40/80个 | 40/100/250点 | 20/40/80分钟 |
| **学习任务** | 文化学习 | 2/5/10个 | 80/200/500点 | 10/25/50分钟 |
| **通关任务** | 完美通关 | 1/3/5关 | 100/300/800点 | 30/90/150分钟 |

### 🔸 **任务状态管理**
- 任务进度实时更新
- 基础奖励 + 广告双倍选项
- 每日0点自动重置
- 未完成任务提醒机制

---

## 🏛️ 六、文化教育系统

### 🔸 **文化古迹系统**

| 古迹类型 | 内容描述 | 问答难度 | 教育价值 |
|---------|---------|---------|---------|
| **世界遗产** | 联合国教科文组织认定遗产 | 中等 | 国际文化认知 |
| **国家文物** | 各国重要文化古迹建筑 | 较难 | 民族文化了解 |
| **地方特色** | 地方特色文化景点 | 简单 | 地域文化认识 |

### 🔸 **文化图鉴系统**

| 图鉴类别 | 内容范围 | 获取方式 | 知识深度 |
|---------|---------|---------|---------|
| **非遗技艺** | 传统手工艺、技能 | 收集掉落 | 技艺传承 |
| **民俗文化** | 节庆、习俗、传说 | 收集掉落 | 文化传统 |
| **历史人物** | 重要历史文化人物 | 收集掉落 | 人文历史 |
| **特色美食** | 地方特色食物文化 | 收集掉落 | 饮食文化 |

---

## 📱 七、激励广告系统

### 🔸 **广告触发场景**

| 触发场景 | 广告类型 | 奖励内容 | 观看频次限制 |
|---------|---------|---------|-------------|
| 体力不足 | 激励视频 | 恢复30点体力 | 每小时3次 |
| 开启宝箱 | 激励视频 | 宝箱内容翻倍 | 不限制 |
| 问答奖励 | 激励视频 | 经验值翻倍 | 不限制 |
| 任务奖励 | 激励视频 | 任务经验翻倍 | 每个任务1次 |
| 被动收益 | 激励视频 | 离线收益翻倍 | 每次收集1次 |
| 升级加速 | 激励视频 | 当前经验+10% | 每日3次 |

---

## 🖥️ 八、前端界面改造

### 🔸 **主界面设计**
- **城市选择界面**：展示5个城市，星级进度显示
- **守望者头像**：等级和外观效果展示
- **关卡入口**：三个关卡按钮（抓小偷、清垃圾、保遗迹）
- **排行榜**：经验值排行和通关时间排行

### 🔸 **游戏界面设计**
- **全景视图**：千亿像素城市全景，支持缩放平移
- **状态栏**：体力值、经验值、当前进度
- **小地图**：玩家位置和未发现目标
- **道具栏**：放大镜、雷达等辅助道具

### 🔸 **问答界面设计**
- **场景展示**：被污染的文化遗迹图片
- **BOSS对话**：垃圾大王的嘲讽和对话
- **问题区域**：题目和选项，支持多选单选
- **结果反馈**：答案解释和奖励展示

---

## 📊 九、数值平衡设计

### 🔸 **核心设计原则**
- **目标游戏时长**：25-35分钟/日
- **体力完全恢复**：6小时
- **升级周期**：4-6天/级
- **每日经验获取**：1500-2400点

### 🔸 **防数值膨胀机制**
- 体力上限锁定：最高120点
- 被动收益上限：60经验/分钟
- 每日任务限制：防止经验无上限获取
- 动态平衡调整：根据数据自动优化

---

## ⚡ 十、实施优先级

### **Phase 1: 核心功能重构（高优先级）**
1. ✅ 删除admin.py模型
2. ✅ 重构passive_income.py模型
3. ✅ 新增boss_health.py模型
4. ✅ 实现BOSS血量系统API
5. ✅ 优化体力值系统
6. ✅ 完善经验值计算

### **Phase 2: 游戏机制完善（高优先级）**
1. ✅ 实现三大关卡机制
2. ✅ 完善宝箱掉落系统
3. ✅ 实现道具使用逻辑
4. ✅ 优化任务系统

### **Phase 3: 文化教育系统（中优先级）**
1. ✅ 新增cultural_quiz.py模型
2. ✅ 实现古迹问答API
3. ✅ 完善图鉴收集系统
4. ✅ 添加文化内容数据

### **Phase 4: 界面优化（中优先级）**
1. ✅ 简化认证系统
2. ✅ 重构城市选择界面
3. ✅ 优化状态栏显示
4. ✅ 完善BOSS对话界面

### **Phase 5: 系统优化（低优先级）**
1. ✅ 清理废弃代码
2. ✅ 性能优化
3. ✅ 数据监控
4. ✅ 测试完善

---

## 🎯 预期成果

通过此次改造，将实现：

1. **完全符合PRD的游戏体验**
2. **简化清晰的代码架构**
3. **优化的数值平衡系统**
4. **完整的文化教育功能**
5. **可持续的商业模式**

改造完成后，游戏将成为一款真正的"千亿像素城市寻宝"文化教育游戏，为用户提供寓教于乐的文化学习体验。