#!/usr/bin/env python3
"""
运行PRD系统数据库迁移脚本
"""
import mysql.connector
from pathlib import Path


def main():
    """主函数"""
    print("《千亿像素城市寻宝》PRD系统数据库迁移")
    print("=" * 50)
    
    # 检查迁移文件是否存在
    migration_file = Path("database_migrations/006_prd_systems_implementation.sql")
    if not migration_file.exists():
        print(f"错误: 迁移文件不存在 {migration_file}")
        print("请确保在项目根目录下运行此脚本")
        return
    
    # 运行迁移
    try:
        # 数据库连接配置（请根据实际情况修改）
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root', 
            'password': 'your_password',  # 请修改为实际密码
            'database': 'universe_vr_game',
            'charset': 'utf8mb4'
        }
        
        print("正在连接MySQL数据库...")
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # 读取迁移脚本
        print(f"正在读取迁移脚本: {migration_file}")
        with open(migration_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL命令（按分号分割，过滤空命令）
        sql_commands = [cmd.strip() for cmd in sql_content.split(';') if cmd.strip()]
        
        print(f"找到 {len(sql_commands)} 个SQL命令，开始执行...")
        
        successful_commands = 0
        for i, command in enumerate(sql_commands, 1):
            try:
                # 跳过注释和空命令
                if command.startswith('--') or command.startswith('/*') or not command or len(command) < 5:
                    continue
                
                # 处理DELIMITER命令（MySQL客户端特有，需要特殊处理）
                if 'DELIMITER' in command.upper():
                    print(f"跳过DELIMITER命令: {i}")
                    continue
                
                print(f"执行命令 {i}: {command[:100]}...")
                cursor.execute(command)
                successful_commands += 1
                
            except mysql.connector.Error as e:
                # 对于某些可预期的错误（如表已存在），只是警告
                if "already exists" in str(e) or "Duplicate entry" in str(e):
                    print(f"警告 - 命令 {i}: {e}")
                    continue
                else:
                    print(f"错误 - 命令 {i}: {e}")
                    print(f"失败的SQL: {command[:200]}...")
                    # 继续执行下一个命令而不是中断
                    continue
        
        # 提交事务
        connection.commit()
        print(f"迁移完成！成功执行 {successful_commands} 个命令")
        
        print("\n✓ 数据库迁移完成！")
        print("新的PRD系统表已创建，可以开始测试前后端集成")
        
    except Exception as e:
        print(f"执行迁移时发生错误: {e}")
        print("请检查数据库配置和连接")
        print("\n✗ 数据库迁移失败")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()
        print("数据库连接已关闭")

if __name__ == "__main__":
    main()